'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import {
  CalculatorIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  CalendarIcon,
  TrendingUpIcon,
  LightBulbIcon,
  InformationCircleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface CalculationResult {
  totalInvestment: number;
  expectedReturns: number;
  netProfit: number;
  returnRate: number;
  monthlyReturn: number;
  breakEvenMonth: number;
}

export default function CalculatorPage() {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    jamiyaType: 'friends',
    sharesCount: 1,
    pricePerShare: 2500,
    duration: 12,
    expectedReturnRate: 15,
    riskLevel: 'medium'
  });
  const [result, setResult] = useState<CalculationResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const jamiyaTypes = [
    { value: 'friends', label: 'جمعية الأصدقاء', pricePerShare: 2500, avgReturn: 15 },
    { value: 'family', label: 'جمعية العائلة', pricePerShare: 1500, avgReturn: 12 },
    { value: 'neighborhood', label: 'جمعية الأحياء', pricePerShare: 5000, avgReturn: 18 },
    { value: 'professional', label: 'جمعية المهنيين', pricePerShare: 10000, avgReturn: 20 }
  ];

  const riskLevels = [
    { value: 'low', label: 'منخفض', multiplier: 0.8, color: 'green' },
    { value: 'medium', label: 'متوسط', multiplier: 1.0, color: 'yellow' },
    { value: 'high', label: 'عالي', multiplier: 1.3, color: 'red' }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      
      // Update price per share when jamiya type changes
      if (field === 'jamiyaType') {
        const selectedJamiya = jamiyaTypes.find(j => j.value === value);
        if (selectedJamiya) {
          newData.pricePerShare = selectedJamiya.pricePerShare;
          newData.expectedReturnRate = selectedJamiya.avgReturn;
        }
      }
      
      return newData;
    });
  };

  const calculateReturns = () => {
    setIsCalculating(true);
    
    // Simulate calculation delay
    setTimeout(() => {
      const totalInvestment = formData.sharesCount * formData.pricePerShare * formData.duration;
      const riskMultiplier = riskLevels.find(r => r.value === formData.riskLevel)?.multiplier || 1;
      const adjustedReturnRate = formData.expectedReturnRate * riskMultiplier;
      
      // Calculate compound returns
      const monthlyRate = adjustedReturnRate / 100 / 12;
      const compoundFactor = Math.pow(1 + monthlyRate, formData.duration);
      const expectedReturns = totalInvestment * compoundFactor;
      const netProfit = expectedReturns - totalInvestment;
      const returnRate = (netProfit / totalInvestment) * 100;
      const monthlyReturn = netProfit / formData.duration;
      const breakEvenMonth = Math.ceil(totalInvestment / (formData.sharesCount * formData.pricePerShare * (adjustedReturnRate / 100)));

      setResult({
        totalInvestment,
        expectedReturns,
        netProfit,
        returnRate,
        monthlyReturn,
        breakEvenMonth
      });
      
      setIsCalculating(false);
    }, 1500);
  };

  const resetCalculator = () => {
    setFormData({
      jamiyaType: 'friends',
      sharesCount: 1,
      pricePerShare: 2500,
      duration: 12,
      expectedReturnRate: 15,
      riskLevel: 'medium'
    });
    setResult(null);
  };

  return (
    <ProtectedRoute requiredPermissions={['calculator']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">حاسبة الاستثمار الذكية</h1>
                <p className="text-gray-600 mt-1">
                  احسب العوائد المتوقعة لاستثماراتك في الجمعيات المالية
                </p>
              </div>
              <div className="flex items-center space-x-4 space-x-reverse">
                <button
                  onClick={resetCalculator}
                  className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  <ArrowPathIcon className="h-5 w-5 ml-2" />
                  إعادة تعيين
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Calculator Form */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <div className="flex items-center mb-6">
                <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CalculatorIcon className="h-6 w-6 text-blue-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mr-3">معلومات الاستثمار</h2>
              </div>

              <div className="space-y-6">
                {/* Jamiya Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الجمعية
                  </label>
                  <select
                    value={formData.jamiyaType}
                    onChange={(e) => handleInputChange('jamiyaType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {jamiyaTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label} - {formatCurrency(type.pricePerShare)} للسهم
                      </option>
                    ))}
                  </select>
                </div>

                {/* Shares Count */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    عدد الأسهم
                  </label>
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <button
                      type="button"
                      onClick={() => handleInputChange('sharesCount', Math.max(1, formData.sharesCount - 1))}
                      className="w-10 h-10 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center"
                    >
                      <span className="text-lg font-bold">-</span>
                    </button>
                    
                    <input
                      type="number"
                      value={formData.sharesCount}
                      onChange={(e) => handleInputChange('sharesCount', Math.max(1, Number(e.target.value)))}
                      className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-center"
                      min="1"
                      max="50"
                    />
                    
                    <button
                      type="button"
                      onClick={() => handleInputChange('sharesCount', Math.min(50, formData.sharesCount + 1))}
                      className="w-10 h-10 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center"
                    >
                      <span className="text-lg font-bold">+</span>
                    </button>
                    
                    <span className="text-sm text-gray-600">سهم</span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    المبلغ الشهري: {formatCurrency(formData.sharesCount * formData.pricePerShare)}
                  </p>
                </div>

                {/* Duration */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    مدة الاستثمار (بالأشهر)
                  </label>
                  <input
                    type="range"
                    min="6"
                    max="60"
                    value={formData.duration}
                    onChange={(e) => handleInputChange('duration', Number(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>6 أشهر</span>
                    <span className="font-medium text-blue-600">{formData.duration} شهر</span>
                    <span>5 سنوات</span>
                  </div>
                </div>

                {/* Expected Return Rate */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    معدل العائد المتوقع (%)
                  </label>
                  <input
                    type="number"
                    value={formData.expectedReturnRate}
                    onChange={(e) => handleInputChange('expectedReturnRate', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="5"
                    max="30"
                    step="0.5"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    المعدل المتوسط للجمعية المختارة: {jamiyaTypes.find(j => j.value === formData.jamiyaType)?.avgReturn}%
                  </p>
                </div>

                {/* Risk Level */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    مستوى المخاطرة
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {riskLevels.map((level) => (
                      <button
                        key={level.value}
                        onClick={() => handleInputChange('riskLevel', level.value)}
                        className={`
                          p-3 rounded-lg border-2 text-center transition-all
                          ${formData.riskLevel === level.value
                            ? `border-${level.color}-500 bg-${level.color}-50`
                            : 'border-gray-200 bg-white hover:border-gray-300'
                          }
                        `}
                      >
                        <span className="text-sm font-medium">{level.label}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Calculate Button */}
                <button
                  onClick={calculateReturns}
                  disabled={isCalculating}
                  className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isCalculating ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                      جاري الحساب...
                    </>
                  ) : (
                    <>
                      <CalculatorIcon className="h-5 w-5 ml-2" />
                      احسب العوائد المتوقعة
                    </>
                  )}
                </button>
              </div>
            </motion.div>

            {/* Results Panel */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-6"
            >
              {result ? (
                <>
                  {/* Results Summary */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center mb-6">
                      <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <ChartBarIcon className="h-6 w-6 text-green-600" />
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900 mr-3">نتائج الحساب</h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <CurrencyDollarIcon className="h-5 w-5 text-blue-600 ml-2" />
                          <span className="text-sm font-medium text-blue-900">إجمالي الاستثمار</span>
                        </div>
                        <p className="text-2xl font-bold text-blue-600">
                          {formatCurrency(result.totalInvestment)}
                        </p>
                      </div>

                      <div className="p-4 bg-green-50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <TrendingUpIcon className="h-5 w-5 text-green-600 ml-2" />
                          <span className="text-sm font-medium text-green-900">العوائد المتوقعة</span>
                        </div>
                        <p className="text-2xl font-bold text-green-600">
                          {formatCurrency(result.expectedReturns)}
                        </p>
                      </div>

                      <div className="p-4 bg-purple-50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <ChartBarIcon className="h-5 w-5 text-purple-600 ml-2" />
                          <span className="text-sm font-medium text-purple-900">صافي الربح</span>
                        </div>
                        <p className="text-2xl font-bold text-purple-600">
                          {formatCurrency(result.netProfit)}
                        </p>
                      </div>

                      <div className="p-4 bg-orange-50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <TrendingUpIcon className="h-5 w-5 text-orange-600 ml-2" />
                          <span className="text-sm font-medium text-orange-900">نسبة العائد</span>
                        </div>
                        <p className="text-2xl font-bold text-orange-600">
                          %{result.returnRate.toFixed(1)}
                        </p>
                      </div>
                    </div>

                    <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <span className="text-sm text-gray-600">العائد الشهري المتوسط:</span>
                        <span className="mr-2 font-semibold text-gray-900">
                          {formatCurrency(result.monthlyReturn)}
                        </span>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <span className="text-sm text-gray-600">نقطة التعادل:</span>
                        <span className="mr-2 font-semibold text-gray-900">
                          {result.breakEvenMonth} شهر
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Investment Breakdown */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل الاستثمار</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-600">عدد الأسهم:</span>
                        <span className="font-medium">{formData.sharesCount} سهم</span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-600">سعر السهم:</span>
                        <span className="font-medium">{formatCurrency(formData.pricePerShare)}</span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-600">المبلغ الشهري:</span>
                        <span className="font-medium">{formatCurrency(formData.sharesCount * formData.pricePerShare)}</span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-600">مدة الاستثمار:</span>
                        <span className="font-medium">{formData.duration} شهر</span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-600">معدل العائد:</span>
                        <span className="font-medium">%{formData.expectedReturnRate}</span>
                      </div>
                      <div className="flex justify-between items-center py-2">
                        <span className="text-gray-600">مستوى المخاطرة:</span>
                        <span className="font-medium">
                          {riskLevels.find(r => r.value === formData.riskLevel)?.label}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Investment Tips */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center mb-4">
                      <LightBulbIcon className="h-6 w-6 text-yellow-600 ml-2" />
                      <h3 className="text-lg font-semibold text-gray-900">نصائح استثمارية</h3>
                    </div>
                    <div className="space-y-3">
                      {result.returnRate > 20 && (
                        <div className="p-3 bg-green-50 rounded-lg">
                          <p className="text-sm text-green-800">
                            🎉 عائد ممتاز! هذا الاستثمار يحقق عوائد عالية جداً.
                          </p>
                        </div>
                      )}
                      {result.returnRate >= 15 && result.returnRate <= 20 && (
                        <div className="p-3 bg-blue-50 rounded-lg">
                          <p className="text-sm text-blue-800">
                            👍 عائد جيد! هذا الاستثمار يحقق عوائد مناسبة.
                          </p>
                        </div>
                      )}
                      {result.returnRate < 15 && (
                        <div className="p-3 bg-yellow-50 rounded-lg">
                          <p className="text-sm text-yellow-800">
                            ⚠️ عائد منخفض. فكر في زيادة عدد الأسهم أو اختيار جمعية أخرى.
                          </p>
                        </div>
                      )}
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm text-gray-700">
                          💡 نصيحة: تنويع الاستثمار في عدة جمعيات يقلل المخاطر ويزيد الفرص.
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
                  <div className="text-center">
                    <CalculatorIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">احسب عوائدك المتوقعة</h3>
                    <p className="text-gray-600 mb-6">
                      أدخل معلومات الاستثمار في النموذج واضغط على "احسب العوائد المتوقعة" لرؤية النتائج
                    </p>
                    <div className="flex items-center justify-center text-sm text-gray-500">
                      <InformationCircleIcon className="h-5 w-5 ml-2" />
                      <span>جميع الحسابات تقديرية وقد تختلف النتائج الفعلية</span>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
