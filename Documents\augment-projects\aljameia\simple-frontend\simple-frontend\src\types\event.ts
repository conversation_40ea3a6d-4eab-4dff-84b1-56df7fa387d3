// Event Management Types and Interfaces

export interface Event {
  id: string;
  title: string;
  description: string;
  type: EventType;
  category: EventCategory;
  status: EventStatus;
  priority: EventPriority;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  timezone: string;
  isAllDay: boolean;
  isRecurring: boolean;
  recurrenceRule?: RecurrenceRule;
  location: EventLocation;
  organizer: EventOrganizer;
  speakers: EventSpeaker[];
  attendees: EventAttendee[];
  maxAttendees?: number;
  registrationRequired: boolean;
  registrationDeadline?: string;
  registrationFee?: number;
  currency: string;
  agenda: AgendaItem[];
  materials: EventMaterial[];
  requirements: string[];
  tags: string[];
  visibility: EventVisibility;
  permissions: EventPermissions;
  notifications: EventNotification[];
  feedback: EventFeedback[];
  photos: string[];
  videos: string[];
  documents: string[];
  createdBy: string;
  createdByName: string;
  createdAt: string;
  lastModified: string;
  lastModifiedBy: string;
  lastModifiedByName: string;
  publishedAt?: string;
  cancelledAt?: string;
  cancellationReason?: string;
}

export interface EventLocation {
  type: LocationType;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  room?: string;
  floor?: string;
  building?: string;
  capacity?: number;
  facilities: string[];
  virtualLink?: string;
  virtualPlatform?: string;
  virtualPassword?: string;
  virtualInstructions?: string;
}

export interface EventOrganizer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  organization?: string;
  title?: string;
  bio?: string;
  avatar?: string;
}

export interface EventSpeaker {
  id: string;
  name: string;
  title: string;
  organization?: string;
  bio: string;
  avatar?: string;
  email?: string;
  phone?: string;
  socialMedia?: {
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
  topics: string[];
  sessionTitle?: string;
  sessionDescription?: string;
  sessionStartTime?: string;
  sessionEndTime?: string;
}

export interface EventAttendee {
  id: string;
  userId?: string;
  name: string;
  email: string;
  phone?: string;
  organization?: string;
  title?: string;
  registrationDate: string;
  attendanceStatus: AttendanceStatus;
  checkInTime?: string;
  checkOutTime?: string;
  feedback?: AttendeeEventFeedback;
  specialRequirements?: string[];
  dietaryRestrictions?: string[];
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface AgendaItem {
  id: string;
  title: string;
  description?: string;
  type: AgendaItemType;
  startTime: string;
  endTime: string;
  duration: number;
  speakerId?: string;
  speakerName?: string;
  location?: string;
  materials?: string[];
  isBreak: boolean;
  isOptional: boolean;
  maxAttendees?: number;
  registeredAttendees: string[];
}

export interface EventMaterial {
  id: string;
  name: string;
  type: MaterialType;
  url: string;
  description?: string;
  isPublic: boolean;
  availableFrom?: string;
  availableUntil?: string;
  downloadCount: number;
  uploadedBy: string;
  uploadedAt: string;
}

export interface RecurrenceRule {
  frequency: RecurrenceFrequency;
  interval: number;
  daysOfWeek?: number[];
  dayOfMonth?: number;
  weekOfMonth?: number;
  monthOfYear?: number;
  endDate?: string;
  occurrences?: number;
  exceptions?: string[];
}

export interface EventNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  scheduledFor: string;
  sentAt?: string;
  recipients: NotificationRecipient[];
  channels: NotificationChannel[];
  status: NotificationStatus;
}

export interface NotificationRecipient {
  id: string;
  type: 'attendee' | 'speaker' | 'organizer' | 'all';
  userId?: string;
  email?: string;
  phone?: string;
}

export interface EventFeedback {
  id: string;
  attendeeId: string;
  attendeeName: string;
  rating: number;
  comments?: string;
  categories: FeedbackCategory[];
  submittedAt: string;
  isAnonymous: boolean;
}

export interface FeedbackCategory {
  category: string;
  rating: number;
  comments?: string;
}

export interface AttendeeEventFeedback {
  overallRating: number;
  contentRating: number;
  organizationRating: number;
  venueRating: number;
  speakerRating: number;
  comments: string;
  wouldRecommend: boolean;
  suggestions: string;
  submittedAt: string;
}

export interface EventPermissions {
  isPublic: boolean;
  allowedUsers: string[];
  allowedRoles: string[];
  allowedGroups: string[];
  permissions: {
    view: boolean;
    register: boolean;
    edit: boolean;
    delete: boolean;
    manage: boolean;
    moderate: boolean;
  };
}

// Enums
export type EventType = 
  | 'meeting'
  | 'conference'
  | 'workshop'
  | 'seminar'
  | 'webinar'
  | 'training'
  | 'social'
  | 'networking'
  | 'ceremony'
  | 'other';

export type EventCategory = 
  | 'board_meeting'
  | 'general_assembly'
  | 'committee_meeting'
  | 'training_session'
  | 'professional_development'
  | 'social_event'
  | 'fundraising'
  | 'community_outreach'
  | 'awards_ceremony'
  | 'annual_conference'
  | 'other';

export type EventStatus = 
  | 'draft'
  | 'published'
  | 'registration_open'
  | 'registration_closed'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'postponed';

export type EventPriority = 'low' | 'medium' | 'high' | 'urgent';

export type LocationType = 'physical' | 'virtual' | 'hybrid';

export type AttendanceStatus = 
  | 'registered'
  | 'confirmed'
  | 'attended'
  | 'no_show'
  | 'cancelled'
  | 'waitlisted';

export type AgendaItemType = 
  | 'presentation'
  | 'discussion'
  | 'workshop'
  | 'break'
  | 'networking'
  | 'qa_session'
  | 'panel'
  | 'keynote'
  | 'other';

export type MaterialType = 
  | 'presentation'
  | 'document'
  | 'video'
  | 'audio'
  | 'image'
  | 'link'
  | 'other';

export type RecurrenceFrequency = 
  | 'daily'
  | 'weekly'
  | 'monthly'
  | 'yearly';

export type NotificationType = 
  | 'registration_confirmation'
  | 'reminder'
  | 'update'
  | 'cancellation'
  | 'material_available'
  | 'feedback_request'
  | 'follow_up';

export type NotificationChannel = 'email' | 'sms' | 'push' | 'in_app';

export type NotificationStatus = 'scheduled' | 'sent' | 'failed' | 'cancelled';

export type EventVisibility = 'public' | 'private' | 'members_only' | 'invite_only';

// Search and Filter Types
export interface EventFilters {
  search?: string;
  type?: EventType[];
  category?: EventCategory[];
  status?: EventStatus[];
  dateFrom?: string;
  dateTo?: string;
  location?: string;
  organizer?: string;
  tags?: string[];
  hasAvailableSpots?: boolean;
  requiresRegistration?: boolean;
  isFree?: boolean;
}

export interface EventSortOptions {
  field: 'startDate' | 'title' | 'registrationCount' | 'createdAt';
  direction: 'asc' | 'desc';
}

// Form Types
export interface CreateEventForm {
  title: string;
  description: string;
  type: EventType;
  category: EventCategory;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  isAllDay: boolean;
  location: Partial<EventLocation>;
  maxAttendees?: number;
  registrationRequired: boolean;
  registrationDeadline?: string;
  registrationFee?: number;
  requirements: string[];
  tags: string[];
  visibility: EventVisibility;
  agenda: Partial<AgendaItem>[];
  speakers: Partial<EventSpeaker>[];
}

export interface RegisterEventForm {
  eventId: string;
  attendeeInfo: {
    name: string;
    email: string;
    phone?: string;
    organization?: string;
    title?: string;
    specialRequirements?: string[];
    dietaryRestrictions?: string[];
    emergencyContact?: {
      name: string;
      phone: string;
      relationship: string;
    };
  };
}

// Statistics Types
export interface EventStatistics {
  totalEvents: number;
  upcomingEvents: number;
  completedEvents: number;
  totalAttendees: number;
  averageAttendance: number;
  popularCategories: {
    category: EventCategory;
    count: number;
    percentage: number;
  }[];
  monthlyStats: {
    month: string;
    events: number;
    attendees: number;
  }[];
  recentEvents: Event[];
  topRatedEvents: Event[];
}

// API Response Types
export interface EventListResponse {
  events: Event[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface EventResponse {
  event: Event;
  success: boolean;
  message?: string;
}

export interface RegistrationResponse {
  registration: EventAttendee;
  success: boolean;
  message?: string;
}
