{"name": "aljameia-admin-dashboard", "version": "1.0.0", "description": "Admin Dashboard for Aljameia Financial Platform", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "docker:build": "docker build -t aljameia-admin .", "docker:run": "docker run -p 3001:3001 aljameia-admin"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.4", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "@mui/x-charts": "^6.18.2", "recharts": "^2.8.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "axios": "^1.6.2", "swr": "^2.2.4", "socket.io-client": "^4.7.4", "react-query": "^3.39.3", "zustand": "^4.4.7", "dayjs": "^1.11.10", "react-hot-toast": "^2.4.1", "react-dropzone": "^14.2.3", "react-beautiful-dnd": "^13.1.1", "framer-motion": "^10.16.16", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "react-intersection-observer": "^9.5.3", "react-helmet-async": "^2.0.4", "next-themes": "^0.2.1", "next-auth": "^4.24.5", "jose": "^5.1.3", "bcryptjs": "^2.4.3", "sharp": "^0.32.6", "file-saver": "^2.0.5", "xlsx": "^0.18.5", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "react-pdf": "^7.6.0", "qrcode": "^1.5.3", "react-qr-code": "^2.0.12"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.8", "@types/file-saver": "^2.0.7", "@types/qrcode": "^1.5.5", "typescript": "^5.3.2", "eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prettier": "^3.1.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "@storybook/addon-essentials": "^7.6.3", "@storybook/addon-interactions": "^7.6.3", "@storybook/addon-links": "^7.6.3", "@storybook/blocks": "^7.6.3", "@storybook/nextjs": "^7.6.3", "@storybook/react": "^7.6.3", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.3", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/pages/_app.tsx", "!src/pages/_document.tsx"]}}