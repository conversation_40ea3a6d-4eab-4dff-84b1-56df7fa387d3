import { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Container,
  useTheme,
  alpha,
} from '@mui/material';
import {
  TrendingUp,
  People,
  AccountBalance,
  Payment,
  Warning,
  CheckCircle,
  Schedule,
  Analytics,
} from '@mui/icons-material';

import { useAuth } from '@/hooks/useAuth';
import { useDashboardStats } from '@/hooks/useDashboardStats';
import StatsCard from '@/components/Dashboard/StatsCard';
import RecentTransactions from '@/components/Dashboard/RecentTransactions';
import JamiyaOverview from '@/components/Dashboard/JamiyaOverview';
import PaymentChart from '@/components/Dashboard/PaymentChart';
import UserGrowthChart from '@/components/Dashboard/UserGrowthChart';
import QuickActions from '@/components/Dashboard/QuickActions';
import SystemAlerts from '@/components/Dashboard/SystemAlerts';
import LoadingSpinner from '@/components/Common/LoadingSpinner';
import ErrorMessage from '@/components/Common/ErrorMessage';

const Dashboard: NextPage = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const { data: stats, isLoading, error, refetch } = useDashboardStats();

  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');

  useEffect(() => {
    // Refresh data every 5 minutes
    const interval = setInterval(() => {
      refetch();
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [refetch]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorMessage message="فشل في تحميل بيانات لوحة التحكم" onRetry={refetch} />;
  }

  const statsCards = [
    {
      title: 'إجمالي المستخدمين',
      value: stats?.totalUsers || 0,
      change: stats?.userGrowth || 0,
      icon: People,
      color: theme.palette.primary.main,
      trend: 'up' as const,
    },
    {
      title: 'الجمعيات النشطة',
      value: stats?.activeJamiyas || 0,
      change: stats?.jamiyaGrowth || 0,
      icon: AccountBalance,
      color: theme.palette.success.main,
      trend: 'up' as const,
    },
    {
      title: 'إجمالي المدفوعات',
      value: `${stats?.totalPayments?.toLocaleString('ar-SA') || 0} ريال`,
      change: stats?.paymentGrowth || 0,
      icon: Payment,
      color: theme.palette.info.main,
      trend: 'up' as const,
    },
    {
      title: 'معدل النجاح',
      value: `${stats?.successRate || 0}%`,
      change: stats?.successRateChange || 0,
      icon: CheckCircle,
      color: theme.palette.warning.main,
      trend: stats?.successRateChange >= 0 ? 'up' : 'down',
    },
  ];

  return (
    <>
      <Head>
        <title>لوحة التحكم - الجمعية</title>
        <meta name="description" content="لوحة تحكم منصة الجمعية المالية" />
      </Head>

      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* Welcome Section */}
        <Box mb={4}>
          <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
            مرحباً، {user?.name} 👋
          </Typography>
          <Typography variant="body1" color="text.secondary">
            إليك نظرة عامة على أداء المنصة اليوم
          </Typography>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} mb={4}>
          {statsCards.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <StatsCard {...stat} />
            </Grid>
          ))}
        </Grid>

        {/* Charts Section */}
        <Grid container spacing={3} mb={4}>
          {/* Payment Chart */}
          <Grid item xs={12} lg={8}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" component="h2">
                    إحصائيات المدفوعات
                  </Typography>
                  <Box>
                    {(['week', 'month', 'year'] as const).map((period) => (
                      <Typography
                        key={period}
                        component="button"
                        variant="body2"
                        sx={{
                          mx: 1,
                          px: 2,
                          py: 0.5,
                          border: 'none',
                          borderRadius: 1,
                          cursor: 'pointer',
                          backgroundColor: selectedPeriod === period 
                            ? alpha(theme.palette.primary.main, 0.1)
                            : 'transparent',
                          color: selectedPeriod === period 
                            ? theme.palette.primary.main 
                            : theme.palette.text.secondary,
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.primary.main, 0.05),
                          },
                        }}
                        onClick={() => setSelectedPeriod(period)}
                      >
                        {period === 'week' ? 'أسبوع' : period === 'month' ? 'شهر' : 'سنة'}
                      </Typography>
                    ))}
                  </Box>
                </Box>
                <PaymentChart period={selectedPeriod} />
              </CardContent>
            </Card>
          </Grid>

          {/* User Growth Chart */}
          <Grid item xs={12} lg={4}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" component="h2" mb={2}>
                  نمو المستخدمين
                </Typography>
                <UserGrowthChart />
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Content Section */}
        <Grid container spacing={3}>
          {/* Recent Transactions */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h2" mb={2}>
                  المعاملات الأخيرة
                </Typography>
                <RecentTransactions />
              </CardContent>
            </Card>
          </Grid>

          {/* Sidebar */}
          <Grid item xs={12} lg={4}>
            <Grid container spacing={3}>
              {/* Quick Actions */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" component="h2" mb={2}>
                      إجراءات سريعة
                    </Typography>
                    <QuickActions />
                  </CardContent>
                </Card>
              </Grid>

              {/* System Alerts */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" component="h2" mb={2}>
                      تنبيهات النظام
                    </Typography>
                    <SystemAlerts />
                  </CardContent>
                </Card>
              </Grid>

              {/* Jamiya Overview */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" component="h2" mb={2}>
                      نظرة عامة على الجمعيات
                    </Typography>
                    <JamiyaOverview />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        {/* Performance Metrics */}
        <Grid container spacing={3} mt={2}>
          <Grid item xs={12}>
            <Paper 
              sx={{ 
                p: 3, 
                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
              }}
            >
              <Box display="flex" alignItems="center" mb={2}>
                <Analytics sx={{ mr: 1, color: theme.palette.primary.main }} />
                <Typography variant="h6" component="h2">
                  مؤشرات الأداء الرئيسية
                </Typography>
              </Box>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary" fontWeight="bold">
                      {stats?.kpis?.averageTransactionTime || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط وقت المعاملة (ثانية)
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="success.main" fontWeight="bold">
                      {stats?.kpis?.systemUptime || 0}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      وقت تشغيل النظام
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="info.main" fontWeight="bold">
                      {stats?.kpis?.activeUsers || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      المستخدمون النشطون اليوم
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="warning.main" fontWeight="bold">
                      {stats?.kpis?.pendingApprovals || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      الموافقات المعلقة
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  );
};

export default Dashboard;
