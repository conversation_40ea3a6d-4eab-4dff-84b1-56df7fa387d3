{"logs": [{"outputFile": "com.aljameia.app-mergeDebugResources-104:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\cdc55fe2502a063bcee343c082439e6f\\transformed\\navigation-ui-2.7.6\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,117", "endOffsets": "163,281"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "18062,18175", "endColumns": "112,117", "endOffsets": "18170,18288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5081b8eacd451e5b41f2b566f41c579e\\transformed\\core-1.12.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "42,43,44,45,46,47,48,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3880,3975,4077,4179,4282,4386,4483,19171", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "3970,4072,4174,4277,4381,4478,4589,19267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b3e74a7425b14daa5ccd7c3c1ea8df2a\\transformed\\browser-1.4.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "74,114,115,116", "startColumns": "4,4,4,4", "startOffsets": "7463,11773,11877,11985", "endColumns": "99,103,107,104", "endOffsets": "7558,11872,11980,12085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f1a3dac0af0a85f165dc53a6762b4a81\\transformed\\play-services-basement-18.2.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6117", "endColumns": "163", "endOffsets": "6276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\523b9fad2b83d89c2b74860689d8fb91\\transformed\\biometric-1.1.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,260,384,513,646,782,901,1040,1135,1284,1426", "endColumns": "114,89,123,128,132,135,118,138,94,148,141,127", "endOffsets": "165,255,379,508,641,777,896,1035,1130,1279,1421,1549"}, "to": {"startLines": "73,107,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7348,11178,12090,12214,12343,12476,12612,12731,12870,12965,13114,13256", "endColumns": "114,89,123,128,132,135,118,138,94,148,141,127", "endOffsets": "7458,11263,12209,12338,12471,12607,12726,12865,12960,13109,13251,13379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\21cc0a6be925d3c2a0c747bb469e8733\\transformed\\appcompat-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,18936", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,19013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d2a56f6da0e2e4ab4da1b326f38c566a\\transformed\\material-1.11.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1038,1133,1203,1266,1373,1438,1505,1566,1633,1695,1749,1863,1922,1983,2037,2112,2238,2326,2416,2558,2630,2703,2840,2929,3010,3067,3123,3189,3260,3337,3423,3503,3575,3651,3732,3802,3902,3989,4061,4152,4245,4319,4394,4486,4538,4620,4686,4770,4856,4918,4982,5045,5114,5218,5322,5416,5516,5577,5637", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,78,93,87,91,111,81,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,89,141,71,72,136,88,80,56,55,65,70,76,85,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83", "endOffsets": "268,346,422,501,595,683,775,887,969,1033,1128,1198,1261,1368,1433,1500,1561,1628,1690,1744,1858,1917,1978,2032,2107,2233,2321,2411,2553,2625,2698,2835,2924,3005,3062,3118,3184,3255,3332,3418,3498,3570,3646,3727,3797,3897,3984,4056,4147,4240,4314,4389,4481,4533,4615,4681,4765,4851,4913,4977,5040,5109,5213,5317,5411,5511,5572,5632,5716"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3465,3543,3619,3698,3792,4594,4686,4798,11539,11678,13384,13628,13691,13798,13863,13930,13991,14058,14120,14174,14288,14347,14408,14462,14537,14663,14751,14841,14983,15055,15128,15265,15354,15435,15492,15548,15614,15685,15762,15848,15928,16000,16076,16157,16227,16327,16414,16486,16577,16670,16744,16819,16911,16963,17045,17111,17195,17281,17343,17407,17470,17539,17643,17747,17841,17941,18002,18602", "endLines": "5,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "endColumns": "12,77,75,78,93,87,91,111,81,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,89,141,71,72,136,88,80,56,55,65,70,76,85,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83", "endOffsets": "318,3538,3614,3693,3787,3875,4681,4793,4875,11598,11768,13449,13686,13793,13858,13925,13986,14053,14115,14169,14283,14342,14403,14457,14532,14658,14746,14836,14978,15050,15123,15260,15349,15430,15487,15543,15609,15680,15757,15843,15923,15995,16071,16152,16222,16322,16409,16481,16572,16665,16739,16814,16906,16958,17040,17106,17190,17276,17338,17402,17465,17534,17638,17742,17836,17936,17997,18057,18681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c514f303830aa824ccabc1313adde6e2\\transformed\\material3-1.1.2\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,283,394,506,581,678,793,927,1048,1169,1249,1343,1433,1526,1638,1758,1862,2000,2136,2264,2419,2538,2649,2765,2877,2964,3058,3177,3308,3408,3515,3617,3751,3892,3996,4091,4173,4248,4330,4410,4510,4595,4676,4773,4873,4966,5058,5141,5241,5336,5429,5562,5648,5755", "endColumns": "113,113,110,111,74,96,114,133,120,120,79,93,89,92,111,119,103,137,135,127,154,118,110,115,111,86,93,118,130,99,106,101,133,140,103,94,81,74,81,79,99,84,80,96,99,92,91,82,99,94,92,132,85,106,96", "endOffsets": "164,278,389,501,576,673,788,922,1043,1164,1244,1338,1428,1521,1633,1753,1857,1995,2131,2259,2414,2533,2644,2760,2872,2959,3053,3172,3303,3403,3510,3612,3746,3887,3991,4086,4168,4243,4325,4405,4505,4590,4671,4768,4868,4961,5053,5136,5236,5331,5424,5557,5643,5750,5847"}, "to": {"startLines": "33,34,35,36,54,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,109,112,195,198,200,204,205,206,207,208,209,210,211,212,213,214,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3014,3128,3242,3353,5056,7563,7660,7775,7909,8030,8151,8231,8325,8415,8508,8620,8740,8844,8982,9118,9246,9401,9520,9631,9747,9859,9946,10040,10159,10290,10390,10497,10599,10733,10874,10978,11370,11603,18854,19091,19272,19636,19721,19802,19899,19999,20092,20184,20267,20367,20462,20555,20688,20774,20881", "endColumns": "113,113,110,111,74,96,114,133,120,120,79,93,89,92,111,119,103,137,135,127,154,118,110,115,111,86,93,118,130,99,106,101,133,140,103,94,81,74,81,79,99,84,80,96,99,92,91,82,99,94,92,132,85,106,96", "endOffsets": "3123,3237,3348,3460,5126,7655,7770,7904,8025,8146,8226,8320,8410,8503,8615,8735,8839,8977,9113,9241,9396,9515,9626,9742,9854,9941,10035,10154,10285,10385,10492,10594,10728,10869,10973,11068,11447,11673,18931,19166,19367,19716,19797,19894,19994,20087,20179,20262,20362,20457,20550,20683,20769,20876,20973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\85fae80ff7869a8da6251422548fb462\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,990,1058,1141,1226,1299,1375,1445", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,985,1053,1136,1221,1294,1370,1440,1558"}, "to": {"startLines": "52,53,106,108,110,128,129,188,189,190,191,193,194,197,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4880,4973,11073,11268,11452,13454,13535,18293,18383,18465,18534,18686,18769,19018,19372,19448,19518", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "4968,5051,11173,11365,11534,13530,13623,18378,18460,18529,18597,18764,18849,19086,19443,19513,19631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1c0b3a48f4d4c74d31971bddddc3d282\\transformed\\play-services-base-18.1.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5131,5234,5387,5513,5619,5759,5885,6008,6281,6446,6552,6709,6838,6991,7148,7211,7270", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "5229,5382,5508,5614,5754,5880,6003,6112,6441,6547,6704,6833,6986,7143,7206,7265,7343"}}]}]}