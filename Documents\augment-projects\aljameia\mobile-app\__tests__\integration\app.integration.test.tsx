import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { AuthProvider } from '../../contexts/AuthContext';
import { AppProvider } from '../../contexts/AppContext';
import LoginScreen from '../../app/auth/login';
import HomeScreen from '../../app/(tabs)/index';
import { authService } from '../../services/authService';
import { reportService } from '../../services/reportService';
import { biometricService } from '../../services/biometricService';

// Mock all services
jest.mock('../../services/authService');
jest.mock('../../services/reportService');
jest.mock('../../services/biometricService');
jest.mock('../../services/cacheService');
jest.mock('../../services/notificationService');

const mockAuthService = authService as jest.Mocked<typeof authService>;
const mockReportService = reportService as jest.Mocked<typeof reportService>;
const mockBiometricService = biometricService as jest.Mocked<typeof biometricService>;

// Test wrapper with all providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider>
    <AppProvider>
      {children}
    </AppProvider>
  </AuthProvider>
);

describe('App Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    mockBiometricService.initialize.mockResolvedValue();
    mockBiometricService.isBiometricEnabled.mockReturnValue(false);
    mockAuthService.isAuthenticated.mockResolvedValue(false);
    mockAuthService.getStoredUser.mockResolvedValue(null);
  });

  describe('Authentication Flow', () => {
    it('should complete full login flow successfully', async () => {
      const mockUser = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      const mockAuthResponse = {
        user: mockUser,
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
      };

      mockAuthService.login.mockResolvedValue(mockAuthResponse);

      const { getByPlaceholderText, getByText } = render(
        <TestWrapper>
          <LoginScreen />
        </TestWrapper>
      );

      // Fill in login form
      const emailInput = getByPlaceholderText('أدخل بريدك الإلكتروني');
      const passwordInput = getByPlaceholderText('أدخل كلمة المرور');
      const loginButton = getByText('تسجيل الدخول');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(loginButton);

      // Wait for login to complete
      await waitFor(() => {
        expect(mockAuthService.login).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        });
      });

      // Verify success alert
      expect(Alert.alert).toHaveBeenCalledWith('نجح تسجيل الدخول', 'مرحباً Test User');
    });

    it('should handle login validation errors', async () => {
      const { getByText } = render(
        <TestWrapper>
          <LoginScreen />
        </TestWrapper>
      );

      const loginButton = getByText('تسجيل الدخول');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(getByText('البريد الإلكتروني مطلوب')).toBeTruthy();
        expect(getByText('كلمة المرور مطلوبة')).toBeTruthy();
      });

      // Should not call login service with invalid data
      expect(mockAuthService.login).not.toHaveBeenCalled();
    });

    it('should handle login service errors', async () => {
      mockAuthService.login.mockRejectedValue(new Error('Invalid credentials'));

      const { getByPlaceholderText, getByText } = render(
        <TestWrapper>
          <LoginScreen />
        </TestWrapper>
      );

      const emailInput = getByPlaceholderText('أدخل بريدك الإلكتروني');
      const passwordInput = getByPlaceholderText('أدخل كلمة المرور');
      const loginButton = getByText('تسجيل الدخول');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'wrongpassword');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(mockAuthService.login).toHaveBeenCalled();
      });

      // Verify error alert
      expect(Alert.alert).toHaveBeenCalledWith(
        'خطأ في تسجيل الدخول',
        'Invalid credentials'
      );
    });
  });

  describe('Dashboard Data Loading', () => {
    it('should load dashboard data after successful authentication', async () => {
      const mockUser = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      const mockDashboardStats = {
        totalInvestment: 100000,
        totalReturns: 15000,
        activeShares: 25,
        pendingPayments: 1,
        completedJamiyas: 3,
        activeJamiyas: 2,
        totalEarnings: 15000,
        monthlyGrowth: 8.5,
      };

      // Mock authenticated state
      mockAuthService.isAuthenticated.mockResolvedValue(true);
      mockAuthService.getStoredUser.mockResolvedValue(mockUser);
      mockReportService.getDashboardStats.mockResolvedValue(mockDashboardStats);

      const { getByText } = render(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      // Wait for data to load
      await waitFor(() => {
        expect(mockReportService.getDashboardStats).toHaveBeenCalled();
      });

      // Verify dashboard data is displayed
      await waitFor(() => {
        expect(getByText('مرحباً بك')).toBeTruthy();
        expect(getByText('Test User')).toBeTruthy();
      });
    });

    it('should handle dashboard data loading errors gracefully', async () => {
      const mockUser = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      // Mock authenticated state
      mockAuthService.isAuthenticated.mockResolvedValue(true);
      mockAuthService.getStoredUser.mockResolvedValue(mockUser);
      mockReportService.getDashboardStats.mockRejectedValue(new Error('Network error'));

      const { getByText } = render(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      // Wait for error handling
      await waitFor(() => {
        expect(mockReportService.getDashboardStats).toHaveBeenCalled();
      });

      // Should still show user info even if dashboard data fails
      await waitFor(() => {
        expect(getByText('مرحباً بك')).toBeTruthy();
        expect(getByText('Test User')).toBeTruthy();
      });
    });
  });

  describe('Biometric Authentication Integration', () => {
    it('should enable biometric authentication successfully', async () => {
      const mockUser = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      // Mock authenticated state
      mockAuthService.isAuthenticated.mockResolvedValue(true);
      mockAuthService.getStoredUser.mockResolvedValue(mockUser);
      mockBiometricService.enableBiometric.mockResolvedValue(true);

      // This would be tested in a biometric settings screen
      // For now, we'll test the service integration
      await waitFor(() => {
        expect(mockBiometricService.initialize).toHaveBeenCalled();
      });
    });

    it('should handle biometric login flow', async () => {
      const mockUser = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      mockBiometricService.isBiometricEnabled.mockReturnValue(true);
      mockBiometricService.authenticate.mockResolvedValue({ success: true });
      mockBiometricService.getStoredCredentials.mockResolvedValue({
        email: '<EMAIL>',
        token: 'stored-token',
      });
      mockAuthService.getCurrentUser.mockResolvedValue(mockUser);

      // This would be tested with a biometric login button
      // For now, we'll verify the service integration
      await waitFor(() => {
        expect(mockBiometricService.initialize).toHaveBeenCalled();
      });
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle network connectivity issues', async () => {
      // Mock network error
      mockAuthService.isAuthenticated.mockRejectedValue(new Error('Network error'));

      const { getByText } = render(
        <TestWrapper>
          <LoginScreen />
        </TestWrapper>
      );

      // Should still render the login screen
      await waitFor(() => {
        expect(getByText('تسجيل الدخول')).toBeTruthy();
      });
    });

    it('should clear auth state on token expiration', async () => {
      // Mock token expiration
      mockAuthService.isAuthenticated.mockResolvedValue(false);
      mockAuthService.getStoredUser.mockResolvedValue(null);

      const { getByText } = render(
        <TestWrapper>
          <LoginScreen />
        </TestWrapper>
      );

      // Should show login screen for unauthenticated state
      await waitFor(() => {
        expect(getByText('تسجيل الدخول')).toBeTruthy();
      });
    });
  });

  describe('Data Persistence and Caching', () => {
    it('should use cached data when available', async () => {
      const mockUser = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      // Mock authenticated state with cached data
      mockAuthService.isAuthenticated.mockResolvedValue(true);
      mockAuthService.getStoredUser.mockResolvedValue(mockUser);

      const { getByText } = render(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      // Should show user data immediately from cache
      await waitFor(() => {
        expect(getByText('مرحباً بك')).toBeTruthy();
        expect(getByText('Test User')).toBeTruthy();
      });
    });
  });

  describe('Performance and Loading States', () => {
    it('should show loading states during data fetching', async () => {
      // Mock slow network response
      mockReportService.getDashboardStats.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({
          totalInvestment: 100000,
          totalReturns: 15000,
          activeShares: 25,
          pendingPayments: 1,
          completedJamiyas: 3,
          activeJamiyas: 2,
          totalEarnings: 15000,
          monthlyGrowth: 8.5,
        }), 1000))
      );

      const mockUser = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      mockAuthService.isAuthenticated.mockResolvedValue(true);
      mockAuthService.getStoredUser.mockResolvedValue(mockUser);

      const { getByText } = render(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      // Should show user info immediately
      await waitFor(() => {
        expect(getByText('Test User')).toBeTruthy();
      });
    });
  });
});
