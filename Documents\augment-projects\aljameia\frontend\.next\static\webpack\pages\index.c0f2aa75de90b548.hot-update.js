"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/layout/NotificationDropdown.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/NotificationDropdown.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Menu,Transition!=!../node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BellIcon,CheckIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"__barrel_optimize__?names=formatDistanceToNow!=!../node_modules/date-fns/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst NotificationDropdown = ()=>{\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            type: \"info\",\n            title: t(\"notifications.payment_reminder.title\"),\n            message: t(\"notifications.payment_reminder.message\"),\n            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n            read: false,\n            actionUrl: \"/payments\"\n        },\n        {\n            id: \"2\",\n            type: \"success\",\n            title: t(\"notifications.document_approved.title\"),\n            message: t(\"notifications.document_approved.message\"),\n            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),\n            read: false,\n            actionUrl: \"/documents\"\n        },\n        {\n            id: \"3\",\n            type: \"warning\",\n            title: t(\"notifications.membership_expiring.title\"),\n            message: t(\"notifications.membership_expiring.message\"),\n            timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),\n            read: true,\n            actionUrl: \"/membership\"\n        }\n    ]);\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CheckIcon, {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, undefined);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ExclamationTriangleIcon, {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, undefined);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.XMarkIcon, {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.InformationCircleIcon, {\n                    className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const markAsRead = (id)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === id ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    };\n    const markAllAsRead = ()=>{\n        setNotifications((prev)=>prev.map((notification)=>({\n                    ...notification,\n                    read: true\n                })));\n    };\n    const handleNotificationClick = (notification)=>{\n        markAsRead(notification.id);\n        if (notification.actionUrl) {\n            router.push(notification.actionUrl);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu, {\n        as: \"div\",\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Button, {\n                    className: \"relative rounded-full p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: t(\"notifications.open\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.BellIcon, {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined),\n                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-medium text-white\",\n                            children: unreadCount > 9 ? \"9+\" : unreadCount\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Transition, {\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                enter: \"transition ease-out duration-100\",\n                enterFrom: \"transform opacity-0 scale-95\",\n                enterTo: \"transform opacity-100 scale-100\",\n                leave: \"transition ease-in duration-75\",\n                leaveFrom: \"transform opacity-100 scale-100\",\n                leaveTo: \"transform opacity-0 scale-95\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Items, {\n                    className: \"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-3 border-b border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: t(\"notifications.title\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: markAllAsRead,\n                                        className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                        children: t(\"notifications.mark_all_read\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-96 overflow-y-auto\",\n                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-6 text-center text-sm text-gray-500\",\n                                children: t(\"notifications.no_notifications\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, undefined) : notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Item, {\n                                    children: (param)=>{\n                                        let { active } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>handleNotificationClick(notification),\n                                            className: \"\".concat(active ? \"bg-gray-50\" : \"\", \" \").concat(!notification.read ? \"bg-blue-50\" : \"\", \" cursor-pointer px-4 py-3 border-b border-gray-100 last:border-b-0\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 mt-1\",\n                                                        children: getNotificationIcon(notification.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium \".concat(!notification.read ? \"text-gray-900\" : \"text-gray-700\"),\n                                                                        children: notification.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                        lineNumber: 158,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                        lineNumber: 164,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__.formatDistanceToNow)(notification.timestamp, {\n                                                                    addSuffix: true,\n                                                                    locale\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    }\n                                }, notification.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined),\n                        notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-3 border-t border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/notifications\"),\n                                className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                                children: t(\"notifications.view_all\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NotificationDropdown, \"KJav3DzRngB5/0eDu4Bo1b9sNUw=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = NotificationDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (NotificationDropdown);\nvar _c;\n$RefreshReg$(_c, \"NotificationDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/NotificationDropdown.tsx\n"));

/***/ })

});