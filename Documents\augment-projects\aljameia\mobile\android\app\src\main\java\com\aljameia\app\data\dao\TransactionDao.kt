package com.aljameia.app.data.dao

import androidx.room.*
import com.aljameia.app.data.entities.Transaction
import com.aljameia.app.data.entities.TransactionStatus
import com.aljameia.app.data.entities.TransactionType
import kotlinx.coroutines.flow.Flow
import java.math.BigDecimal
import java.util.Date

@Dao
interface TransactionDao {
    
    @Query("SELECT * FROM transactions WHERE id = :transactionId")
    suspend fun getTransactionById(transactionId: String): Transaction?
    
    @Query("SELECT * FROM transactions WHERE userId = :userId ORDER BY createdAt DESC")
    fun getTransactionsByUser(userId: String): Flow<List<Transaction>>
    
    @Query("SELECT * FROM transactions WHERE userId = :userId AND status = :status ORDER BY createdAt DESC")
    fun getTransactionsByUserAndStatus(userId: String, status: TransactionStatus): Flow<List<Transaction>>
    
    @Query("SELECT * FROM transactions WHERE userId = :userId AND type = :type ORDER BY createdAt DESC")
    fun getTransactionsByUserAndType(userId: String, type: TransactionType): Flow<List<Transaction>>
    
    @Query("SELECT * FROM transactions WHERE status = :status ORDER BY createdAt DESC")
    fun getTransactionsByStatus(status: TransactionStatus): Flow<List<Transaction>>
    
    @Query("SELECT * FROM transactions WHERE dueDate <= :date AND status = 'PENDING' ORDER BY dueDate ASC")
    fun getOverdueTransactions(date: Date): Flow<List<Transaction>>
    
    @Query("SELECT SUM(amount) FROM transactions WHERE userId = :userId AND status = 'COMPLETED' AND type = :type")
    suspend fun getTotalAmountByUserAndType(userId: String, type: TransactionType): BigDecimal?
    
    @Query("SELECT * FROM transactions WHERE createdAt BETWEEN :startDate AND :endDate ORDER BY createdAt DESC")
    fun getTransactionsByDateRange(startDate: Date, endDate: Date): Flow<List<Transaction>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTransaction(transaction: Transaction)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTransactions(transactions: List<Transaction>)
    
    @Update
    suspend fun updateTransaction(transaction: Transaction)
    
    @Delete
    suspend fun deleteTransaction(transaction: Transaction)
    
    @Query("UPDATE transactions SET status = :status, paidDate = :paidDate WHERE id = :transactionId")
    suspend fun updateTransactionStatus(transactionId: String, status: TransactionStatus, paidDate: Date?)
    
    @Query("DELETE FROM transactions WHERE userId = :userId")
    suspend fun deleteTransactionsByUser(userId: String)
    
    @Query("DELETE FROM transactions")
    suspend fun deleteAllTransactions()
}
