import { useTranslation } from 'next-i18next';
import Link from 'next/link';
import { 
  PlusIcon,
  UserPlusIcon,
  DocumentPlusIcon,
  CreditCardIcon,
  CalendarIcon,
  EnvelopeIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

const QuickActions = () => {
  const { t } = useTranslation('dashboard');

  const quickActions = [
    {
      title: t('quick_actions.add_member'),
      description: t('quick_actions.add_member_desc'),
      icon: UserPlusIcon,
      href: '/members/register',
      color: 'blue',
      primary: true
    },
    {
      title: t('quick_actions.record_payment'),
      description: t('quick_actions.record_payment_desc'),
      icon: CreditCardIcon,
      href: '/payments/record',
      color: 'green',
      primary: true
    },
    {
      title: t('quick_actions.upload_document'),
      description: t('quick_actions.upload_document_desc'),
      icon: DocumentPlusIcon,
      href: '/documents/upload',
      color: 'purple',
      primary: false
    },
    {
      title: t('quick_actions.create_event'),
      description: t('quick_actions.create_event_desc'),
      icon: CalendarIcon,
      href: '/events/create',
      color: 'orange',
      primary: false
    },
    {
      title: t('quick_actions.send_notification'),
      description: t('quick_actions.send_notification_desc'),
      icon: EnvelopeIcon,
      href: '/notifications/send',
      color: 'indigo',
      primary: false
    },
    {
      title: t('quick_actions.generate_report'),
      description: t('quick_actions.generate_report_desc'),
      icon: ChartBarIcon,
      href: '/reports/generate',
      color: 'pink',
      primary: false
    }
  ];

  const getColorClasses = (color: string, primary: boolean) => {
    const colors = {
      blue: primary 
        ? 'bg-blue-600 hover:bg-blue-700 text-white' 
        : 'bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200',
      green: primary 
        ? 'bg-green-600 hover:bg-green-700 text-white' 
        : 'bg-green-50 hover:bg-green-100 text-green-700 border-green-200',
      purple: primary 
        ? 'bg-purple-600 hover:bg-purple-700 text-white' 
        : 'bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-200',
      orange: primary 
        ? 'bg-orange-600 hover:bg-orange-700 text-white' 
        : 'bg-orange-50 hover:bg-orange-100 text-orange-700 border-orange-200',
      indigo: primary 
        ? 'bg-indigo-600 hover:bg-indigo-700 text-white' 
        : 'bg-indigo-50 hover:bg-indigo-100 text-indigo-700 border-indigo-200',
      pink: primary 
        ? 'bg-pink-600 hover:bg-pink-700 text-white' 
        : 'bg-pink-50 hover:bg-pink-100 text-pink-700 border-pink-200'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const getIconColorClasses = (color: string, primary: boolean) => {
    if (primary) return 'text-white';
    
    const colors = {
      blue: 'text-blue-600',
      green: 'text-green-600',
      purple: 'text-purple-600',
      orange: 'text-orange-600',
      indigo: 'text-indigo-600',
      pink: 'text-pink-600'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <PlusIcon className="h-5 w-5 mr-2" />
          {t('quick_actions.title')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {quickActions.map((action, index) => (
            <Link key={index} href={action.href}>
              <div className={`
                p-4 rounded-lg border transition-all duration-200 cursor-pointer
                ${getColorClasses(action.color, action.primary)}
                ${action.primary ? 'shadow-md hover:shadow-lg' : 'border hover:shadow-sm'}
              `}>
                <div className="flex items-center space-x-3">
                  <div className={`
                    p-2 rounded-lg
                    ${action.primary 
                      ? 'bg-white bg-opacity-20' 
                      : `bg-${action.color}-100`
                    }
                  `}>
                    <action.icon className={`
                      h-5 w-5 
                      ${getIconColorClasses(action.color, action.primary)}
                    `} />
                  </div>
                  <div className="flex-1">
                    <h3 className={`
                      font-medium 
                      ${action.primary ? 'text-white' : `text-${action.color}-900`}
                    `}>
                      {action.title}
                    </h3>
                    <p className={`
                      text-sm mt-1
                      ${action.primary 
                        ? 'text-white text-opacity-80' 
                        : `text-${action.color}-600`
                      }
                    `}>
                      {action.description}
                    </p>
                  </div>
                  <div className={`
                    ${action.primary ? 'text-white' : `text-${action.color}-400`}
                  `}>
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Additional Quick Links */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            {t('quick_actions.more_actions')}
          </h4>
          <div className="grid grid-cols-2 gap-2">
            <Link href="/settings">
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <Cog6ToothIcon className="h-4 w-4 mr-2" />
                {t('quick_actions.settings')}
              </Button>
            </Link>
            <Link href="/help">
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {t('quick_actions.help')}
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActions;
