import mongoose, { Document, Schema } from 'mongoose';

// Subscription interface
export interface ISubscription extends Document {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  jamiyaId: mongoose.Types.ObjectId;
  shares: number;
  monthlyAmount: number;
  totalAmount: number;
  status: 'pending' | 'active' | 'paused' | 'cancelled' | 'completed';
  joinedAt: Date;
  nextPaymentDate: Date;
  lastPaymentDate?: Date;
  paymentHistory: {
    paymentId: mongoose.Types.ObjectId;
    amount: number;
    date: Date;
    status: 'completed' | 'failed' | 'pending';
  }[];
  position: number; // Position in the jamiya queue
  receivedAmount?: number; // Amount received when it's their turn
  receivedDate?: Date; // Date when they received the amount
  settings: {
    autoPayment: boolean;
    reminderDays: number;
    preferredPaymentMethod: string;
  };
  statistics: {
    totalPaid: number;
    paymentsCount: number;
    missedPayments: number;
    latePayments: number;
    averagePaymentDelay: number;
  };
  notes?: string;
  createdAt: Date;
  updatedAt: Date;

  // Methods
  calculateNextPaymentDate(): Date;
  updatePaymentHistory(paymentId: mongoose.Types.ObjectId, amount: number, status: string): Promise<void>;
  updateStatistics(): Promise<void>;
  canReceiveAmount(): boolean;
  markAsReceived(amount: number): Promise<void>;
}

// Subscription schema
const subscriptionSchema = new Schema<ISubscription>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
  },
  jamiyaId: {
    type: Schema.Types.ObjectId,
    ref: 'Jamiya',
    required: [true, 'Jamiya ID is required'],
  },
  shares: {
    type: Number,
    required: [true, 'Number of shares is required'],
    min: [1, 'Must have at least 1 share'],
    max: [10, 'Cannot have more than 10 shares'],
    default: 1,
  },
  monthlyAmount: {
    type: Number,
    required: [true, 'Monthly amount is required'],
    min: [100, 'Monthly amount must be at least 100'],
  },
  totalAmount: {
    type: Number,
    required: [true, 'Total amount is required'],
    min: [1000, 'Total amount must be at least 1000'],
  },
  status: {
    type: String,
    enum: ['pending', 'active', 'paused', 'cancelled', 'completed'],
    default: 'pending',
  },
  joinedAt: {
    type: Date,
    default: Date.now,
  },
  nextPaymentDate: {
    type: Date,
    required: [true, 'Next payment date is required'],
  },
  lastPaymentDate: {
    type: Date,
  },
  paymentHistory: [{
    paymentId: {
      type: Schema.Types.ObjectId,
      ref: 'Payment',
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: [0, 'Payment amount cannot be negative'],
    },
    date: {
      type: Date,
      required: true,
    },
    status: {
      type: String,
      enum: ['completed', 'failed', 'pending'],
      required: true,
    },
  }],
  position: {
    type: Number,
    required: [true, 'Position in queue is required'],
    min: [1, 'Position must be at least 1'],
  },
  receivedAmount: {
    type: Number,
    min: [0, 'Received amount cannot be negative'],
  },
  receivedDate: {
    type: Date,
  },
  settings: {
    autoPayment: {
      type: Boolean,
      default: false,
    },
    reminderDays: {
      type: Number,
      default: 3,
      min: [0, 'Reminder days cannot be negative'],
      max: [15, 'Reminder days cannot exceed 15'],
    },
    preferredPaymentMethod: {
      type: String,
      enum: ['mada', 'stc_pay', 'apple_pay', 'bank_transfer', 'cash'],
      default: 'mada',
    },
  },
  statistics: {
    totalPaid: {
      type: Number,
      default: 0,
      min: [0, 'Total paid cannot be negative'],
    },
    paymentsCount: {
      type: Number,
      default: 0,
      min: [0, 'Payments count cannot be negative'],
    },
    missedPayments: {
      type: Number,
      default: 0,
      min: [0, 'Missed payments cannot be negative'],
    },
    latePayments: {
      type: Number,
      default: 0,
      min: [0, 'Late payments cannot be negative'],
    },
    averagePaymentDelay: {
      type: Number,
      default: 0,
      min: [0, 'Average payment delay cannot be negative'],
    },
  },
  notes: {
    type: String,
    maxlength: [500, 'Notes cannot exceed 500 characters'],
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
subscriptionSchema.index({ userId: 1 });
subscriptionSchema.index({ jamiyaId: 1 });
subscriptionSchema.index({ userId: 1, jamiyaId: 1 }, { unique: true });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ nextPaymentDate: 1 });
subscriptionSchema.index({ position: 1 });
subscriptionSchema.index({ joinedAt: 1 });

// Virtual for payment completion percentage
subscriptionSchema.virtual('paymentCompletionPercentage').get(function() {
  if (this.totalAmount === 0) return 0;
  return Math.round((this.statistics.totalPaid / this.totalAmount) * 100);
});

// Virtual for remaining amount
subscriptionSchema.virtual('remainingAmount').get(function() {
  return Math.max(0, this.totalAmount - this.statistics.totalPaid);
});

// Virtual for is payment due
subscriptionSchema.virtual('isPaymentDue').get(function() {
  return this.nextPaymentDate <= new Date();
});

// Virtual for days until next payment
subscriptionSchema.virtual('daysUntilNextPayment').get(function() {
  const today = new Date();
  const nextPayment = new Date(this.nextPaymentDate);
  const diffTime = nextPayment.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Pre-save middleware
subscriptionSchema.pre('save', function(next) {
  // Calculate total amount based on shares and monthly amount
  if (this.isModified('shares') || this.isModified('monthlyAmount')) {
    // Get jamiya duration to calculate total
    // This would typically be done by populating the jamiya
    // For now, we'll assume it's already calculated
  }
  
  next();
});

// Instance method to calculate next payment date
subscriptionSchema.methods.calculateNextPaymentDate = function(): Date {
  const currentDate = this.lastPaymentDate || this.joinedAt;
  const nextDate = new Date(currentDate);
  nextDate.setMonth(nextDate.getMonth() + 1);
  return nextDate;
};

// Instance method to update payment history
subscriptionSchema.methods.updatePaymentHistory = async function(
  paymentId: mongoose.Types.ObjectId,
  amount: number,
  status: string
): Promise<void> {
  this.paymentHistory.push({
    paymentId,
    amount,
    date: new Date(),
    status,
  });

  if (status === 'completed') {
    this.statistics.totalPaid += amount;
    this.statistics.paymentsCount += 1;
    this.lastPaymentDate = new Date();
    this.nextPaymentDate = this.calculateNextPaymentDate();
  } else if (status === 'failed') {
    this.statistics.missedPayments += 1;
  }

  await this.save();
};

// Instance method to update statistics
subscriptionSchema.methods.updateStatistics = async function(): Promise<void> {
  const completedPayments = this.paymentHistory.filter(p => p.status === 'completed');
  const failedPayments = this.paymentHistory.filter(p => p.status === 'failed');

  this.statistics.totalPaid = completedPayments.reduce((sum, p) => sum + p.amount, 0);
  this.statistics.paymentsCount = completedPayments.length;
  this.statistics.missedPayments = failedPayments.length;

  // Calculate late payments (payments made after due date)
  let latePayments = 0;
  let totalDelay = 0;

  for (const payment of completedPayments) {
    // This would require more complex logic to determine if payment was late
    // For now, we'll use a simplified approach
  }

  this.statistics.latePayments = latePayments;
  this.statistics.averagePaymentDelay = latePayments > 0 ? totalDelay / latePayments : 0;

  await this.save();
};

// Instance method to check if can receive amount
subscriptionSchema.methods.canReceiveAmount = function(): boolean {
  return (
    this.status === 'active' &&
    !this.receivedAmount &&
    this.statistics.totalPaid >= this.totalAmount
  );
};

// Instance method to mark as received
subscriptionSchema.methods.markAsReceived = async function(amount: number): Promise<void> {
  this.receivedAmount = amount;
  this.receivedDate = new Date();
  this.status = 'completed';
  await this.save();
};

// Static method to find active subscriptions
subscriptionSchema.statics.findActive = function() {
  return this.find({ status: 'active' });
};

// Static method to find due payments
subscriptionSchema.statics.findDuePayments = function() {
  return this.find({
    status: 'active',
    nextPaymentDate: { $lte: new Date() },
  });
};

// Static method to find by user and jamiya
subscriptionSchema.statics.findByUserAndJamiya = function(userId: string, jamiyaId: string) {
  return this.findOne({ userId, jamiyaId });
};

// Static method to get jamiya members
subscriptionSchema.statics.getJamiyaMembers = function(jamiyaId: string) {
  return this.find({ jamiyaId, status: { $in: ['active', 'completed'] } })
    .populate('userId', 'name email avatar')
    .sort({ position: 1 });
};

// Static method to get user subscriptions
subscriptionSchema.statics.getUserSubscriptions = function(userId: string) {
  return this.find({ userId })
    .populate('jamiyaId', 'name description monthlyAmount status')
    .sort({ createdAt: -1 });
};

// Static method to calculate jamiya statistics
subscriptionSchema.statics.calculateJamiyaStats = async function(jamiyaId: string) {
  const stats = await this.aggregate([
    { $match: { jamiyaId: new mongoose.Types.ObjectId(jamiyaId) } },
    {
      $group: {
        _id: null,
        totalMembers: { $sum: 1 },
        activeMembers: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        completedMembers: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        totalCollected: { $sum: '$statistics.totalPaid' },
        totalShares: { $sum: '$shares' },
        averagePaymentCompletion: { $avg: '$statistics.totalPaid' },
      }
    }
  ]);

  return stats[0] || {
    totalMembers: 0,
    activeMembers: 0,
    completedMembers: 0,
    totalCollected: 0,
    totalShares: 0,
    averagePaymentCompletion: 0,
  };
};

// Create and export the model
export const Subscription = mongoose.model<ISubscription>('Subscription', subscriptionSchema);
export default Subscription;
