import type { AppProps } from 'next/app';
import { useEffect } from 'react';
import Head from 'next/head';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { CacheProvider, EmotionCache } from '@emotion/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { Toaster } from 'react-hot-toast';
import { HelmetProvider } from 'react-helmet-async';

import { theme } from '@/config/theme';
import { createEmotionCache } from '@/utils/createEmotionCache';
import { AuthProvider } from '@/contexts/AuthContext';
import { SocketProvider } from '@/contexts/SocketContext';
import { SettingsProvider } from '@/contexts/SettingsContext';
import { LoadingProvider } from '@/contexts/LoadingContext';
import Layout from '@/components/Layout';
import ErrorBoundary from '@/components/ErrorBoundary';
import '@/styles/globals.css';

// Client-side cache, shared for the whole session of the user in the browser.
const clientSideEmotionCache = createEmotionCache();

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

interface MyAppProps extends AppProps {
  emotionCache?: EmotionCache;
}

export default function MyApp(props: MyAppProps) {
  const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;

  useEffect(() => {
    // Remove the server-side injected CSS.
    const jssStyles = document.querySelector('#jss-server-side');
    if (jssStyles) {
      jssStyles.parentElement?.removeChild(jssStyles);
    }
  }, []);

  return (
    <CacheProvider value={emotionCache}>
      <Head>
        <title>لوحة التحكم - الجمعية</title>
        <meta name="viewport" content="initial-scale=1, width=device-width" />
        <meta name="description" content="لوحة تحكم منصة الجمعية المالية" />
        <meta name="keywords" content="جمعية, مالية, إدارة, لوحة تحكم" />
        <meta name="author" content="فريق الجمعية" />
        
        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:title" content="لوحة التحكم - الجمعية" />
        <meta property="og:description" content="لوحة تحكم منصة الجمعية المالية" />
        <meta property="og:image" content="/images/og-image.png" />
        
        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:title" content="لوحة التحكم - الجمعية" />
        <meta property="twitter:description" content="لوحة تحكم منصة الجمعية المالية" />
        <meta property="twitter:image" content="/images/og-image.png" />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        
        {/* Arabic fonts */}
        <link
          href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />
      </Head>

      <HelmetProvider>
        <ErrorBoundary>
          <QueryClientProvider client={queryClient}>
            <ThemeProvider theme={theme}>
              <CssBaseline />
              <SettingsProvider>
                <LoadingProvider>
                  <AuthProvider>
                    <SocketProvider>
                      <Layout>
                        <Component {...pageProps} />
                      </Layout>
                    </SocketProvider>
                  </AuthProvider>
                </LoadingProvider>
              </SettingsProvider>
            </ThemeProvider>
            
            {/* React Query Devtools (only in development) */}
            {process.env.NODE_ENV === 'development' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
          </QueryClientProvider>
        </ErrorBoundary>
      </HelmetProvider>

      {/* Toast notifications */}
      <Toaster
        position="top-center"
        reverseOrder={false}
        gutter={8}
        containerClassName=""
        containerStyle={{}}
        toastOptions={{
          // Define default options
          className: '',
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
            fontFamily: 'Cairo, sans-serif',
            direction: 'rtl',
          },
          
          // Default options for specific types
          success: {
            duration: 3000,
            style: {
              background: '#4caf50',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#4caf50',
            },
          },
          error: {
            duration: 5000,
            style: {
              background: '#f44336',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#f44336',
            },
          },
          loading: {
            duration: Infinity,
            style: {
              background: '#2196f3',
            },
          },
        }}
      />
    </CacheProvider>
  );
}
