import { useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Typography,
  Divider,
  Tooltip,
  Avatar,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  AccountBalance as AccountBalanceIcon,
  Payment as PaymentIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  Support as SupportIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  ExpandLess,
  ExpandMore,
  AdminPanelSettings,
  Group,
  TrendingUp,
  Receipt,
  CreditCard,
  AccountBalanceWallet,
  BarChart,
  PieChart,
  Timeline,
  ManageAccounts,
  VpnKey,
  Shield,
  BugReport,
  Help,
  ContactSupport,
} from '@mui/icons-material';

import { useAuth } from '@/hooks/useAuth';

interface SidebarProps {
  variant?: 'permanent' | 'temporary';
  open: boolean;
  onClose?: () => void;
}

interface MenuItem {
  id: string;
  title: string;
  icon: React.ElementType;
  path?: string;
  children?: MenuItem[];
  roles?: string[];
  badge?: number;
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    title: 'لوحة التحكم',
    icon: DashboardIcon,
    path: '/dashboard',
  },
  {
    id: 'users',
    title: 'إدارة المستخدمين',
    icon: PeopleIcon,
    children: [
      {
        id: 'users-list',
        title: 'قائمة المستخدمين',
        icon: Group,
        path: '/users',
      },
      {
        id: 'users-roles',
        title: 'الأدوار والصلاحيات',
        icon: AdminPanelSettings,
        path: '/users/roles',
        roles: ['admin'],
      },
      {
        id: 'users-verification',
        title: 'التحقق من الهوية',
        icon: VpnKey,
        path: '/users/verification',
      },
    ],
  },
  {
    id: 'jamiyas',
    title: 'إدارة الجمعيات',
    icon: AccountBalanceIcon,
    children: [
      {
        id: 'jamiyas-list',
        title: 'قائمة الجمعيات',
        icon: AccountBalance,
        path: '/jamiyas',
      },
      {
        id: 'jamiyas-create',
        title: 'إنشاء جمعية جديدة',
        icon: AccountBalanceWallet,
        path: '/jamiyas/create',
      },
      {
        id: 'jamiyas-pending',
        title: 'الجمعيات المعلقة',
        icon: Schedule,
        path: '/jamiyas/pending',
        badge: 5,
      },
    ],
  },
  {
    id: 'payments',
    title: 'إدارة المدفوعات',
    icon: PaymentIcon,
    children: [
      {
        id: 'payments-list',
        title: 'قائمة المدفوعات',
        icon: Receipt,
        path: '/payments',
      },
      {
        id: 'payments-methods',
        title: 'طرق الدفع',
        icon: CreditCard,
        path: '/payments/methods',
      },
      {
        id: 'payments-failed',
        title: 'المدفوعات الفاشلة',
        icon: ErrorOutline,
        path: '/payments/failed',
        badge: 3,
      },
      {
        id: 'payments-refunds',
        title: 'المبالغ المستردة',
        icon: Undo,
        path: '/payments/refunds',
      },
    ],
  },
  {
    id: 'reports',
    title: 'التقارير والإحصائيات',
    icon: AssessmentIcon,
    children: [
      {
        id: 'reports-overview',
        title: 'نظرة عامة',
        icon: BarChart,
        path: '/reports',
      },
      {
        id: 'reports-financial',
        title: 'التقارير المالية',
        icon: TrendingUp,
        path: '/reports/financial',
      },
      {
        id: 'reports-users',
        title: 'تقارير المستخدمين',
        icon: PieChart,
        path: '/reports/users',
      },
      {
        id: 'reports-performance',
        title: 'تقارير الأداء',
        icon: Timeline,
        path: '/reports/performance',
      },
    ],
  },
  {
    id: 'notifications',
    title: 'إدارة الإشعارات',
    icon: NotificationsIcon,
    path: '/notifications',
    badge: 12,
  },
  {
    id: 'security',
    title: 'الأمان والحماية',
    icon: SecurityIcon,
    children: [
      {
        id: 'security-logs',
        title: 'سجلات الأمان',
        icon: Shield,
        path: '/security/logs',
        roles: ['admin'],
      },
      {
        id: 'security-settings',
        title: 'إعدادات الأمان',
        icon: Security,
        path: '/security/settings',
        roles: ['admin'],
      },
      {
        id: 'security-audit',
        title: 'مراجعة الأمان',
        icon: BugReport,
        path: '/security/audit',
        roles: ['admin'],
      },
    ],
  },
  {
    id: 'support',
    title: 'الدعم الفني',
    icon: SupportIcon,
    children: [
      {
        id: 'support-tickets',
        title: 'تذاكر الدعم',
        icon: ContactSupport,
        path: '/support/tickets',
        badge: 8,
      },
      {
        id: 'support-faq',
        title: 'الأسئلة الشائعة',
        icon: Help,
        path: '/support/faq',
      },
    ],
  },
  {
    id: 'settings',
    title: 'الإعدادات',
    icon: SettingsIcon,
    children: [
      {
        id: 'settings-general',
        title: 'الإعدادات العامة',
        icon: ManageAccounts,
        path: '/settings/general',
        roles: ['admin'],
      },
      {
        id: 'settings-system',
        title: 'إعدادات النظام',
        icon: Settings,
        path: '/settings/system',
        roles: ['admin'],
      },
    ],
  },
];

const Sidebar = ({ variant, open, onClose }: SidebarProps) => {
  const theme = useTheme();
  const router = useRouter();
  const { user } = useAuth();
  const [expandedItems, setExpandedItems] = useState<string[]>(['dashboard']);

  const handleItemClick = (item: MenuItem) => {
    if (item.children) {
      const isExpanded = expandedItems.includes(item.id);
      if (isExpanded) {
        setExpandedItems(expandedItems.filter(id => id !== item.id));
      } else {
        setExpandedItems([...expandedItems, item.id]);
      }
    } else if (item.path) {
      router.push(item.path);
      if (variant === 'temporary' && onClose) {
        onClose();
      }
    }
  };

  const isItemActive = (path?: string) => {
    if (!path) return false;
    return router.pathname === path || router.pathname.startsWith(path + '/');
  };

  const hasPermission = (roles?: string[]) => {
    if (!roles || roles.length === 0) return true;
    return user?.role && roles.includes(user.role);
  };

  const renderMenuItem = (item: MenuItem, level = 0) => {
    if (!hasPermission(item.roles)) return null;

    const isActive = isItemActive(item.path);
    const isExpanded = expandedItems.includes(item.id);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <Box key={item.id}>
        <Tooltip 
          title={!open ? item.title : ''} 
          placement="right"
          disableHoverListener={open}
        >
          <ListItem disablePadding sx={{ display: 'block' }}>
            <ListItemButton
              onClick={() => handleItemClick(item)}
              sx={{
                minHeight: 48,
                justifyContent: open ? 'initial' : 'center',
                px: 2.5,
                pl: level > 0 ? 4 : 2.5,
                backgroundColor: isActive 
                  ? alpha(theme.palette.primary.main, 0.1)
                  : 'transparent',
                borderRight: isActive 
                  ? `3px solid ${theme.palette.primary.main}`
                  : '3px solid transparent',
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.05),
                },
              }}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: open ? 3 : 'auto',
                  justifyContent: 'center',
                  color: isActive 
                    ? theme.palette.primary.main 
                    : theme.palette.text.secondary,
                }}
              >
                <item.icon />
              </ListItemIcon>
              
              {open && (
                <>
                  <ListItemText 
                    primary={item.title}
                    sx={{
                      opacity: open ? 1 : 0,
                      color: isActive 
                        ? theme.palette.primary.main 
                        : theme.palette.text.primary,
                    }}
                  />
                  
                  {item.badge && (
                    <Box
                      sx={{
                        backgroundColor: theme.palette.error.main,
                        color: 'white',
                        borderRadius: '50%',
                        minWidth: 20,
                        height: 20,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        mr: hasChildren ? 1 : 0,
                      }}
                    >
                      {item.badge}
                    </Box>
                  )}
                  
                  {hasChildren && (
                    isExpanded ? <ExpandLess /> : <ExpandMore />
                  )}
                </>
              )}
            </ListItemButton>
          </ListItem>
        </Tooltip>

        {hasChildren && open && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children!.map(child => renderMenuItem(child, level + 1))}
            </List>
          </Collapse>
        )}
      </Box>
    );
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo and Title */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: open ? 'flex-start' : 'center',
          px: 2.5,
          py: 2,
          minHeight: 64,
        }}
      >
        <Avatar
          sx={{
            width: 40,
            height: 40,
            backgroundColor: theme.palette.primary.main,
            mr: open ? 2 : 0,
          }}
        >
          <AccountBalanceIcon />
        </Avatar>
        
        {open && (
          <Box>
            <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
              الجمعية
            </Typography>
            <Typography variant="caption" color="text.secondary">
              لوحة التحكم
            </Typography>
          </Box>
        )}
      </Box>

      <Divider />

      {/* User Info */}
      {open && user && (
        <Box sx={{ p: 2 }}>
          <Box display="flex" alignItems="center">
            <Avatar
              src={user.avatar}
              sx={{ width: 32, height: 32, mr: 1 }}
            >
              {user.name?.charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {user.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {user.role === 'admin' ? 'مدير' : user.role === 'moderator' ? 'مشرف' : 'مستخدم'}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}

      <Divider />

      {/* Navigation Menu */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List>
          {menuItems.map(item => renderMenuItem(item))}
        </List>
      </Box>

      {/* Footer */}
      {open && (
        <Box sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
          <Typography variant="caption" color="text.secondary" align="center" display="block">
            الإصدار 1.0.0
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default Sidebar;
