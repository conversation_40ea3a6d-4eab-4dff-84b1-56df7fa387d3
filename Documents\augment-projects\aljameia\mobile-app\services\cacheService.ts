import AsyncStorage from '@react-native-async-storage/async-storage';

// Cache Keys
const CACHE_KEYS = {
  DASHBOARD_STATS: 'cache_dashboard_stats',
  SUBSCRIPTIONS: 'cache_subscriptions',
  PAYMENTS: 'cache_payments',
  JAMIYAS: 'cache_jamiyas',
  USER_PROFILE: 'cache_user_profile',
  PAYMENT_METHODS: 'cache_payment_methods',
  LAST_SYNC: 'cache_last_sync',
};

// Cache Entry Interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

// Cache Configuration
const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  DASHBOARD_TTL: 2 * 60 * 1000, // 2 minutes
  SUBSCRIPTIONS_TTL: 10 * 60 * 1000, // 10 minutes
  PAYMENTS_TTL: 5 * 60 * 1000, // 5 minutes
  JAMIYAS_TTL: 15 * 60 * 1000, // 15 minutes
  USER_PROFILE_TTL: 30 * 60 * 1000, // 30 minutes
  PAYMENT_METHODS_TTL: 60 * 60 * 1000, // 1 hour
};

// Cache Service Class
class CacheService {
  /**
   * Set cache entry with TTL
   */
  async set<T>(key: string, data: T, ttl: number = CACHE_CONFIG.DEFAULT_TTL): Promise<void> {
    try {
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        expiresAt: Date.now() + ttl,
      };

      await AsyncStorage.setItem(key, JSON.stringify(entry));
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  /**
   * Get cache entry if not expired
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const cached = await AsyncStorage.getItem(key);
      
      if (!cached) {
        return null;
      }

      const entry: CacheEntry<T> = JSON.parse(cached);
      
      // Check if expired
      if (Date.now() > entry.expiresAt) {
        await this.remove(key);
        return null;
      }

      return entry.data;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * Remove cache entry
   */
  async remove(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('Cache remove error:', error);
    }
  }

  /**
   * Check if cache entry exists and is valid
   */
  async has(key: string): Promise<boolean> {
    try {
      const cached = await AsyncStorage.getItem(key);
      
      if (!cached) {
        return false;
      }

      const entry: CacheEntry<any> = JSON.parse(cached);
      
      // Check if expired
      if (Date.now() > entry.expiresAt) {
        await this.remove(key);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Cache has error:', error);
      return false;
    }
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<void> {
    try {
      const keys = Object.values(CACHE_KEYS);
      await AsyncStorage.multiRemove(keys);
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  /**
   * Get cache info
   */
  async getInfo(): Promise<{
    totalEntries: number;
    totalSize: number;
    entries: Array<{
      key: string;
      size: number;
      timestamp: number;
      expiresAt: number;
      isExpired: boolean;
    }>;
  }> {
    try {
      const keys = Object.values(CACHE_KEYS);
      const entries = [];
      let totalSize = 0;

      for (const key of keys) {
        const cached = await AsyncStorage.getItem(key);
        
        if (cached) {
          const entry: CacheEntry<any> = JSON.parse(cached);
          const size = new Blob([cached]).size;
          
          entries.push({
            key,
            size,
            timestamp: entry.timestamp,
            expiresAt: entry.expiresAt,
            isExpired: Date.now() > entry.expiresAt,
          });
          
          totalSize += size;
        }
      }

      return {
        totalEntries: entries.length,
        totalSize,
        entries,
      };
    } catch (error) {
      console.error('Cache info error:', error);
      return {
        totalEntries: 0,
        totalSize: 0,
        entries: [],
      };
    }
  }

  /**
   * Clean expired entries
   */
  async cleanExpired(): Promise<number> {
    try {
      const keys = Object.values(CACHE_KEYS);
      let cleanedCount = 0;

      for (const key of keys) {
        const cached = await AsyncStorage.getItem(key);
        
        if (cached) {
          const entry: CacheEntry<any> = JSON.parse(cached);
          
          if (Date.now() > entry.expiresAt) {
            await this.remove(key);
            cleanedCount++;
          }
        }
      }

      return cleanedCount;
    } catch (error) {
      console.error('Cache clean error:', error);
      return 0;
    }
  }

  // Specific cache methods for different data types

  /**
   * Dashboard Stats Cache
   */
  async setDashboardStats(data: any): Promise<void> {
    await this.set(CACHE_KEYS.DASHBOARD_STATS, data, CACHE_CONFIG.DASHBOARD_TTL);
  }

  async getDashboardStats(): Promise<any | null> {
    return await this.get(CACHE_KEYS.DASHBOARD_STATS);
  }

  /**
   * Subscriptions Cache
   */
  async setSubscriptions(data: any[]): Promise<void> {
    await this.set(CACHE_KEYS.SUBSCRIPTIONS, data, CACHE_CONFIG.SUBSCRIPTIONS_TTL);
  }

  async getSubscriptions(): Promise<any[] | null> {
    return await this.get(CACHE_KEYS.SUBSCRIPTIONS);
  }

  /**
   * Payments Cache
   */
  async setPayments(data: any[]): Promise<void> {
    await this.set(CACHE_KEYS.PAYMENTS, data, CACHE_CONFIG.PAYMENTS_TTL);
  }

  async getPayments(): Promise<any[] | null> {
    return await this.get(CACHE_KEYS.PAYMENTS);
  }

  /**
   * Jamiyas Cache
   */
  async setJamiyas(data: any[]): Promise<void> {
    await this.set(CACHE_KEYS.JAMIYAS, data, CACHE_CONFIG.JAMIYAS_TTL);
  }

  async getJamiyas(): Promise<any[] | null> {
    return await this.get(CACHE_KEYS.JAMIYAS);
  }

  /**
   * User Profile Cache
   */
  async setUserProfile(data: any): Promise<void> {
    await this.set(CACHE_KEYS.USER_PROFILE, data, CACHE_CONFIG.USER_PROFILE_TTL);
  }

  async getUserProfile(): Promise<any | null> {
    return await this.get(CACHE_KEYS.USER_PROFILE);
  }

  /**
   * Payment Methods Cache
   */
  async setPaymentMethods(data: any[]): Promise<void> {
    await this.set(CACHE_KEYS.PAYMENT_METHODS, data, CACHE_CONFIG.PAYMENT_METHODS_TTL);
  }

  async getPaymentMethods(): Promise<any[] | null> {
    return await this.get(CACHE_KEYS.PAYMENT_METHODS);
  }

  /**
   * Last Sync Time
   */
  async setLastSync(timestamp: number = Date.now()): Promise<void> {
    await AsyncStorage.setItem(CACHE_KEYS.LAST_SYNC, timestamp.toString());
  }

  async getLastSync(): Promise<number | null> {
    try {
      const timestamp = await AsyncStorage.getItem(CACHE_KEYS.LAST_SYNC);
      return timestamp ? parseInt(timestamp, 10) : null;
    } catch (error) {
      console.error('Get last sync error:', error);
      return null;
    }
  }

  /**
   * Check if data needs refresh
   */
  async needsRefresh(key: string, maxAge: number = CACHE_CONFIG.DEFAULT_TTL): Promise<boolean> {
    try {
      const cached = await AsyncStorage.getItem(key);
      
      if (!cached) {
        return true;
      }

      const entry: CacheEntry<any> = JSON.parse(cached);
      const age = Date.now() - entry.timestamp;
      
      return age > maxAge;
    } catch (error) {
      console.error('Needs refresh check error:', error);
      return true;
    }
  }
}

// Export singleton instance
export const cacheService = new CacheService();
export { CACHE_KEYS, CACHE_CONFIG };
export default cacheService;
