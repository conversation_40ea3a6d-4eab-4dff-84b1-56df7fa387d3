import { Fragment } from 'react';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { Menu, Transition } from '@headlessui/react';
import { LanguageIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ar', name: 'العربية', flag: '🇸🇦' },
];

const LanguageSwitcher = () => {
  const router = useRouter();
  const { t } = useTranslation('common');
  
  const currentLanguage = languages.find(lang => lang.code === router.locale) || languages[0];

  const handleLanguageChange = (locale: string) => {
    router.push(router.asPath, router.asPath, { locale });
  };

  return (
    <Menu as="div" className="relative">
      <div>
        <Menu.Button className="flex items-center rounded-md p-2 text-sm font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          <span className="mr-2">{currentLanguage.flag}</span>
          <span className="hidden sm:block">{currentLanguage.name}</span>
          <ChevronDownIcon className="ml-1 h-4 w-4" />
        </Menu.Button>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          {languages.map((language) => (
            <Menu.Item key={language.code}>
              {({ active }) => (
                <button
                  onClick={() => handleLanguageChange(language.code)}
                  className={`${
                    active ? 'bg-gray-100' : ''
                  } ${
                    language.code === router.locale ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                  } flex w-full items-center px-4 py-2 text-sm`}
                >
                  <span className="mr-3">{language.flag}</span>
                  {language.name}
                </button>
              )}
            </Menu.Item>
          ))}
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

export default LanguageSwitcher;
