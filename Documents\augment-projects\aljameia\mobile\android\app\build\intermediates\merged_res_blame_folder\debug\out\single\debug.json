[{"merged": "com.aljameia.app-merged_res-106:/layout_activity_main.xml.flat", "source": "com.aljameia.app-main-108:/layout/activity_main.xml"}, {"merged": "com.aljameia.app-merged_res-106:/drawable_ic_notification.xml.flat", "source": "com.aljameia.app-main-108:/drawable/ic_notification.xml"}, {"merged": "com.aljameia.app-merged_res-106:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.aljameia.app-main-108:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.aljameia.app-merged_res-106:/drawable_ic_launcher_foreground.xml.flat", "source": "com.aljameia.app-main-108:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.aljameia.app-merged_res-106:/xml_global_tracker.xml.flat", "source": "com.aljameia.app-main-108:/xml/global_tracker.xml"}, {"merged": "com.aljameia.app-merged_res-106:/xml_data_extraction_rules.xml.flat", "source": "com.aljameia.app-main-108:/xml/data_extraction_rules.xml"}, {"merged": "com.aljameia.app-merged_res-106:/drawable_ic_launcher_background.xml.flat", "source": "com.aljameia.app-main-108:/drawable/ic_launcher_background.xml"}, {"merged": "com.aljameia.app-merged_res-106:/xml_backup_rules.xml.flat", "source": "com.aljameia.app-main-108:/xml/backup_rules.xml"}, {"merged": "com.aljameia.app-merged_res-106:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.aljameia.app-main-108:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.aljameia.app-merged_res-106:/xml_network_security_config.xml.flat", "source": "com.aljameia.app-main-108:/xml/network_security_config.xml"}, {"merged": "com.aljameia.app-merged_res-106:/xml_file_paths.xml.flat", "source": "com.aljameia.app-main-108:/xml/file_paths.xml"}, {"merged": "com.aljameia.app-merged_res-106:/xml_shortcuts.xml.flat", "source": "com.aljameia.app-main-108:/xml/shortcuts.xml"}]