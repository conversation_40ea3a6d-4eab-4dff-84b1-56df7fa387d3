package com.aljameia.app.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.math.BigDecimal
import java.util.Date

@Entity(tableName = "events")
data class Event(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String,
    val type: EventType,
    val startDate: Date,
    val endDate: Date,
    val location: String?,
    val address: String?,
    val maxAttendees: Int?,
    val currentAttendees: Int = 0,
    val registrationFee: BigDecimal?,
    val currency: String = "SAR",
    val isRegistrationOpen: Boolean = true,
    val registrationDeadline: Date?,
    val imageUrl: String?,
    val organizerName: String,
    val organizerContact: String?,
    val requirements: List<String> = emptyList(),
    val tags: List<String> = emptyList(),
    val isPublic: Boolean = true,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)

enum class EventType {
    GENERAL_MEETING,
    WORKSHOP,
    SEMINAR,
    SOCIAL_EVENT,
    FUNDRAISING,
    TRAINING,
    CONFERENCE,
    VOLUNTEER_ACTIVITY,
    OTHER
}
