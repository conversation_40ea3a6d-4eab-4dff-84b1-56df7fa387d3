import { api } from './simpleApiClient';
import { User, Member, LoginCredentials, RegisterData, AuthResponse } from '@/types/auth';

export class AuthService {
  // Login user
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await api.post('/auth/login', credentials);
    return response.data.data;
  }

  // Register user
  async register(data: RegisterData): Promise<{ user: User; member: Member }> {
    const response = await api.post('/auth/register', data);
    return response.data.data;
  }

  // Logout user
  async logout(): Promise<void> {
    await api.post('/auth/logout');
  }

  // Refresh access token
  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    const response = await api.post('/auth/refresh', { refreshToken });
    return response.data.data;
  }

  // Get user profile
  async getProfile(): Promise<{ user: User; member?: Member; profile?: any }> {
    const response = await api.get('/auth/profile');
    return response.data.data;
  }

  // Update user profile
  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await api.put('/auth/profile', data);
    return response.data.data.user;
  }

  // Change password
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await api.post('/auth/change-password', {
      currentPassword,
      newPassword,
    });
  }

  // Request password reset
  async requestPasswordReset(email: string): Promise<void> {
    await api.post('/auth/request-password-reset', { email });
  }

  // Reset password
  async resetPassword(token: string, password: string): Promise<void> {
    await api.post('/auth/reset-password', { token, password });
  }

  // Verify email
  async verifyEmail(token: string): Promise<void> {
    await api.post('/auth/verify-email', { token });
  }

  // Resend verification email
  async resendVerificationEmail(): Promise<void> {
    await api.post('/auth/resend-verification');
  }

  // Setup 2FA
  async setup2FA(): Promise<{ secret: string; qrCodeUrl: string; backupCodes: string[] }> {
    const response = await api.post('/auth/2fa/setup');
    return response.data.data;
  }

  // Enable 2FA
  async enable2FA(token: string): Promise<{ backupCodes: string[] }> {
    const response = await api.post('/auth/2fa/enable', { token });
    return response.data.data;
  }

  // Disable 2FA
  async disable2FA(password: string): Promise<void> {
    await api.post('/auth/2fa/disable', { password });
  }

  // Verify 2FA
  async verify2FA(token: string, tempToken: string): Promise<AuthResponse> {
    const response = await api.post('/auth/2fa/verify', { token, tempToken });
    return response.data.data;
  }

  // Use backup code
  async useBackupCode(backupCode: string, tempToken: string): Promise<AuthResponse> {
    const response = await api.post('/auth/2fa/backup-code', { backupCode, tempToken });
    return response.data.data;
  }

  // Generate new backup codes
  async generateBackupCodes(password: string): Promise<{ backupCodes: string[] }> {
    const response = await api.post('/auth/2fa/generate-backup-codes', { password });
    return response.data.data;
  }

  // Get user sessions
  async getSessions(): Promise<any[]> {
    const response = await api.get('/auth/sessions');
    return response.data.data;
  }

  // Revoke session
  async revokeSession(sessionId: string): Promise<void> {
    await api.delete(`/auth/sessions/${sessionId}`);
  }

  // Revoke all sessions
  async revokeAllSessions(): Promise<void> {
    await api.delete('/auth/sessions');
  }

  // Update preferences
  async updatePreferences(preferences: any): Promise<void> {
    await api.put('/auth/preferences', preferences);
  }

  // Delete account
  async deleteAccount(password: string, confirmationText: string, reason?: string): Promise<void> {
    await api.delete('/auth/account', {
      data: { password, confirmationText, reason },
    });
  }

  // Check if email exists
  async checkEmailExists(email: string): Promise<{ exists: boolean }> {
    const response = await api.post('/auth/check-email', { email });
    return response.data.data;
  }

  // Validate password strength
  validatePasswordStrength(password: string): {
    isValid: boolean;
    score: number;
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    // Length check
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('Password must be at least 8 characters long');
    }

    // Uppercase check
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Password must contain at least one uppercase letter');
    }

    // Lowercase check
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Password must contain at least one lowercase letter');
    }

    // Number check
    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('Password must contain at least one number');
    }

    // Special character check
    if (/[@$!%*?&]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Password must contain at least one special character (@$!%*?&)');
    }

    // Common patterns check
    const commonPatterns = [
      /123456/,
      /password/i,
      /qwerty/i,
      /admin/i,
      /letmein/i,
    ];

    if (commonPatterns.some(pattern => pattern.test(password))) {
      score -= 1;
      feedback.push('Password contains common patterns');
    }

    return {
      isValid: score >= 4 && feedback.length === 0,
      score: Math.max(0, Math.min(5, score)),
      feedback,
    };
  }

  // Generate secure password
  generateSecurePassword(length: number = 16): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '@$!%*?&';
    
    const allChars = lowercase + uppercase + numbers + symbols;
    let password = '';
    
    // Ensure at least one character from each category
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    const token = localStorage.getItem('accessToken');
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 > Date.now();
    } catch {
      return false;
    }
  }

  // Get current user from token
  getCurrentUser(): User | null {
    const token = localStorage.getItem('accessToken');
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return {
        id: payload.userId,
        email: payload.email,
        role: payload.role,
      } as User;
    } catch {
      return null;
    }
  }

  // Clear authentication data
  clearAuth(): void {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }

  // Set authentication data
  setAuth(accessToken: string, refreshToken: string): void {
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
  }

  // Get access token
  getAccessToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  // Get refresh token
  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }
}

// Create singleton instance
export const authService = new AuthService();
