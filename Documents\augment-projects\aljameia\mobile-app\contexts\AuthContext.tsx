import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Alert } from 'react-native';
import { authService, User, LoginRequest, RegisterRequest } from '../services';
import { biometricService } from '../services/biometricService';

// Auth Context Types
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isBiometricEnabled: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  loginWithBiometric: () => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  enableBiometric: () => Promise<boolean>;
  disableBiometric: () => Promise<void>;
}

// Create Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth Provider Props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth Provider Component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isBiometricEnabled, setIsBiometricEnabled] = useState(false);

  // Initialize auth state on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);

      // Initialize biometric service
      await biometricService.initialize();
      setIsBiometricEnabled(biometricService.isBiometricEnabled());

      // Check if user is authenticated
      const isAuth = await authService.isAuthenticated();

      if (isAuth) {
        // Get stored user data
        const storedUser = await authService.getStoredUser();

        if (storedUser) {
          setUser(storedUser);
          setIsAuthenticated(true);

          // Refresh user data from server
          try {
            const currentUser = await authService.getCurrentUser();
            setUser(currentUser);
          } catch (error) {
            console.log('Failed to refresh user data:', error);
            // Keep using stored user data
          }
        } else {
          // Token exists but no user data, fetch from server
          try {
            const currentUser = await authService.getCurrentUser();
            setUser(currentUser);
            setIsAuthenticated(true);
          } catch (error) {
            console.log('Failed to get current user:', error);
            await authService.logout();
          }
        }
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      // Clear any invalid auth state
      await authService.logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials: LoginRequest) => {
    try {
      setIsLoading(true);
      
      const authResponse = await authService.login(credentials);
      
      setUser(authResponse.user);
      setIsAuthenticated(true);

      // Store credentials for biometric login if enabled
      if (isBiometricEnabled) {
        await biometricService.storeCredentials({
          email: credentials.email,
          token: authResponse.accessToken,
        });
      }

      Alert.alert('نجح تسجيل الدخول', `مرحباً ${authResponse.user.name}`);
    } catch (error: any) {
      console.error('Login error:', error);
      Alert.alert('خطأ في تسجيل الدخول', error.message || 'فشل في تسجيل الدخول');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithBiometric = async () => {
    try {
      setIsLoading(true);

      // Check if biometric is enabled
      if (!isBiometricEnabled) {
        throw new Error('المصادقة البيومترية غير مفعلة');
      }

      // Authenticate with biometric
      const authResult = await biometricService.authenticate('تسجيل الدخول');

      if (!authResult.success) {
        throw new Error(authResult.error || 'فشل في المصادقة البيومترية');
      }

      // Get stored credentials
      const storedCredentials = await biometricService.getStoredCredentials();

      if (!storedCredentials) {
        throw new Error('لا توجد بيانات مخزنة للمصادقة البيومترية');
      }

      // Use stored token to authenticate
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
      setIsAuthenticated(true);

      Alert.alert('نجح تسجيل الدخول', `مرحباً ${currentUser.name}`);
    } catch (error: any) {
      console.error('Biometric login error:', error);
      Alert.alert('خطأ في المصادقة البيومترية', error.message || 'فشل في تسجيل الدخول');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterRequest) => {
    try {
      setIsLoading(true);
      
      const authResponse = await authService.register(userData);
      
      setUser(authResponse.user);
      setIsAuthenticated(true);
      
      Alert.alert('تم إنشاء الحساب', `مرحباً ${authResponse.user.name}`);
    } catch (error: any) {
      console.error('Registration error:', error);
      Alert.alert('خطأ في إنشاء الحساب', error.message || 'فشل في إنشاء الحساب');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      
      await authService.logout();
      
      setUser(null);
      setIsAuthenticated(false);

      // Clear biometric data
      await biometricService.clearAllData();
      setIsBiometricEnabled(false);

      Alert.alert('تم تسجيل الخروج', 'تم تسجيل خروجك بنجاح');
    } catch (error: any) {
      console.error('Logout error:', error);
      // Even if server logout fails, clear local state
      setUser(null);
      setIsAuthenticated(false);
      setIsBiometricEnabled(false);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      if (!isAuthenticated) return;
      
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error: any) {
      console.error('Refresh user error:', error);
      
      // If refresh fails due to invalid token, logout
      if (error.status === 401) {
        await logout();
      }
    }
  };

  const updateProfile = async (userData: Partial<User>) => {
    try {
      setIsLoading(true);
      
      const updatedUser = await authService.updateProfile(userData);
      setUser(updatedUser);
      
      Alert.alert('تم التحديث', 'تم تحديث ملفك الشخصي بنجاح');
    } catch (error: any) {
      console.error('Update profile error:', error);
      Alert.alert('خطأ في التحديث', error.message || 'فشل في تحديث الملف الشخصي');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const enableBiometric = async (): Promise<boolean> => {
    try {
      const success = await biometricService.enableBiometric();

      if (success) {
        setIsBiometricEnabled(true);

        // Store current credentials for biometric login
        const token = await authService.getCurrentUser();
        if (token && user) {
          await biometricService.storeCredentials({
            email: user.email,
            token: 'current_token', // You might need to get the actual token
          });
        }

        Alert.alert('تم التفعيل', 'تم تفعيل المصادقة البيومترية بنجاح');
      }

      return success;
    } catch (error: any) {
      console.error('Enable biometric error:', error);
      Alert.alert('خطأ', 'فشل في تفعيل المصادقة البيومترية');
      return false;
    }
  };

  const disableBiometric = async (): Promise<void> => {
    try {
      await biometricService.disableBiometric();
      setIsBiometricEnabled(false);

      Alert.alert('تم الإيقاف', 'تم إيقاف المصادقة البيومترية');
    } catch (error: any) {
      console.error('Disable biometric error:', error);
      Alert.alert('خطأ', 'فشل في إيقاف المصادقة البيومترية');
    }
  };

  const contextValue: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    isBiometricEnabled,
    login,
    loginWithBiometric,
    register,
    logout,
    refreshUser,
    updateProfile,
    enableBiometric,
    disableBiometric,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

// HOC for protected routes
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return (props: P) => {
    const { isAuthenticated, isLoading } = useAuth();
    
    if (isLoading) {
      // You can return a loading component here
      return null;
    }
    
    if (!isAuthenticated) {
      // You can redirect to login screen here
      return null;
    }
    
    return <Component {...props} />;
  };
};

export default AuthContext;
