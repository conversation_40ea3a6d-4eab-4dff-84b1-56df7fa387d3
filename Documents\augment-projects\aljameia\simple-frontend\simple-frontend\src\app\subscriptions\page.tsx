'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  CurrencyDollarIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  XCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CalendarIcon,
  BanknotesIcon,
  CreditCardIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import type { MonthlySubscription, SubscriptionPayment } from '@/types/jamiya';

export default function SubscriptionsPage() {
  const { user } = useAuth();
  const [subscriptions, setSubscriptions] = useState<MonthlySubscription[]>([]);
  const [recentPayments, setRecentPayments] = useState<SubscriptionPayment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('active');

  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const mockSubscriptions: MonthlySubscription[] = [
    {
      id: '1',
      jamiyaId: '1',
      memberId: 'user1',
      memberName: 'أحمد محمد الراشد',
      memberEmail: '<EMAIL>',
      memberPhone: '+************',
      sharesCount: 2,
      pricePerShare: 2500,
      monthlyAmount: 5000,
      currency: 'SAR',
      startDate: '2024-01-01',
      status: 'active',
      paymentMethod: 'bank_transfer',
      bankAccount: '************************',
      autoRenewal: true,
      reminderDays: [7, 3, 1],
      totalPaid: 30000,
      totalDue: 60000,
      missedPayments: 0,
      lastPaymentDate: '2024-06-01',
      nextPaymentDate: '2024-07-01',
      createdAt: '2024-01-01T10:00:00Z',
      updatedAt: '2024-06-18T14:30:00Z'
    },
    {
      id: '2',
      jamiyaId: '2',
      memberId: 'user1',
      memberName: 'أحمد محمد الراشد',
      memberEmail: '<EMAIL>',
      memberPhone: '+************',
      sharesCount: 1,
      pricePerShare: 3000,
      monthlyAmount: 3000,
      currency: 'SAR',
      startDate: '2024-03-01',
      status: 'active',
      paymentMethod: 'auto_debit',
      bankAccount: '************************',
      autoRenewal: true,
      reminderDays: [5, 1],
      totalPaid: 12000,
      totalDue: 36000,
      missedPayments: 0,
      lastPaymentDate: '2024-06-01',
      nextPaymentDate: '2024-07-01',
      createdAt: '2024-03-01T10:00:00Z',
      updatedAt: '2024-06-18T14:30:00Z'
    },
    {
      id: '3',
      jamiyaId: '3',
      memberId: 'user1',
      memberName: 'أحمد محمد الراشد',
      memberEmail: '<EMAIL>',
      memberPhone: '+************',
      sharesCount: 4,
      pricePerShare: 500,
      monthlyAmount: 2000,
      currency: 'SAR',
      startDate: '2024-02-01',
      endDate: '2024-05-31',
      status: 'completed',
      paymentMethod: 'cash',
      autoRenewal: false,
      reminderDays: [7, 3],
      totalPaid: 8000,
      totalDue: 8000,
      missedPayments: 0,
      lastPaymentDate: '2024-05-01',
      nextPaymentDate: '2024-06-01',
      createdAt: '2024-02-01T10:00:00Z',
      updatedAt: '2024-05-31T14:30:00Z'
    }
  ];

  const mockRecentPayments: SubscriptionPayment[] = [
    {
      id: '1',
      subscriptionId: '1',
      jamiyaId: '1',
      memberId: 'user1',
      amount: 5000,
      currency: 'SAR',
      dueDate: '2024-06-01',
      paidDate: '2024-06-01',
      status: 'paid',
      paymentMethod: 'bank_transfer',
      reference: 'TXN-2024-001',
      remindersSent: 1,
      lastReminderDate: '2024-05-29',
      createdAt: '2024-05-01T10:00:00Z',
      updatedAt: '2024-06-01T10:00:00Z'
    },
    {
      id: '2',
      subscriptionId: '2',
      jamiyaId: '2',
      memberId: 'user1',
      amount: 3000,
      currency: 'SAR',
      dueDate: '2024-06-01',
      paidDate: '2024-06-01',
      status: 'paid',
      paymentMethod: 'auto_debit',
      reference: 'AUTO-2024-002',
      remindersSent: 0,
      createdAt: '2024-05-01T10:00:00Z',
      updatedAt: '2024-06-01T10:00:00Z'
    },
    {
      id: '3',
      subscriptionId: '1',
      jamiyaId: '1',
      memberId: 'user1',
      amount: 5000,
      currency: 'SAR',
      dueDate: '2024-07-01',
      status: 'pending',
      remindersSent: 0,
      createdAt: '2024-06-01T10:00:00Z',
      updatedAt: '2024-06-01T10:00:00Z'
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSubscriptions(mockSubscriptions);
      setRecentPayments(mockRecentPayments);
      setIsLoading(false);
    };

    loadData();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-blue-500" />;
      case 'paused':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'completed': return 'مكتمل';
      case 'paused': return 'متوقف';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقداً';
      case 'bank_transfer': return 'تحويل بنكي';
      case 'online_payment': return 'دفع إلكتروني';
      case 'auto_debit': return 'خصم تلقائي';
      case 'check': return 'شيك';
      default: return method;
    }
  };

  const filteredSubscriptions = subscriptions.filter(sub => {
    if (activeTab === 'active') return sub.status === 'active';
    if (activeTab === 'completed') return sub.status === 'completed';
    if (activeTab === 'paused') return sub.status === 'paused';
    return true;
  });

  const totalActiveSubscriptions = subscriptions.filter(s => s.status === 'active').length;
  const totalMonthlyAmount = subscriptions
    .filter(s => s.status === 'active')
    .reduce((sum, s) => sum + s.monthlyAmount, 0);
  const totalPaid = subscriptions.reduce((sum, s) => sum + s.totalPaid, 0);
  const pendingPayments = recentPayments.filter(p => p.status === 'pending').length;

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['subscription_management']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الاشتراكات...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['subscription_management']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">منصة الجمعيات المالية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/dashboard">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    لوحة التحكم
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CurrencyDollarIcon className="h-8 w-8 text-blue-600 ml-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">الاشتراكات الشهرية</h1>
                  <p className="text-gray-600">إدارة اشتراكاتك في الأسهم الشهرية للجمعيات</p>
                </div>
              </div>
              <Link href="/subscriptions/create">
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                  <PlusIcon className="h-4 w-4 ml-2" />
                  اشتراك جديد
                </button>
              </Link>
            </div>
          </motion.div>

          {/* Statistics Cards */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-blue-600">{totalActiveSubscriptions}</p>
                  <p className="text-sm text-gray-600">اشتراكات نشطة</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <BanknotesIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(totalMonthlyAmount)}
                  </p>
                  <p className="text-sm text-gray-600">إجمالي شهري</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <CheckCircleIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-purple-600">
                    {formatCurrency(totalPaid)}
                  </p>
                  <p className="text-sm text-gray-600">إجمالي المدفوع</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <ClockIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-orange-600">{pendingPayments}</p>
                  <p className="text-sm text-gray-600">مدفوعات معلقة</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-6"
          >
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 space-x-reverse">
                {[
                  { id: 'active', name: 'نشطة', count: subscriptions.filter(s => s.status === 'active').length },
                  { id: 'completed', name: 'مكتملة', count: subscriptions.filter(s => s.status === 'completed').length },
                  { id: 'paused', name: 'متوقفة', count: subscriptions.filter(s => s.status === 'paused').length },
                  { id: 'all', name: 'الكل', count: subscriptions.length }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      flex items-center py-2 px-1 border-b-2 font-medium text-sm
                      ${activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    {tab.name}
                    <span className="mr-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                      {tab.count}
                    </span>
                  </button>
                ))}
              </nav>
            </div>
          </motion.div>

          {/* Subscriptions List */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">قائمة الاشتراكات</h3>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الجمعية
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الأسهم
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المبلغ الشهري
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      طريقة الدفع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الدفعة القادمة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSubscriptions.map((subscription) => (
                    <tr key={subscription.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            جمعية رقم {subscription.jamiyaId}
                          </div>
                          <div className="text-sm text-gray-500">
                            بدأت في {new Date(subscription.startDate).toLocaleDateString('ar-SA')}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="font-medium">{subscription.sharesCount} سهم</div>
                          <div className="text-xs text-gray-500">
                            {formatCurrency(subscription.pricePerShare)} للسهم
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(subscription.monthlyAmount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <CreditCardIcon className="h-4 w-4 text-gray-400 ml-2" />
                          {getPaymentMethodText(subscription.paymentMethod)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {subscription.status === 'active' ? (
                          <div>
                            <div>{new Date(subscription.nextPaymentDate).toLocaleDateString('ar-SA')}</div>
                            <div className="text-xs text-gray-500">
                              {Math.ceil((new Date(subscription.nextPaymentDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} يوم
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(subscription.status)}
                          <span className="mr-1 text-sm text-gray-600">
                            {getStatusText(subscription.status)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Link href={`/subscriptions/${subscription.id}`}>
                            <button className="text-blue-600 hover:text-blue-900 p-1">
                              <EyeIcon className="h-4 w-4" />
                            </button>
                          </Link>
                          <Link href={`/subscriptions/${subscription.id}/edit`}>
                            <button className="text-green-600 hover:text-green-900 p-1">
                              <PencilIcon className="h-4 w-4" />
                            </button>
                          </Link>
                          <button className="text-red-600 hover:text-red-900 p-1">
                            <XCircleIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </motion.div>

          {/* Recent Payments */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">المدفوعات الأخيرة</h3>
                <Link href="/subscriptions/payments">
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    عرض الكل
                  </button>
                </Link>
              </div>
            </div>

            <div className="p-6">
              <div className="space-y-4">
                {recentPayments.slice(0, 5).map((payment) => (
                  <div key={payment.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${
                        payment.status === 'paid' ? 'bg-green-100' :
                        payment.status === 'pending' ? 'bg-yellow-100' : 'bg-red-100'
                      }`}>
                        {payment.status === 'paid' ? (
                          <CheckCircleIcon className="h-5 w-5 text-green-600" />
                        ) : payment.status === 'pending' ? (
                          <ClockIcon className="h-5 w-5 text-yellow-600" />
                        ) : (
                          <XCircleIcon className="h-5 w-5 text-red-600" />
                        )}
                      </div>
                      <div className="mr-3">
                        <p className="font-medium text-gray-900">
                          جمعية رقم {payment.jamiyaId}
                        </p>
                        <p className="text-sm text-gray-600">
                          {payment.status === 'paid' ? 'تم الدفع في' : 'مستحق في'} {' '}
                          {new Date(payment.paidDate || payment.dueDate).toLocaleDateString('ar-SA')}
                        </p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className={`font-semibold ${
                        payment.status === 'paid' ? 'text-green-600' :
                        payment.status === 'pending' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {formatCurrency(payment.amount)}
                      </p>
                      <div className="flex items-center">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          payment.status === 'paid' ? 'bg-green-100 text-green-600' :
                          payment.status === 'pending' ? 'bg-yellow-100 text-yellow-600' :
                          'bg-red-100 text-red-600'
                        }`}>
                          {payment.status === 'paid' ? 'مدفوع' :
                           payment.status === 'pending' ? 'معلق' : 'متأخر'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
