import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  Modal,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

interface QRData {
  type: 'payment' | 'jamiya' | 'member';
  data: any;
}

export default function QRScannerScreen() {
  const [showResult, setShowResult] = useState(false);
  const [qrData, setQrData] = useState<QRData | null>(null);
  const [manualCode, setManualCode] = useState('');
  const [showManualInput, setShowManualInput] = useState(false);

  // Mock QR codes for testing
  const mockQRCodes = [
    {
      type: 'payment',
      data: {
        amount: 5000,
        description: 'دفعة جمعية الأصدقاء - يونيو 2024',
        recipient: 'جمعية الأصدقاء'
      }
    },
    {
      type: 'jamiya',
      data: {
        name: 'جمعية الأحياء',
        type: 'جمعية استثمارية',
        id: 'JAM001',
        members: 25
      }
    },
    {
      type: 'member',
      data: {
        name: 'أحمد محمد السعيد',
        memberId: 'MEM12345',
        jamiya: 'جمعية الأصدقاء',
        joinDate: '2024-01-15'
      }
    }
  ];

  const simulateQRScan = (index: number) => {
    const selectedQR = mockQRCodes[index] as QRData;
    setQrData(selectedQR);
    setShowResult(true);
  };

  const handleManualInput = () => {
    if (!manualCode.trim()) {
      Alert.alert('خطأ', 'يرجى إدخال رمز QR');
      return;
    }

    try {
      const parsedData = JSON.parse(manualCode);
      setQrData(parsedData);
      setShowResult(true);
      setShowManualInput(false);
      setManualCode('');
    } catch (error) {
      Alert.alert('خطأ', 'رمز QR غير صالح');
    }
  };

  const resetScanner = () => {
    setShowResult(false);
    setQrData(null);
  };

  const handleQRAction = () => {
    if (!qrData) return;

    switch (qrData.type) {
      case 'payment':
        Alert.alert(
          'طلب دفع',
          `المبلغ: ${qrData.data.amount} ريال\nالوصف: ${qrData.data.description}\nالمستفيد: ${qrData.data.recipient}`,
          [
            { text: 'إلغاء', style: 'cancel' },
            { text: 'ادفع الآن', onPress: () => Alert.alert('تم', 'تم الدفع بنجاح') }
          ]
        );
        break;
      case 'jamiya':
        Alert.alert(
          'معلومات الجمعية',
          `اسم الجمعية: ${qrData.data.name}\nنوع الجمعية: ${qrData.data.type}\nعدد الأعضاء: ${qrData.data.members}`,
          [
            { text: 'إغلاق', style: 'cancel' },
            { text: 'انضم', onPress: () => Alert.alert('تم', 'تم إرسال طلب الانضمام') }
          ]
        );
        break;
      case 'member':
        Alert.alert(
          'معلومات العضو',
          `الاسم: ${qrData.data.name}\nرقم العضوية: ${qrData.data.memberId}\nالجمعية: ${qrData.data.jamiya}`,
          [{ text: 'إغلاق', style: 'cancel' }]
        );
        break;
      default:
        Alert.alert('QR Code غير مدعوم', 'نوع QR Code غير معروف');
    }
    
    resetScanner();
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>مسح QR Code</Text>
        <TouchableOpacity 
          style={styles.manualButton}
          onPress={() => setShowManualInput(true)}
        >
          <Ionicons name="create-outline" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Scanner Simulation */}
      <View style={styles.scannerContainer}>
        <View style={styles.scannerFrame}>
          <View style={styles.scanArea}>
            <View style={[styles.corner, styles.topLeft]} />
            <View style={[styles.corner, styles.topRight]} />
            <View style={[styles.corner, styles.bottomLeft]} />
            <View style={[styles.corner, styles.bottomRight]} />
            
            <View style={styles.centerContent}>
              <Ionicons name="qr-code-outline" size={80} color="#3B82F6" />
              <Text style={styles.scanText}>وجه الكاميرا نحو رمز QR</Text>
            </View>
          </View>
        </View>

        {/* Instructions */}
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsText}>
            اختر أحد الرموز التجريبية أدناه للاختبار
          </Text>
        </View>
      </View>

      {/* Mock QR Buttons */}
      <View style={styles.mockButtonsContainer}>
        <TouchableOpacity 
          style={[styles.mockButton, { backgroundColor: '#10B981' }]}
          onPress={() => simulateQRScan(0)}
        >
          <Ionicons name="card-outline" size={24} color="white" />
          <Text style={styles.mockButtonText}>طلب دفع</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.mockButton, { backgroundColor: '#3B82F6' }]}
          onPress={() => simulateQRScan(1)}
        >
          <Ionicons name="business-outline" size={24} color="white" />
          <Text style={styles.mockButtonText}>معلومات جمعية</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.mockButton, { backgroundColor: '#8B5CF6' }]}
          onPress={() => simulateQRScan(2)}
        >
          <Ionicons name="person-outline" size={24} color="white" />
          <Text style={styles.mockButtonText}>معلومات عضو</Text>
        </TouchableOpacity>
      </View>

      {/* Manual Input Modal */}
      <Modal
        visible={showManualInput}
        transparent
        animationType="slide"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.manualInputModal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>إدخال رمز QR يدوياً</Text>
              <TouchableOpacity onPress={() => setShowManualInput(false)}>
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            
            <TextInput
              style={styles.manualInput}
              value={manualCode}
              onChangeText={setManualCode}
              placeholder="أدخل رمز QR هنا..."
              multiline
              numberOfLines={4}
            />
            
            <TouchableOpacity style={styles.submitButton} onPress={handleManualInput}>
              <Text style={styles.submitButtonText}>تأكيد</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Result Modal */}
      <Modal
        visible={showResult}
        transparent
        animationType="slide"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.resultModal}>
            <View style={styles.resultHeader}>
              <Text style={styles.resultTitle}>نتيجة المسح</Text>
              <TouchableOpacity onPress={resetScanner}>
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            {qrData && (
              <View style={styles.resultContent}>
                <View style={styles.resultIcon}>
                  <Ionicons 
                    name={
                      qrData.type === 'payment' ? 'card-outline' :
                      qrData.type === 'jamiya' ? 'business-outline' :
                      'person-outline'
                    } 
                    size={48} 
                    color="#3B82F6" 
                  />
                </View>
                
                <Text style={styles.resultType}>
                  {qrData.type === 'payment' ? 'طلب دفع' :
                   qrData.type === 'jamiya' ? 'معلومات جمعية' :
                   'معلومات عضو'}
                </Text>

                <View style={styles.resultDetails}>
                  {qrData.type === 'payment' && (
                    <>
                      <Text style={styles.resultLabel}>المبلغ:</Text>
                      <Text style={styles.resultValue}>{qrData.data.amount} ريال</Text>
                      <Text style={styles.resultLabel}>الوصف:</Text>
                      <Text style={styles.resultValue}>{qrData.data.description}</Text>
                      <Text style={styles.resultLabel}>المستفيد:</Text>
                      <Text style={styles.resultValue}>{qrData.data.recipient}</Text>
                    </>
                  )}
                  {qrData.type === 'jamiya' && (
                    <>
                      <Text style={styles.resultLabel}>اسم الجمعية:</Text>
                      <Text style={styles.resultValue}>{qrData.data.name}</Text>
                      <Text style={styles.resultLabel}>النوع:</Text>
                      <Text style={styles.resultValue}>{qrData.data.type}</Text>
                      <Text style={styles.resultLabel}>عدد الأعضاء:</Text>
                      <Text style={styles.resultValue}>{qrData.data.members}</Text>
                    </>
                  )}
                  {qrData.type === 'member' && (
                    <>
                      <Text style={styles.resultLabel}>الاسم:</Text>
                      <Text style={styles.resultValue}>{qrData.data.name}</Text>
                      <Text style={styles.resultLabel}>رقم العضوية:</Text>
                      <Text style={styles.resultValue}>{qrData.data.memberId}</Text>
                      <Text style={styles.resultLabel}>الجمعية:</Text>
                      <Text style={styles.resultValue}>{qrData.data.jamiya}</Text>
                    </>
                  )}
                </View>

                <TouchableOpacity style={styles.actionButton} onPress={handleQRAction}>
                  <Text style={styles.actionButtonText}>
                    {qrData.type === 'payment' ? 'ادفع الآن' :
                     qrData.type === 'jamiya' ? 'انضم للجمعية' :
                     'عرض التفاصيل'}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  manualButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  scannerFrame: {
    width: width - 80,
    height: width - 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanArea: {
    width: 250,
    height: 250,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#3B82F6',
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  centerContent: {
    alignItems: 'center',
  },
  scanText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
  },
  instructionsContainer: {
    marginTop: 40,
    alignItems: 'center',
  },
  instructionsText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  mockButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  mockButton: {
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    minWidth: 80,
  },
  mockButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 4,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  manualInputModal: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: height * 0.5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  manualInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    textAlignVertical: 'top',
    marginBottom: 20,
    minHeight: 100,
  },
  submitButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultModal: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: height * 0.7,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  resultTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  resultContent: {
    alignItems: 'center',
  },
  resultIcon: {
    width: 80,
    height: 80,
    backgroundColor: '#EFF6FF',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  resultType: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 20,
  },
  resultDetails: {
    width: '100%',
    marginBottom: 30,
  },
  resultLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  resultValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  actionButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 12,
    width: '100%',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
