// Document Management Types and Interfaces

export interface Document {
  id: string;
  name: string;
  originalName: string;
  description?: string;
  type: DocumentType;
  category: DocumentCategory;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  downloadUrl: string;
  version: number;
  isLatestVersion: boolean;
  parentDocumentId?: string;
  tags: string[];
  metadata: DocumentMetadata;
  permissions: DocumentPermissions;
  status: DocumentStatus;
  uploadedBy: string;
  uploadedByName: string;
  uploadedAt: string;
  lastModified: string;
  lastModifiedBy: string;
  lastModifiedByName: string;
  expiryDate?: string;
  isArchived: boolean;
  archivedAt?: string;
  archivedBy?: string;
  downloadCount: number;
  viewCount: number;
  shareCount: number;
  comments: DocumentComment[];
  approvals: DocumentApproval[];
  relatedDocuments: string[];
  checksum: string;
}

export interface DocumentMetadata {
  author?: string;
  subject?: string;
  keywords?: string[];
  language?: string;
  pageCount?: number;
  wordCount?: number;
  createdDate?: string;
  modifiedDate?: string;
  application?: string;
  customFields: Record<string, any>;
}

export interface DocumentPermissions {
  isPublic: boolean;
  allowedUsers: string[];
  allowedRoles: string[];
  allowedGroups: string[];
  permissions: {
    view: boolean;
    download: boolean;
    edit: boolean;
    delete: boolean;
    share: boolean;
    comment: boolean;
    approve: boolean;
  };
}

export interface DocumentComment {
  id: string;
  content: string;
  authorId: string;
  authorName: string;
  createdAt: string;
  updatedAt?: string;
  isResolved: boolean;
  resolvedBy?: string;
  resolvedAt?: string;
  replies: DocumentCommentReply[];
}

export interface DocumentCommentReply {
  id: string;
  content: string;
  authorId: string;
  authorName: string;
  createdAt: string;
  updatedAt?: string;
}

export interface DocumentApproval {
  id: string;
  approverId: string;
  approverName: string;
  status: ApprovalStatus;
  comments?: string;
  approvedAt?: string;
  level: number;
  isRequired: boolean;
}

export interface DocumentFolder {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  path: string;
  level: number;
  isRoot: boolean;
  permissions: FolderPermissions;
  createdBy: string;
  createdByName: string;
  createdAt: string;
  lastModified: string;
  lastModifiedBy: string;
  documentCount: number;
  subfolderCount: number;
  totalSize: number;
  color?: string;
  icon?: string;
  isArchived: boolean;
  archivedAt?: string;
  archivedBy?: string;
}

export interface FolderPermissions {
  isPublic: boolean;
  allowedUsers: string[];
  allowedRoles: string[];
  allowedGroups: string[];
  permissions: {
    view: boolean;
    upload: boolean;
    createFolder: boolean;
    edit: boolean;
    delete: boolean;
    manage: boolean;
  };
}

export interface DocumentShare {
  id: string;
  documentId: string;
  sharedBy: string;
  sharedByName: string;
  sharedWith?: string;
  sharedWithName?: string;
  shareType: ShareType;
  shareUrl?: string;
  expiryDate?: string;
  password?: string;
  allowDownload: boolean;
  allowComments: boolean;
  viewCount: number;
  downloadCount: number;
  createdAt: string;
  lastAccessed?: string;
  isActive: boolean;
}

export interface DocumentTemplate {
  id: string;
  name: string;
  description: string;
  category: DocumentCategory;
  templateUrl: string;
  thumbnailUrl?: string;
  fields: TemplateField[];
  isActive: boolean;
  createdBy: string;
  createdByName: string;
  createdAt: string;
  lastModified: string;
  usageCount: number;
}

export interface TemplateField {
  id: string;
  name: string;
  label: string;
  type: FieldType;
  isRequired: boolean;
  defaultValue?: any;
  options?: string[];
  validation?: FieldValidation;
  placeholder?: string;
  helpText?: string;
}

export interface FieldValidation {
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
}

export interface DocumentActivity {
  id: string;
  documentId: string;
  action: ActivityAction;
  description: string;
  userId: string;
  userName: string;
  timestamp: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

export interface DocumentSearch {
  query: string;
  filters: DocumentFilters;
  sortBy: DocumentSortField;
  sortOrder: 'asc' | 'desc';
  page: number;
  limit: number;
}

export interface DocumentFilters {
  type?: DocumentType[];
  category?: DocumentCategory[];
  status?: DocumentStatus[];
  uploadedBy?: string[];
  dateFrom?: string;
  dateTo?: string;
  sizeFrom?: number;
  sizeTo?: number;
  tags?: string[];
  folderId?: string;
  hasComments?: boolean;
  needsApproval?: boolean;
  isExpired?: boolean;
}

// Enums
export type DocumentType = 
  | 'pdf'
  | 'word'
  | 'excel'
  | 'powerpoint'
  | 'image'
  | 'video'
  | 'audio'
  | 'text'
  | 'archive'
  | 'other';

export type DocumentCategory = 
  | 'contracts'
  | 'reports'
  | 'policies'
  | 'procedures'
  | 'forms'
  | 'presentations'
  | 'financial'
  | 'legal'
  | 'hr'
  | 'marketing'
  | 'technical'
  | 'meeting_minutes'
  | 'correspondence'
  | 'certificates'
  | 'other';

export type DocumentStatus = 
  | 'draft'
  | 'pending_review'
  | 'under_review'
  | 'approved'
  | 'rejected'
  | 'published'
  | 'archived'
  | 'expired';

export type ApprovalStatus = 
  | 'pending'
  | 'approved'
  | 'rejected'
  | 'withdrawn';

export type ShareType = 
  | 'internal'
  | 'external'
  | 'public'
  | 'password_protected';

export type FieldType = 
  | 'text'
  | 'textarea'
  | 'number'
  | 'date'
  | 'select'
  | 'multiselect'
  | 'checkbox'
  | 'radio'
  | 'file';

export type ActivityAction = 
  | 'uploaded'
  | 'downloaded'
  | 'viewed'
  | 'edited'
  | 'deleted'
  | 'shared'
  | 'commented'
  | 'approved'
  | 'rejected'
  | 'archived'
  | 'restored'
  | 'moved'
  | 'copied'
  | 'renamed';

export type DocumentSortField = 
  | 'name'
  | 'uploadedAt'
  | 'lastModified'
  | 'size'
  | 'downloadCount'
  | 'viewCount'
  | 'type'
  | 'category';

// Form Types
export interface UploadDocumentForm {
  files: File[];
  folderId?: string;
  category: DocumentCategory;
  description?: string;
  tags: string[];
  permissions: Partial<DocumentPermissions>;
  expiryDate?: string;
  requireApproval: boolean;
  approvers: string[];
}

export interface CreateFolderForm {
  name: string;
  description?: string;
  parentId?: string;
  permissions: Partial<FolderPermissions>;
  color?: string;
  icon?: string;
}

export interface ShareDocumentForm {
  documentId: string;
  shareType: ShareType;
  sharedWith?: string;
  expiryDate?: string;
  password?: string;
  allowDownload: boolean;
  allowComments: boolean;
  message?: string;
}

// Statistics Types
export interface DocumentStatistics {
  totalDocuments: number;
  totalSize: number;
  totalFolders: number;
  documentsThisMonth: number;
  downloadsThisMonth: number;
  viewsThisMonth: number;
  pendingApprovals: number;
  expiringSoon: number;
  byCategory: Record<DocumentCategory, number>;
  byType: Record<DocumentType, number>;
  byStatus: Record<DocumentStatus, number>;
  topDownloaded: Document[];
  recentActivity: DocumentActivity[];
  storageUsage: {
    used: number;
    total: number;
    percentage: number;
  };
}

// API Response Types
export interface DocumentListResponse {
  documents: Document[];
  folders: DocumentFolder[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface DocumentResponse {
  document: Document;
  success: boolean;
  message?: string;
}

export interface FolderResponse {
  folder: DocumentFolder;
  success: boolean;
  message?: string;
}

export interface UploadResponse {
  documents: Document[];
  failed: UploadError[];
  success: boolean;
  message?: string;
}

export interface UploadError {
  filename: string;
  error: string;
  code: string;
}
