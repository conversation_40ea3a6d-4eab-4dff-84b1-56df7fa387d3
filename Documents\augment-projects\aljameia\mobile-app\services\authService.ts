import { httpClient, token<PERSON>anager, user<PERSON>anager, ApiResponse } from './api';

// Auth Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  phone?: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

// Auth Service Class
class AuthService {
  /**
   * Login user with email and password
   */
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await httpClient.post<AuthResponse>('/auth/login', credentials);
      
      if (response.success && response.data) {
        // Store tokens and user data
        await tokenManager.setTokens(
          response.data.accessToken,
          response.data.refreshToken
        );
        await userManager.setUserData(response.data.user);
        
        return response.data;
      }
      
      throw new Error(response.message || 'Login failed');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Register new user
   */
  async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      const response = await httpClient.post<AuthResponse>('/auth/register', userData);
      
      if (response.success && response.data) {
        // Store tokens and user data
        await tokenManager.setTokens(
          response.data.accessToken,
          response.data.refreshToken
        );
        await userManager.setUserData(response.data.user);
        
        return response.data;
      }
      
      throw new Error(response.message || 'Registration failed');
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      // Call logout endpoint to invalidate tokens on server
      await httpClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
      // Continue with local cleanup even if server call fails
    } finally {
      // Clear local storage
      await tokenManager.clearTokens();
      await userManager.clearUserData();
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<User> {
    try {
      const response = await httpClient.get<User>('/auth/me');
      
      if (response.success && response.data) {
        // Update stored user data
        await userManager.setUserData(response.data);
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get user profile');
    } catch (error) {
      console.error('Get current user error:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userData: Partial<User>): Promise<User> {
    try {
      const response = await httpClient.put<User>('/auth/profile', userData);
      
      if (response.success && response.data) {
        // Update stored user data
        await userManager.setUserData(response.data);
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to update profile');
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }

  /**
   * Change password
   */
  async changePassword(passwordData: ChangePasswordRequest): Promise<void> {
    try {
      const response = await httpClient.put('/auth/change-password', passwordData);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to change password');
      }
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  /**
   * Request password reset
   */
  async forgotPassword(data: ForgotPasswordRequest): Promise<void> {
    try {
      const response = await httpClient.post('/auth/forgot-password', data);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to send reset email');
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      throw error;
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(data: ResetPasswordRequest): Promise<void> {
    try {
      const response = await httpClient.post('/auth/reset-password', data);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to reset password');
      }
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  }

  /**
   * Verify email
   */
  async verifyEmail(token: string): Promise<void> {
    try {
      const response = await httpClient.post('/auth/verify-email', { token });
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to verify email');
      }
    } catch (error) {
      console.error('Verify email error:', error);
      throw error;
    }
  }

  /**
   * Resend verification email
   */
  async resendVerificationEmail(): Promise<void> {
    try {
      const response = await httpClient.post('/auth/resend-verification');
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to resend verification email');
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      throw error;
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const token = await tokenManager.getAccessToken();
      if (!token) {
        return false;
      }

      // Verify token with server
      const response = await httpClient.get('/auth/verify-token');
      return response.success;
    } catch (error) {
      console.error('Authentication check error:', error);
      return false;
    }
  }

  /**
   * Get stored user data
   */
  async getStoredUser(): Promise<User | null> {
    try {
      return await userManager.getUserData();
    } catch (error) {
      console.error('Get stored user error:', error);
      return null;
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<string | null> {
    try {
      return await tokenManager.refreshAccessToken();
    } catch (error) {
      console.error('Token refresh error:', error);
      // Clear invalid tokens
      await this.logout();
      return null;
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
