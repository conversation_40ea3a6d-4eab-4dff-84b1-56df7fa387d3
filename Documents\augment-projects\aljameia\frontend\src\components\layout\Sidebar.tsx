import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { 
  HomeIcon,
  ChartBarIcon,
  UsersIcon,
  DocumentTextIcon,
  CreditCardIcon,
  CalendarIcon,
  Cog6ToothIcon,
  UserCircleIcon,
  BellIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '@/hooks/useAuth';

const Sidebar = () => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const { user } = useAuth();

  const navigation = [
    {
      name: t('sidebar.dashboard'),
      href: '/dashboard',
      icon: HomeIcon,
      current: router.pathname === '/dashboard',
    },
    {
      name: t('sidebar.financial'),
      href: '/financial',
      icon: ChartBarIcon,
      current: router.pathname.startsWith('/financial'),
      children: [
        { name: t('sidebar.transactions'), href: '/financial/transactions' },
        { name: t('sidebar.reports'), href: '/financial/reports' },
        { name: t('sidebar.budgets'), href: '/financial/budgets' },
      ],
    },
    {
      name: t('sidebar.members'),
      href: '/members',
      icon: UsersIcon,
      current: router.pathname.startsWith('/members'),
      children: [
        { name: t('sidebar.member_list'), href: '/members' },
        { name: t('sidebar.member_registration'), href: '/members/register' },
        { name: t('sidebar.member_reports'), href: '/members/reports' },
      ],
    },
    {
      name: t('sidebar.documents'),
      href: '/documents',
      icon: DocumentTextIcon,
      current: router.pathname.startsWith('/documents'),
      children: [
        { name: t('sidebar.document_library'), href: '/documents' },
        { name: t('sidebar.upload_document'), href: '/documents/upload' },
        { name: t('sidebar.document_templates'), href: '/documents/templates' },
      ],
    },
    {
      name: t('sidebar.payments'),
      href: '/payments',
      icon: CreditCardIcon,
      current: router.pathname.startsWith('/payments'),
      children: [
        { name: t('sidebar.payment_history'), href: '/payments' },
        { name: t('sidebar.payment_methods'), href: '/payments/methods' },
        { name: t('sidebar.invoices'), href: '/payments/invoices' },
      ],
    },
    {
      name: t('sidebar.events'),
      href: '/events',
      icon: CalendarIcon,
      current: router.pathname.startsWith('/events'),
      children: [
        { name: t('sidebar.event_calendar'), href: '/events' },
        { name: t('sidebar.create_event'), href: '/events/create' },
        { name: t('sidebar.event_registrations'), href: '/events/registrations' },
      ],
    },
    {
      name: t('sidebar.tasks'),
      href: '/tasks',
      icon: ClipboardDocumentListIcon,
      current: router.pathname.startsWith('/tasks'),
    },
    {
      name: t('sidebar.notifications'),
      href: '/notifications',
      icon: BellIcon,
      current: router.pathname.startsWith('/notifications'),
    },
  ];

  const bottomNavigation = [
    {
      name: t('sidebar.profile'),
      href: '/profile',
      icon: UserCircleIcon,
      current: router.pathname === '/profile',
    },
    {
      name: t('sidebar.settings'),
      href: '/settings',
      icon: Cog6ToothIcon,
      current: router.pathname.startsWith('/settings'),
    },
  ];

  return (
    <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg border-r border-gray-200">
      <div className="flex h-full flex-col">
        {/* Logo */}
        <div className="flex h-16 items-center px-6 border-b border-gray-200">
          <Link href="/dashboard" className="flex items-center">
            <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AJ</span>
            </div>
            <span className="ml-3 text-lg font-semibold text-gray-900">
              {t('brand.name')}
            </span>
          </Link>
        </div>

        {/* User Info */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <UserCircleIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">{user?.name}</p>
              <p className="text-xs text-gray-500">{user?.role}</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
          {navigation.map((item) => (
            <div key={item.name}>
              <Link
                href={item.href}
                className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                  item.current
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                <item.icon
                  className={`mr-3 h-5 w-5 flex-shrink-0 ${
                    item.current ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`}
                />
                {item.name}
              </Link>
              
              {/* Sub-navigation */}
              {item.children && item.current && (
                <div className="ml-8 mt-1 space-y-1">
                  {item.children.map((child) => (
                    <Link
                      key={child.name}
                      href={child.href}
                      className={`block px-2 py-1 text-sm rounded-md transition-colors duration-200 ${
                        router.pathname === child.href
                          ? 'text-blue-700 bg-blue-50'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      {child.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>

        {/* Bottom Navigation */}
        <div className="px-4 py-4 border-t border-gray-200 space-y-1">
          {bottomNavigation.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                item.current
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <item.icon
                className={`mr-3 h-5 w-5 flex-shrink-0 ${
                  item.current ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                }`}
              />
              {item.name}
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
