# ==============================================
# Multi-stage Dockerfile for Nginx Reverse Proxy
# ==============================================

# Build stage for custom modules (if needed)
FROM nginx:alpine AS builder

# Install build dependencies
RUN apk add --no-cache \
    gcc \
    libc-dev \
    make \
    openssl-dev \
    pcre-dev \
    zlib-dev \
    linux-headers \
    curl \
    gnupg \
    libxslt-dev \
    gd-dev \
    geoip-dev

# Download and compile additional modules (optional)
# Example: Brotli compression module
# RUN wget https://github.com/google/ngx_brotli/archive/v1.0.0rc.tar.gz \
#     && tar -xzf v1.0.0rc.tar.gz \
#     && cd ngx_brotli-1.0.0rc \
#     && git submodule update --init \
#     && cd .. \
#     && nginx -V 2>&1 | grep -o 'configure arguments: .*' | sed 's/configure arguments: //' > /tmp/nginx_config \
#     && ./configure $(cat /tmp/nginx_config) --add-dynamic-module=./ngx_brotli-1.0.0rc \
#     && make modules

# ==============================================
# Production stage
# ==============================================
FROM nginx:alpine

# Install runtime dependencies
RUN apk add --no-cache \
    curl \
    openssl \
    certbot \
    certbot-nginx \
    logrotate \
    tzdata

# Set timezone
ENV TZ=Asia/Riyadh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create nginx user and directories
RUN addgroup -g 101 -S nginx && \
    adduser -S -D -H -u 101 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# Create necessary directories
RUN mkdir -p \
    /var/cache/nginx/api \
    /var/cache/nginx/static \
    /var/log/nginx \
    /var/www/html \
    /var/www/uploads \
    /var/www/certbot \
    /etc/nginx/ssl \
    /etc/nginx/conf.d

# Copy custom modules (if built)
# COPY --from=builder /usr/lib/nginx/modules/ /usr/lib/nginx/modules/

# Copy configuration files
COPY nginx.conf /etc/nginx/nginx.conf
COPY conf.d/ /etc/nginx/conf.d/
COPY ssl/ /etc/nginx/ssl/

# Copy static files (if any)
COPY html/ /var/www/html/

# Generate DH parameters for SSL
RUN openssl dhparam -out /etc/nginx/ssl/dhparam.pem 2048

# Create self-signed certificate for development
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/private.key \
    -out /etc/nginx/ssl/cert.pem \
    -subj "/C=SA/ST=Riyadh/L=Riyadh/O=Aljameia/OU=IT/CN=localhost"

# Create certificate chain file
RUN cp /etc/nginx/ssl/cert.pem /etc/nginx/ssl/chain.pem

# Set proper permissions
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /var/www && \
    chmod -R 755 /var/www && \
    chmod 600 /etc/nginx/ssl/private.key && \
    chmod 644 /etc/nginx/ssl/cert.pem && \
    chmod 644 /etc/nginx/ssl/chain.pem && \
    chmod 644 /etc/nginx/ssl/dhparam.pem

# Create logrotate configuration
RUN cat > /etc/logrotate.d/nginx << 'EOF'
/var/log/nginx/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 nginx nginx
    postrotate
        if [ -f /var/run/nginx.pid ]; then
            kill -USR1 `cat /var/run/nginx.pid`
        fi
    endscript
}
EOF

# Create health check script
RUN cat > /usr/local/bin/health-check.sh << 'EOF'
#!/bin/sh
set -e

# Check if nginx is running
if ! pgrep nginx > /dev/null; then
    echo "Nginx is not running"
    exit 1
fi

# Check if nginx can serve requests
if ! curl -f http://localhost/health > /dev/null 2>&1; then
    echo "Nginx health check failed"
    exit 1
fi

echo "Nginx is healthy"
exit 0
EOF

RUN chmod +x /usr/local/bin/health-check.sh

# Create startup script
RUN cat > /usr/local/bin/start-nginx.sh << 'EOF'
#!/bin/sh
set -e

echo "Starting Nginx for Aljameia Platform..."

# Test nginx configuration
echo "Testing Nginx configuration..."
nginx -t

# Start nginx in foreground
echo "Starting Nginx..."
exec nginx -g "daemon off;"
EOF

RUN chmod +x /usr/local/bin/start-nginx.sh

# Create SSL certificate renewal script
RUN cat > /usr/local/bin/renew-ssl.sh << 'EOF'
#!/bin/sh
set -e

echo "Renewing SSL certificates..."

# Renew certificates
certbot renew --nginx --quiet

# Reload nginx if certificates were renewed
if [ $? -eq 0 ]; then
    echo "Certificates renewed successfully"
    nginx -s reload
else
    echo "Certificate renewal failed"
    exit 1
fi
EOF

RUN chmod +x /usr/local/bin/renew-ssl.sh

# Create cron job for certificate renewal
RUN echo "0 12 * * * /usr/local/bin/renew-ssl.sh >> /var/log/nginx/ssl-renewal.log 2>&1" | crontab -

# Expose ports
EXPOSE 80 443

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/health-check.sh

# Set working directory
WORKDIR /etc/nginx

# Use non-root user
USER nginx

# Start nginx
CMD ["/usr/local/bin/start-nginx.sh"]

# Labels
LABEL maintainer="Aljameia Team <<EMAIL>>"
LABEL description="Nginx reverse proxy for Aljameia Platform"
LABEL version="1.0.0"
