// Member Types and Interfaces

export interface Member {
  id: string;
  membershipNumber: string;
  personalInfo: PersonalInfo;
  contactInfo: ContactInfo;
  membershipInfo: MembershipInfo;
  emergencyContact: EmergencyContact;
  documents: Document[];
  payments: Payment[];
  activities: Activity[];
  status: MemberStatus;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  lastModifiedBy: string;
}

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  fullNameArabic: string;
  fullNameEnglish?: string;
  dateOfBirth: string;
  gender: 'male' | 'female';
  nationality: string;
  nationalId: string;
  passportNumber?: string;
  maritalStatus: 'single' | 'married' | 'divorced' | 'widowed';
  profession: string;
  employer?: string;
  education: string;
  avatar?: string;
}

export interface ContactInfo {
  email: string;
  phone: string;
  alternativePhone?: string;
  whatsapp?: string;
  address: Address;
  socialMedia?: SocialMedia;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface SocialMedia {
  twitter?: string;
  linkedin?: string;
  instagram?: string;
  facebook?: string;
}

export interface MembershipInfo {
  type: MembershipType;
  category: MembershipCategory;
  joinDate: string;
  renewalDate: string;
  expiryDate: string;
  isActive: boolean;
  benefits: string[];
  restrictions: string[];
  sponsoredBy?: string;
  referredBy?: string;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
  address?: string;
}

export interface Document {
  id: string;
  type: DocumentType;
  name: string;
  url: string;
  uploadDate: string;
  expiryDate?: string;
  isVerified: boolean;
  verifiedBy?: string;
  verificationDate?: string;
  size: number;
  mimeType: string;
}

export interface Payment {
  id: string;
  type: PaymentType;
  amount: number;
  currency: string;
  date: string;
  dueDate?: string;
  status: PaymentStatus;
  method: PaymentMethod;
  reference: string;
  description: string;
  receiptUrl?: string;
}

export interface Activity {
  id: string;
  type: ActivityType;
  title: string;
  description: string;
  date: string;
  location?: string;
  status: ActivityStatus;
  attendanceStatus?: AttendanceStatus;
  feedback?: string;
  rating?: number;
}

// Enums
export type MembershipType = 'basic' | 'premium' | 'vip' | 'honorary' | 'student' | 'corporate';

export type MembershipCategory = 'individual' | 'family' | 'corporate' | 'student' | 'senior';

export type MemberStatus = 'active' | 'inactive' | 'suspended' | 'pending' | 'expired' | 'cancelled';

export type DocumentType = 
  | 'national_id' 
  | 'passport' 
  | 'photo' 
  | 'cv' 
  | 'certificate' 
  | 'contract' 
  | 'medical_report' 
  | 'other';

export type PaymentType = 
  | 'membership_fee' 
  | 'renewal_fee' 
  | 'event_fee' 
  | 'fine' 
  | 'donation' 
  | 'service_fee' 
  | 'other';

export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';

export type PaymentMethod = 'cash' | 'bank_transfer' | 'credit_card' | 'debit_card' | 'online' | 'check';

export type ActivityType = 
  | 'meeting' 
  | 'workshop' 
  | 'seminar' 
  | 'conference' 
  | 'social_event' 
  | 'training' 
  | 'volunteer' 
  | 'other';

export type ActivityStatus = 'upcoming' | 'ongoing' | 'completed' | 'cancelled' | 'postponed';

export type AttendanceStatus = 'registered' | 'attended' | 'absent' | 'cancelled';

// Search and Filter Types
export interface MemberSearchFilters {
  search?: string;
  membershipType?: MembershipType[];
  status?: MemberStatus[];
  city?: string[];
  joinDateFrom?: string;
  joinDateTo?: string;
  ageFrom?: number;
  ageTo?: number;
  gender?: ('male' | 'female')[];
  hasActivePayments?: boolean;
  hasExpiredDocuments?: boolean;
}

export interface MemberSortOptions {
  field: 'name' | 'joinDate' | 'membershipNumber' | 'lastActivity' | 'status';
  direction: 'asc' | 'desc';
}

// Form Types
export interface CreateMemberForm {
  personalInfo: Omit<PersonalInfo, 'avatar'>;
  contactInfo: ContactInfo;
  membershipInfo: Pick<MembershipInfo, 'type' | 'category' | 'sponsoredBy' | 'referredBy'>;
  emergencyContact: EmergencyContact;
}

export interface UpdateMemberForm extends Partial<CreateMemberForm> {
  id: string;
}

// Statistics Types
export interface MemberStatistics {
  total: number;
  active: number;
  inactive: number;
  newThisMonth: number;
  expiringThisMonth: number;
  byMembershipType: Record<MembershipType, number>;
  byCity: Record<string, number>;
  byAge: {
    '18-25': number;
    '26-35': number;
    '36-45': number;
    '46-55': number;
    '56-65': number;
    '65+': number;
  };
  byGender: {
    male: number;
    female: number;
  };
}

// API Response Types
export interface MemberListResponse {
  members: Member[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface MemberResponse {
  member: Member;
  success: boolean;
  message?: string;
}
