package com.aljameia.app.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "users")
data class User(
    @PrimaryKey
    val id: String,
    val email: String,
    val firstName: String,
    val lastName: String,
    val phoneNumber: String?,
    val membershipNumber: String?,
    val membershipType: String,
    val isActive: Boolean = true,
    val profileImageUrl: String?,
    val dateJoined: Date,
    val lastLoginDate: Date?,
    val preferredLanguage: String = "en", // "en" or "ar"
    val notificationsEnabled: Boolean = true,
    val biometricEnabled: Boolean = false,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)
