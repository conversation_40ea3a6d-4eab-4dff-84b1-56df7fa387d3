# ==============================================
# Multi-stage Dockerfile for Next.js Admin Dashboard
# ==============================================

# Base image with Node.js LTS
FROM node:20-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# ==============================================
# Dependencies stage
# ==============================================
FROM base AS deps

# Copy package files
COPY package*.json ./
COPY yarn.lock* ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# ==============================================
# Development stage
# ==============================================
FROM base AS development

# Install development dependencies
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .

# Change ownership to nextjs user
RUN chown -R nextjs:nodejs /app
USER nextjs

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3001/api/health || exit 1

# Start development server
CMD ["dumb-init", "npm", "run", "dev"]

# ==============================================
# Build stage
# ==============================================
FROM base AS builder

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev)
RUN npm ci

# Copy source code
COPY . .

# Set environment variables for build
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Build the application
RUN npm run build

# ==============================================
# Production stage
# ==============================================
FROM base AS production

# Set production environment
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3001

# Copy production dependencies
COPY --from=deps /app/node_modules ./node_modules

# Copy built application
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public

# Create necessary directories
RUN mkdir -p /app/.next/cache && \
    chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3001/api/health || exit 1

# Start production server
CMD ["dumb-init", "node", "server.js"]

# ==============================================
# Testing stage
# ==============================================
FROM development AS testing

# Install testing dependencies
RUN npm ci

# Copy test files
COPY __tests__/ ./__tests__/
COPY jest.config.js ./
COPY jest.setup.js ./

# Run tests
CMD ["npm", "test"]
