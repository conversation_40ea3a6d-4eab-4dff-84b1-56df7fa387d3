package com.aljameia.app.data.repository

import com.aljameia.app.data.dao.UserDao
import com.aljameia.app.data.entities.User
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

// @Singleton
class UserRepository /* @Inject constructor(
    private val userDao: UserDao
) */ {
    
    // For now, we'll use mock data since we disabled Hilt
    private val mockUsers = mutableListOf<User>()
    
    suspend fun getUserById(userId: String): User? {
        return mockUsers.find { it.id == userId }
    }
    
    suspend fun getUserByEmail(email: String): User? {
        return mockUsers.find { it.email == email }
    }
    
    suspend fun getUserByMembershipNumber(membershipNumber: String): User? {
        return mockUsers.find { it.membershipNumber == membershipNumber }
    }
    
    fun getActiveUsers(): Flow<List<User>> {
        return kotlinx.coroutines.flow.flowOf(mockUsers.filter { it.isActive })
    }
    
    suspend fun insertUser(user: User) {
        mockUsers.removeAll { it.id == user.id }
        mockUsers.add(user)
    }
    
    suspend fun updateUser(user: User) {
        val index = mockUsers.indexOfFirst { it.id == user.id }
        if (index != -1) {
            mockUsers[index] = user
        }
    }
    
    suspend fun deleteUser(user: User) {
        mockUsers.removeAll { it.id == user.id }
    }
    
    suspend fun updateLastLoginDate(userId: String, loginDate: Date) {
        val user = mockUsers.find { it.id == userId }
        user?.let {
            val updatedUser = it.copy(lastLoginDate = loginDate, updatedAt = Date())
            updateUser(updatedUser)
        }
    }
    
    suspend fun updateNotificationSettings(userId: String, enabled: Boolean) {
        val user = mockUsers.find { it.id == userId }
        user?.let {
            val updatedUser = it.copy(notificationsEnabled = enabled, updatedAt = Date())
            updateUser(updatedUser)
        }
    }
    
    suspend fun updateBiometricSettings(userId: String, enabled: Boolean) {
        val user = mockUsers.find { it.id == userId }
        user?.let {
            val updatedUser = it.copy(biometricEnabled = enabled, updatedAt = Date())
            updateUser(updatedUser)
        }
    }
    
    suspend fun updatePreferredLanguage(userId: String, language: String) {
        val user = mockUsers.find { it.id == userId }
        user?.let {
            val updatedUser = it.copy(preferredLanguage = language, updatedAt = Date())
            updateUser(updatedUser)
        }
    }
    
    suspend fun createMockUser(): User {
        val mockUser = User(
            id = "user_001",
            email = "<EMAIL>",
            firstName = "أحمد",
            lastName = "المحمد",
            phoneNumber = "+966501234567",
            membershipNumber = "*********",
            membershipType = "Premium",
            isActive = true,
            profileImageUrl = null,
            dateJoined = Date(),
            lastLoginDate = Date(),
            preferredLanguage = "ar",
            notificationsEnabled = true,
            biometricEnabled = false
        )
        insertUser(mockUser)
        return mockUser
    }
}
