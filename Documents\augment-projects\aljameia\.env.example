# Environment Configuration
NODE_ENV=development

# Database Configuration
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=secure_password_123
MONGO_DATABASE=aljameia
MONGODB_URI=*****************************************************************************

# Redis Configuration
REDIS_PASSWORD=redis_password_123
REDIS_URL=redis://:redis_password_123@localhost:6379

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_minimum_32_characters
JWT_REFRESH_SECRET=your_super_secret_jwt_refresh_key_here_minimum_32_characters
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Encryption Configuration
ENCRYPTION_KEY=your_32_character_encryption_key_here
ENCRYPTION_ALGORITHM=aes-256-gcm

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=aljameia-documents
AWS_CLOUDFRONT_DOMAIN=your_cloudfront_domain.cloudfront.net

# Payment Gateway Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# SMS Service Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Email Service Configuration (SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Al-Jameia Financial Association

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_WS_URL=ws://localhost:3000
NEXT_PUBLIC_APP_NAME=Al-Jameia Financial Association
NEXT_PUBLIC_APP_VERSION=1.0.0

# Google Services
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
GOOGLE_OAUTH_CLIENT_ID=your_google_oauth_client_id
GOOGLE_OAUTH_CLIENT_SECRET=your_google_oauth_client_secret

# Monitoring & Analytics
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn
SENTRY_AUTH_TOKEN=your_sentry_auth_token
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_google_analytics_id

# Monitoring Configuration
GRAFANA_PASSWORD=admin123
PROMETHEUS_RETENTION=200h

# Security Configuration
RATE_LIMIT_WINDOW_MS=3600000
RATE_LIMIT_MAX_REQUESTS=1000
SESSION_SECRET=your_session_secret_key_minimum_32_characters
CORS_ORIGIN=http://localhost:3001,https://aljameia.com

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx
UPLOAD_PATH=uploads

# Notification Configuration
PUSH_NOTIFICATION_VAPID_PUBLIC_KEY=your_vapid_public_key
PUSH_NOTIFICATION_VAPID_PRIVATE_KEY=your_vapid_private_key
PUSH_NOTIFICATION_VAPID_EMAIL=<EMAIL>

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=aljameia-backups

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_SIZE=100m
LOG_MAX_FILES=10

# API Configuration
API_VERSION=v1
API_PREFIX=/api
API_TIMEOUT=30000
API_BODY_LIMIT=10mb

# Cache Configuration
CACHE_TTL_DEFAULT=3600
CACHE_TTL_USER_PROFILE=1800
CACHE_TTL_FINANCIAL_DATA=900
CACHE_TTL_REPORTS=7200

# Development Configuration
DEBUG=aljameia:*
MOCK_PAYMENTS=true
MOCK_SMS=true
MOCK_EMAIL=true

# Testing Configuration
TEST_MONGODB_URI=**********************************************************************************
TEST_REDIS_URL=redis://:redis_password_123@localhost:6379/1

# Production Configuration (uncomment for production)
# NODE_ENV=production
# HTTPS_ENABLED=true
# SSL_CERT_PATH=/etc/ssl/certs/aljameia.crt
# SSL_KEY_PATH=/etc/ssl/private/aljameia.key
# CLUSTER_MODE=true
# CLUSTER_WORKERS=4

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_RETRIES=3

# Internationalization
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en
TIMEZONE=Asia/Riyadh

# Feature Flags
FEATURE_2FA_ENABLED=true
FEATURE_BIOMETRIC_AUTH=true
FEATURE_PUSH_NOTIFICATIONS=true
FEATURE_REAL_TIME_UPDATES=true
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_MOBILE_PAYMENTS=true

# Compliance Configuration
PCI_DSS_ENABLED=true
GDPR_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=2555
DATA_RETENTION_DAYS=2555

# Performance Configuration
CONNECTION_POOL_SIZE=20
QUERY_TIMEOUT=30000
SLOW_QUERY_THRESHOLD=1000
CACHE_COMPRESSION=true

# Mobile App Configuration
MOBILE_API_VERSION=v1
MOBILE_MIN_VERSION=1.0.0
MOBILE_FORCE_UPDATE=false
MOBILE_DEEP_LINK_SCHEME=aljameia

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_key
WEBHOOK_TIMEOUT=10000
WEBHOOK_RETRY_ATTEMPTS=3

# Third-party Integrations
BANK_API_ENDPOINT=https://api.bank.com/v1
BANK_API_KEY=your_bank_api_key
CREDIT_BUREAU_API_ENDPOINT=https://api.creditbureau.com/v1
CREDIT_BUREAU_API_KEY=your_credit_bureau_api_key
