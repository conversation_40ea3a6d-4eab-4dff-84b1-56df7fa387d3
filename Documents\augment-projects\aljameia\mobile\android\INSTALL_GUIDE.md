# دليل تثبيت تطبيق الجمعية - Al Jameia App Installation Guide

## 🇸🇦 العربية

### متطلبات التشغيل
- **نظام التشغيل**: Android 7.0 (API 24) أو أحدث
- **مساحة التخزين**: 100 ميجابايت على الأقل
- **الذاكرة**: 2 جيجابايت RAM أو أكثر
- **الاتصال**: اتصال بالإنترنت للمزامنة

### خطوات التثبيت

#### الطريقة الأولى: التثبيت المباشر
1. **تحميل الملف**
   - انتقل إلى مجلد: `app/build/outputs/apk/debug/`
   - احفظ ملف `app-debug.apk` على جهازك

2. **تفعيل المصادر غير المعروفة**
   - اذهب إلى **الإعدادات** > **الأمان**
   - فعّل **مصادر غير معروفة** أو **تثبيت التطبيقات غير المعروفة**

3. **تثبيت التطبيق**
   - اضغط على ملف `app-debug.apk`
   - اتبع التعليمات على الشاشة
   - اضغط **تثبيت**

#### الطريقة الثانية: باستخدام ADB
```bash
# تأكد من تفعيل وضع المطور و USB Debugging
adb install app/build/outputs/apk/debug/app-debug.apk
```

### أول استخدام
1. **افتح التطبيق** من قائمة التطبيقات
2. **شاشة البداية** ستظهر لمدة 3 ثوانٍ
3. **الشاشة الرئيسية** ستحتوي على:
   - أزرار سريعة للمدفوعات والوثائق
   - إعدادات الملف الشخصي
   - خيارات اللغة (عربي/إنجليزي)

### الميزات المتاحة
- ✅ **لوحة المعلومات المالية** - عرض شامل للمعاملات
- ✅ **إدارة الوثائق** - عرض وتحميل الملفات
- ✅ **الإعدادات ثنائية اللغة** - دعم العربية والإنجليزية
- ✅ **المصادقة البيومترية** - بصمة الإصبع والوجه
- ✅ **الوضع الليلي** - مظهر داكن للعينين
- ✅ **الإشعارات** - تنبيهات المدفوعات والأحداث

### استكشاف الأخطاء
- **التطبيق لا يفتح**: تأكد من أن جهازك يدعم Android 7.0+
- **رسائل خطأ**: تحقق من اتصال الإنترنت
- **مشاكل الأداء**: أعد تشغيل التطبيق

---

## 🇺🇸 English

### System Requirements
- **OS**: Android 7.0 (API 24) or higher
- **Storage**: At least 100MB free space
- **RAM**: 2GB or more
- **Network**: Internet connection for sync

### Installation Steps

#### Method 1: Direct Installation
1. **Download APK**
   - Navigate to: `app/build/outputs/apk/debug/`
   - Save `app-debug.apk` to your device

2. **Enable Unknown Sources**
   - Go to **Settings** > **Security**
   - Enable **Unknown Sources** or **Install Unknown Apps**

3. **Install App**
   - Tap on `app-debug.apk` file
   - Follow on-screen instructions
   - Tap **Install**

#### Method 2: Using ADB
```bash
# Make sure Developer Options and USB Debugging are enabled
adb install app/build/outputs/apk/debug/app-debug.apk
```

### First Launch
1. **Open the app** from app drawer
2. **Splash screen** will show for 3 seconds
3. **Main screen** contains:
   - Quick action buttons for payments and documents
   - Profile settings
   - Language options (Arabic/English)

### Available Features
- ✅ **Financial Dashboard** - Comprehensive transaction overview
- ✅ **Document Management** - View and download files
- ✅ **Bilingual Settings** - Arabic and English support
- ✅ **Biometric Authentication** - Fingerprint and face recognition
- ✅ **Dark Mode** - Eye-friendly dark theme
- ✅ **Notifications** - Payment and event alerts

### Troubleshooting
- **App won't open**: Ensure device supports Android 7.0+
- **Error messages**: Check internet connection
- **Performance issues**: Restart the app

---

## 📱 Technical Information

- **APK Size**: ~42MB
- **Package Name**: `com.aljameia.app`
- **Version**: 1.0.0 (Debug)
- **Min SDK**: 24 (Android 7.0)
- **Target SDK**: 34 (Android 14)

## 🔧 Development Build

This is a **debug build** for testing purposes. For production use:
1. Generate a signed release APK
2. Upload to Google Play Store
3. Enable ProGuard for code optimization

## 📞 Support

للدعم الفني - For Technical Support:
- **Email**: <EMAIL>
- **Phone**: +966-XX-XXX-XXXX

---

**تم البناء بـ ❤️ لجمعية الجمعية - Built with ❤️ for Al Jameia Association**
