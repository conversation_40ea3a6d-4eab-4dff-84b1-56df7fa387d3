'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  CurrencyDollarIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  CalendarIcon,
  BanknotesIcon,
  CreditCardIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import type { SubscriptionPayment } from '@/types/jamiya';

export default function PaymentsPage() {
  const { user } = useAuth();
  const [payments, setPayments] = useState<SubscriptionPayment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');

  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const mockPayments: SubscriptionPayment[] = [
    {
      id: '1',
      subscriptionId: '1',
      jamiyaId: '1',
      memberId: 'user1',
      amount: 5000,
      currency: 'SAR',
      dueDate: '2024-07-01',
      status: 'pending',
      remindersSent: 0,
      createdAt: '2024-06-01T10:00:00Z',
      updatedAt: '2024-06-01T10:00:00Z'
    },
    {
      id: '2',
      subscriptionId: '1',
      jamiyaId: '1',
      memberId: 'user1',
      amount: 5000,
      currency: 'SAR',
      dueDate: '2024-06-01',
      paidDate: '2024-06-01',
      status: 'paid',
      paymentMethod: 'bank_transfer',
      reference: 'TXN-2024-001',
      remindersSent: 1,
      lastReminderDate: '2024-05-29',
      createdAt: '2024-05-01T10:00:00Z',
      updatedAt: '2024-06-01T10:00:00Z'
    },
    {
      id: '3',
      subscriptionId: '2',
      jamiyaId: '2',
      memberId: 'user1',
      amount: 3000,
      currency: 'SAR',
      dueDate: '2024-06-01',
      paidDate: '2024-06-01',
      status: 'paid',
      paymentMethod: 'auto_debit',
      reference: 'AUTO-2024-002',
      remindersSent: 0,
      createdAt: '2024-05-01T10:00:00Z',
      updatedAt: '2024-06-01T10:00:00Z'
    },
    {
      id: '4',
      subscriptionId: '1',
      jamiyaId: '1',
      memberId: 'user1',
      amount: 5000,
      currency: 'SAR',
      dueDate: '2024-05-01',
      paidDate: '2024-05-03',
      status: 'paid',
      paymentMethod: 'bank_transfer',
      reference: 'TXN-2024-003',
      lateFee: 50,
      remindersSent: 2,
      lastReminderDate: '2024-05-02',
      createdAt: '2024-04-01T10:00:00Z',
      updatedAt: '2024-05-03T10:00:00Z'
    },
    {
      id: '5',
      subscriptionId: '3',
      jamiyaId: '3',
      memberId: 'user1',
      amount: 2000,
      currency: 'SAR',
      dueDate: '2024-04-01',
      status: 'missed',
      remindersSent: 3,
      lastReminderDate: '2024-04-05',
      createdAt: '2024-03-01T10:00:00Z',
      updatedAt: '2024-04-10T10:00:00Z'
    }
  ];

  useEffect(() => {
    const loadPayments = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPayments(mockPayments);
      setIsLoading(false);
    };

    loadPayments();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'late':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'missed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'paid': return 'مدفوع';
      case 'pending': return 'معلق';
      case 'late': return 'متأخر';
      case 'missed': return 'مفقود';
      default: return status;
    }
  };

  const getPaymentMethodText = (method?: string) => {
    if (!method) return '-';
    switch (method) {
      case 'cash': return 'نقداً';
      case 'bank_transfer': return 'تحويل بنكي';
      case 'online_payment': return 'دفع إلكتروني';
      case 'auto_debit': return 'خصم تلقائي';
      case 'check': return 'شيك';
      default: return method;
    }
  };

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.jamiyaId.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;
    const matchesDate = dateFilter === 'all' || 
                       (dateFilter === 'this_month' && 
                        new Date(payment.dueDate).getMonth() === new Date().getMonth());
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  const totalPaid = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0);
  const totalPending = payments.filter(p => p.status === 'pending').reduce((sum, p) => sum + p.amount, 0);
  const totalMissed = payments.filter(p => p.status === 'missed').length;
  const totalLateFees = payments.reduce((sum, p) => sum + (p.lateFee || 0), 0);

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['subscription_management']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل المدفوعات...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['subscription_management']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">منصة الجمعيات المالية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/subscriptions">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    الاشتراكات
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <BanknotesIcon className="h-8 w-8 text-blue-600 ml-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">سجل المدفوعات</h1>
                  <p className="text-gray-600">عرض وإدارة جميع مدفوعات الاشتراكات الشهرية</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Statistics Cards */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircleIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(totalPaid)}
                  </p>
                  <p className="text-sm text-gray-600">إجمالي المدفوع</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <ClockIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-yellow-600">
                    {formatCurrency(totalPending)}
                  </p>
                  <p className="text-sm text-gray-600">معلق</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <XCircleIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-red-600">{totalMissed}</p>
                  <p className="text-sm text-gray-600">مدفوعات مفقودة</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <ExclamationTriangleIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-orange-600">
                    {formatCurrency(totalLateFees)}
                  </p>
                  <p className="text-sm text-gray-600">غرامات التأخير</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البحث
                </label>
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="البحث بالمرجع أو رقم الجمعية..."
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الحالة
                </label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="paid">مدفوع</option>
                  <option value="pending">معلق</option>
                  <option value="late">متأخر</option>
                  <option value="missed">مفقود</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفترة الزمنية
                </label>
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">جميع الفترات</option>
                  <option value="this_month">هذا الشهر</option>
                  <option value="last_month">الشهر الماضي</option>
                  <option value="this_year">هذا العام</option>
                </select>
              </div>
            </div>
          </motion.div>

          {/* Payments Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  المدفوعات ({filteredPayments.length})
                </h3>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button className="text-gray-600 hover:text-gray-900 p-2">
                    <FunnelIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الجمعية
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المبلغ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      تاريخ الاستحقاق
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      تاريخ الدفع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      طريقة الدفع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المرجع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredPayments.map((payment) => (
                    <tr key={payment.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="font-medium">جمعية رقم {payment.jamiyaId}</div>
                          <div className="text-xs text-gray-500">
                            اشتراك رقم {payment.subscriptionId}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <div>
                          {formatCurrency(payment.amount)}
                          {payment.lateFee && (
                            <div className="text-xs text-red-600">
                              + {formatCurrency(payment.lateFee)} غرامة
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(payment.dueDate).toLocaleDateString('ar-SA')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {payment.paidDate
                          ? new Date(payment.paidDate).toLocaleDateString('ar-SA')
                          : '-'
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {getPaymentMethodText(payment.paymentMethod)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {payment.reference || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getPaymentStatusIcon(payment.status)}
                          <span className="mr-2 text-sm">
                            {getPaymentStatusText(payment.status)}
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredPayments.length === 0 && (
              <div className="text-center py-12">
                <BanknotesIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مدفوعات</h3>
                <p className="text-gray-600">لم يتم العثور على مدفوعات تطابق المعايير المحددة</p>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
