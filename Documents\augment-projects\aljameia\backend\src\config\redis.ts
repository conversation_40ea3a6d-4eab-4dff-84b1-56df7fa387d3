import { createClient, RedisClientType } from 'redis';
import { logger } from '@/utils/logger';

// Redis client instance
export let redisClient: RedisClientType;

// Initialize Redis client
export const connectRedis = async (): Promise<RedisClientType> => {
  try {
    redisClient = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      socket: {
        connectTimeout: 60000,
        lazyConnect: true,
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            logger.error('Redis connection failed after 10 retries');
            return new Error('Redis connection failed');
          }
          return Math.min(retries * 50, 1000);
        },
      },
    });

    // Error handling
    redisClient.on('error', (error) => {
      logger.error('Redis client error:', error);
    });

    redisClient.on('connect', () => {
      logger.info('Redis client connected');
    });

    redisClient.on('ready', () => {
      logger.info('Redis client ready');
    });

    redisClient.on('end', () => {
      logger.info('Redis client disconnected');
    });

    redisClient.on('reconnecting', () => {
      logger.info('Redis client reconnecting');
    });

    await redisClient.connect();
    return redisClient;
  } catch (error) {
    logger.error('Failed to initialize Redis:', error);
    throw error;
  }
};

// Redis cache manager
export class CacheManager {
  private static client: RedisClientType = redisClient;

  // Set cache with TTL
  static async set(
    key: string,
    value: any,
    ttl: number = 3600
  ): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      await this.client.setEx(key, ttl, serializedValue);
    } catch (error) {
      logger.error('Cache set error:', error);
      throw error;
    }
  }

  // Get cache value
  static async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Cache get error:', error);
      return null;
    }
  }

  // Delete cache key
  static async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error) {
      logger.error('Cache delete error:', error);
      throw error;
    }
  }

  // Delete multiple keys
  static async delMany(keys: string[]): Promise<void> {
    try {
      if (keys.length > 0) {
        await this.client.del(keys);
      }
    } catch (error) {
      logger.error('Cache delete many error:', error);
      throw error;
    }
  }

  // Check if key exists
  static async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Cache exists error:', error);
      return false;
    }
  }

  // Set cache with expiration time
  static async setWithExpiry(
    key: string,
    value: any,
    expiryDate: Date
  ): Promise<void> {
    try {
      const ttl = Math.floor((expiryDate.getTime() - Date.now()) / 1000);
      if (ttl > 0) {
        await this.set(key, value, ttl);
      }
    } catch (error) {
      logger.error('Cache set with expiry error:', error);
      throw error;
    }
  }

  // Increment counter
  static async increment(key: string, increment: number = 1): Promise<number> {
    try {
      return await this.client.incrBy(key, increment);
    } catch (error) {
      logger.error('Cache increment error:', error);
      throw error;
    }
  }

  // Set hash field
  static async hSet(key: string, field: string, value: any): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      await this.client.hSet(key, field, serializedValue);
    } catch (error) {
      logger.error('Cache hSet error:', error);
      throw error;
    }
  }

  // Get hash field
  static async hGet<T>(key: string, field: string): Promise<T | null> {
    try {
      const value = await this.client.hGet(key, field);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Cache hGet error:', error);
      return null;
    }
  }

  // Get all hash fields
  static async hGetAll<T>(key: string): Promise<Record<string, T>> {
    try {
      const hash = await this.client.hGetAll(key);
      const result: Record<string, T> = {};
      
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value);
      }
      
      return result;
    } catch (error) {
      logger.error('Cache hGetAll error:', error);
      return {};
    }
  }

  // Delete hash field
  static async hDel(key: string, field: string): Promise<void> {
    try {
      await this.client.hDel(key, field);
    } catch (error) {
      logger.error('Cache hDel error:', error);
      throw error;
    }
  }

  // Add to set
  static async sAdd(key: string, member: string): Promise<void> {
    try {
      await this.client.sAdd(key, member);
    } catch (error) {
      logger.error('Cache sAdd error:', error);
      throw error;
    }
  }

  // Check if member exists in set
  static async sIsMember(key: string, member: string): Promise<boolean> {
    try {
      return await this.client.sIsMember(key, member);
    } catch (error) {
      logger.error('Cache sIsMember error:', error);
      return false;
    }
  }

  // Remove from set
  static async sRem(key: string, member: string): Promise<void> {
    try {
      await this.client.sRem(key, member);
    } catch (error) {
      logger.error('Cache sRem error:', error);
      throw error;
    }
  }

  // Get all set members
  static async sMembers(key: string): Promise<string[]> {
    try {
      return await this.client.sMembers(key);
    } catch (error) {
      logger.error('Cache sMembers error:', error);
      return [];
    }
  }

  // Push to list
  static async lPush(key: string, value: any): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      await this.client.lPush(key, serializedValue);
    } catch (error) {
      logger.error('Cache lPush error:', error);
      throw error;
    }
  }

  // Pop from list
  static async lPop<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.lPop(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Cache lPop error:', error);
      return null;
    }
  }

  // Get list range
  static async lRange<T>(key: string, start: number, stop: number): Promise<T[]> {
    try {
      const values = await this.client.lRange(key, start, stop);
      return values.map(value => JSON.parse(value));
    } catch (error) {
      logger.error('Cache lRange error:', error);
      return [];
    }
  }

  // Clear all cache
  static async flushAll(): Promise<void> {
    try {
      await this.client.flushAll();
    } catch (error) {
      logger.error('Cache flush all error:', error);
      throw error;
    }
  }

  // Get cache info
  static async info(): Promise<string> {
    try {
      return await this.client.info();
    } catch (error) {
      logger.error('Cache info error:', error);
      return '';
    }
  }

  // Health check
  static async ping(): Promise<boolean> {
    try {
      const result = await this.client.ping();
      return result === 'PONG';
    } catch (error) {
      logger.error('Cache ping error:', error);
      return false;
    }
  }
}

// Cache key generators
export class CacheKeys {
  static user(userId: string): string {
    return `user:${userId}`;
  }

  static userProfile(userId: string): string {
    return `user:profile:${userId}`;
  }

  static userSessions(userId: string): string {
    return `user:sessions:${userId}`;
  }

  static member(memberId: string): string {
    return `member:${memberId}`;
  }

  static memberDashboard(memberId: string): string {
    return `member:dashboard:${memberId}`;
  }

  static contributions(memberId: string): string {
    return `contributions:${memberId}`;
  }

  static loans(memberId: string): string {
    return `loans:${memberId}`;
  }

  static transactions(memberId: string): string {
    return `transactions:${memberId}`;
  }

  static notifications(userId: string): string {
    return `notifications:${userId}`;
  }

  static reports(type: string, params: string): string {
    return `reports:${type}:${params}`;
  }

  static rateLimit(identifier: string): string {
    return `rate_limit:${identifier}`;
  }

  static session(sessionId: string): string {
    return `session:${sessionId}`;
  }

  static twoFactorAttempts(userId: string): string {
    return `2fa_attempts:${userId}`;
  }

  static passwordResetToken(token: string): string {
    return `password_reset:${token}`;
  }

  static emailVerificationToken(token: string): string {
    return `email_verification:${token}`;
  }
}

// Initialize Redis on module load
connectRedis().catch((error) => {
  logger.error('Failed to initialize Redis on startup:', error);
});

export { redisClient };
