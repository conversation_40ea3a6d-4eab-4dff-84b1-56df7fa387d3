import React from 'react';
import { renderHook, act } from '@testing-library/react-hooks';
import { Alert } from 'react-native';
import { AuthProvider, useAuth } from '../../contexts/AuthContext';
import { authService } from '../../services/authService';
import { biometricService } from '../../services/biometricService';

// Mock dependencies
jest.mock('../../services/authService');
jest.mock('../../services/biometricService');
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
}));

const mockAuthService = authService as jest.Mocked<typeof authService>;
const mockBiometricService = biometricService as jest.Mocked<typeof biometricService>;
const mockAlert = Alert.alert as jest.MockedFunction<typeof Alert.alert>;

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider>{children}</AuthProvider>
);

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    mockBiometricService.initialize.mockResolvedValue();
    mockBiometricService.isBiometricEnabled.mockReturnValue(false);
    mockAuthService.isAuthenticated.mockResolvedValue(false);
    mockAuthService.getStoredUser.mockResolvedValue(null);
  });

  describe('initialization', () => {
    it('should initialize with default state', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });

      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isBiometricEnabled).toBe(false);
      expect(result.current.isLoading).toBe(true);

      await waitForNextUpdate();

      expect(result.current.isLoading).toBe(false);
      expect(mockBiometricService.initialize).toHaveBeenCalled();
    });

    it('should restore authenticated user on initialization', async () => {
      const mockUser = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      mockAuthService.isAuthenticated.mockResolvedValue(true);
      mockAuthService.getStoredUser.mockResolvedValue(mockUser);
      mockBiometricService.isBiometricEnabled.mockReturnValue(true);

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });

      await waitForNextUpdate();

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.isBiometricEnabled).toBe(true);
    });
  });

  describe('login', () => {
    const mockCredentials = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const mockAuthResponse = {
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
    };

    it('should login successfully', async () => {
      mockAuthService.login.mockResolvedValue(mockAuthResponse);

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });
      
      await waitForNextUpdate(); // Wait for initialization

      await act(async () => {
        await result.current.login(mockCredentials);
      });

      expect(mockAuthService.login).toHaveBeenCalledWith(mockCredentials);
      expect(result.current.user).toEqual(mockAuthResponse.user);
      expect(result.current.isAuthenticated).toBe(true);
      expect(mockAlert).toHaveBeenCalledWith('نجح تسجيل الدخول', 'مرحباً Test User');
    });

    it('should handle login error', async () => {
      const error = new Error('Invalid credentials');
      mockAuthService.login.mockRejectedValue(error);

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });
      
      await waitForNextUpdate(); // Wait for initialization

      await act(async () => {
        try {
          await result.current.login(mockCredentials);
        } catch (e) {
          // Expected to throw
        }
      });

      expect(mockAlert).toHaveBeenCalledWith(
        'خطأ في تسجيل الدخول',
        'Invalid credentials'
      );
      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
    });

    it('should store biometric credentials when enabled', async () => {
      mockAuthService.login.mockResolvedValue(mockAuthResponse);
      mockBiometricService.isBiometricEnabled.mockReturnValue(true);

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });
      
      await waitForNextUpdate(); // Wait for initialization

      await act(async () => {
        await result.current.login(mockCredentials);
      });

      expect(mockBiometricService.storeCredentials).toHaveBeenCalledWith({
        email: mockCredentials.email,
        token: mockAuthResponse.accessToken,
      });
    });
  });

  describe('loginWithBiometric', () => {
    it('should login with biometric successfully', async () => {
      const mockUser = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      mockBiometricService.isBiometricEnabled.mockReturnValue(true);
      mockBiometricService.authenticate.mockResolvedValue({ success: true });
      mockBiometricService.getStoredCredentials.mockResolvedValue({
        email: '<EMAIL>',
        token: 'stored-token',
      });
      mockAuthService.getCurrentUser.mockResolvedValue(mockUser);

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });
      
      await waitForNextUpdate(); // Wait for initialization

      await act(async () => {
        await result.current.loginWithBiometric();
      });

      expect(mockBiometricService.authenticate).toHaveBeenCalledWith('تسجيل الدخول');
      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
    });

    it('should handle biometric authentication failure', async () => {
      mockBiometricService.isBiometricEnabled.mockReturnValue(true);
      mockBiometricService.authenticate.mockResolvedValue({
        success: false,
        error: 'Authentication failed',
      });

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });
      
      await waitForNextUpdate(); // Wait for initialization

      await act(async () => {
        try {
          await result.current.loginWithBiometric();
        } catch (e) {
          // Expected to throw
        }
      });

      expect(mockAlert).toHaveBeenCalledWith(
        'خطأ في المصادقة البيومترية',
        'Authentication failed'
      );
    });

    it('should fail when biometric is not enabled', async () => {
      mockBiometricService.isBiometricEnabled.mockReturnValue(false);

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });
      
      await waitForNextUpdate(); // Wait for initialization

      await act(async () => {
        try {
          await result.current.loginWithBiometric();
        } catch (e) {
          // Expected to throw
        }
      });

      expect(mockAlert).toHaveBeenCalledWith(
        'خطأ في المصادقة البيومترية',
        'المصادقة البيومترية غير مفعلة'
      );
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      // First login
      const mockAuthResponse = {
        user: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user',
          isVerified: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        },
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
      };

      mockAuthService.login.mockResolvedValue(mockAuthResponse);
      mockAuthService.logout.mockResolvedValue();

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });
      
      await waitForNextUpdate(); // Wait for initialization

      // Login first
      await act(async () => {
        await result.current.login({
          email: '<EMAIL>',
          password: 'password123',
        });
      });

      // Then logout
      await act(async () => {
        await result.current.logout();
      });

      expect(mockAuthService.logout).toHaveBeenCalled();
      expect(mockBiometricService.clearAllData).toHaveBeenCalled();
      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isBiometricEnabled).toBe(false);
      expect(mockAlert).toHaveBeenCalledWith('تم تسجيل الخروج', 'تم تسجيل خروجك بنجاح');
    });
  });

  describe('enableBiometric', () => {
    it('should enable biometric successfully', async () => {
      mockBiometricService.enableBiometric.mockResolvedValue(true);

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });
      
      await waitForNextUpdate(); // Wait for initialization

      let success: boolean;
      await act(async () => {
        success = await result.current.enableBiometric();
      });

      expect(success!).toBe(true);
      expect(result.current.isBiometricEnabled).toBe(true);
      expect(mockAlert).toHaveBeenCalledWith('تم التفعيل', 'تم تفعيل المصادقة البيومترية بنجاح');
    });

    it('should handle biometric enable failure', async () => {
      mockBiometricService.enableBiometric.mockResolvedValue(false);

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });
      
      await waitForNextUpdate(); // Wait for initialization

      let success: boolean;
      await act(async () => {
        success = await result.current.enableBiometric();
      });

      expect(success!).toBe(false);
      expect(result.current.isBiometricEnabled).toBe(false);
    });
  });

  describe('disableBiometric', () => {
    it('should disable biometric successfully', async () => {
      mockBiometricService.disableBiometric.mockResolvedValue();

      const { result, waitForNextUpdate } = renderHook(() => useAuth(), { wrapper });
      
      await waitForNextUpdate(); // Wait for initialization

      await act(async () => {
        await result.current.disableBiometric();
      });

      expect(mockBiometricService.disableBiometric).toHaveBeenCalled();
      expect(result.current.isBiometricEnabled).toBe(false);
      expect(mockAlert).toHaveBeenCalledWith('تم الإيقاف', 'تم إيقاف المصادقة البيومترية');
    });
  });
});
