/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,CreditCardIcon,DocumentTextIcon,GlobeAltIcon,ShieldCheckIcon,UsersIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,CreditCardIcon,DocumentTextIcon,GlobeAltIcon,ShieldCheckIcon,UsersIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightIcon: () => (/* reexport safe */ _ArrowRightIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   CheckCircleIcon: () => (/* reexport safe */ _CheckCircleIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   CreditCardIcon: () => (/* reexport safe */ _CreditCardIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   DocumentTextIcon: () => (/* reexport safe */ _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   GlobeAltIcon: () => (/* reexport safe */ _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   ShieldCheckIcon: () => (/* reexport safe */ _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   UsersIcon: () => (/* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _CheckCircleIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CheckCircleIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _CreditCardIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CreditCardIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DocumentTextIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./GlobeAltIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ShieldCheckIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./UsersIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0SWNvbixDaGFydEJhckljb24sQ2hlY2tDaXJjbGVJY29uLENyZWRpdENhcmRJY29uLERvY3VtZW50VGV4dEljb24sR2xvYmVBbHRJY29uLFNoaWVsZENoZWNrSWNvbixVc2Vyc0ljb24hPSEuLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUMrRDtBQUNKO0FBQ007QUFDRjtBQUNJO0FBQ1I7QUFDTSIsInNvdXJjZXMiOlsid2VicGFjazovL0BhbGphbWVpYS9mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9mZTFlIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1JpZ2h0SWNvbiB9IGZyb20gXCIuL0Fycm93UmlnaHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hhcnRCYXJJY29uIH0gZnJvbSBcIi4vQ2hhcnRCYXJJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hlY2tDaXJjbGVJY29uIH0gZnJvbSBcIi4vQ2hlY2tDaXJjbGVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ3JlZGl0Q2FyZEljb24gfSBmcm9tIFwiLi9DcmVkaXRDYXJkSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERvY3VtZW50VGV4dEljb24gfSBmcm9tIFwiLi9Eb2N1bWVudFRleHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmVBbHRJY29uIH0gZnJvbSBcIi4vR2xvYmVBbHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hpZWxkQ2hlY2tJY29uIH0gZnJvbSBcIi4vU2hpZWxkQ2hlY2tJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlcnNJY29uIH0gZnJvbSBcIi4vVXNlcnNJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,CreditCardIcon,DocumentTextIcon,GlobeAltIcon,ShieldCheckIcon,UsersIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,Cog6ToothIcon,UserCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,Cog6ToothIcon,UserCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightOnRectangleIcon: () => (/* reexport safe */ _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   UserCircleIcon: () => (/* reexport safe */ _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightOnRectangleIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Bars3Icon.js */ \"../node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserCircleIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./XMarkIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLEJhcnMzSWNvbixDb2c2VG9vdGhJY29uLFVzZXJDaXJjbGVJY29uLFhNYXJrSWNvbiE9IS4uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ3FGO0FBQ2hDO0FBQ1E7QUFDRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BhbGphbWVpYS9mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9mN2YwIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uIH0gZnJvbSBcIi4vQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJhcnMzSWNvbiB9IGZyb20gXCIuL0JhcnMzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENvZzZUb290aEljb24gfSBmcm9tIFwiLi9Db2c2VG9vdGhJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckNpcmNsZUljb24gfSBmcm9tIFwiLi9Vc2VyQ2lyY2xlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,Cog6ToothIcon,UserCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BellIcon,CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,CreditCardIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UsersIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*****************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BellIcon,CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,CreditCardIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UsersIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalendarIcon: () => (/* reexport safe */ _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ClipboardDocumentListIcon: () => (/* reexport safe */ _ClipboardDocumentListIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   CreditCardIcon: () => (/* reexport safe */ _CreditCardIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   DocumentTextIcon: () => (/* reexport safe */ _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   UserCircleIcon: () => (/* reexport safe */ _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   UsersIcon: () => (/* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BellIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalendarIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _ClipboardDocumentListIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ClipboardDocumentListIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _CreditCardIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreditCardIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DocumentTextIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./HomeIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UserCircleIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./UsersIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CZWxsSWNvbixDYWxlbmRhckljb24sQ2hhcnRCYXJJY29uLENsaXBib2FyZERvY3VtZW50TGlzdEljb24sQ29nNlRvb3RoSWNvbixDcmVkaXRDYXJkSWNvbixEb2N1bWVudFRleHRJY29uLEhvbWVJY29uLFVzZXJDaXJjbGVJY29uLFVzZXJzSWNvbiE9IS4uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNtRDtBQUNRO0FBQ0E7QUFDMEI7QUFDeEI7QUFDRTtBQUNJO0FBQ2hCO0FBQ1kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWxqYW1laWEvZnJvbnRlbmQvLi4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/NGJlMCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmVsbEljb24gfSBmcm9tIFwiLi9CZWxsSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGVuZGFySWNvbiB9IGZyb20gXCIuL0NhbGVuZGFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXJ0QmFySWNvbiB9IGZyb20gXCIuL0NoYXJ0QmFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENsaXBib2FyZERvY3VtZW50TGlzdEljb24gfSBmcm9tIFwiLi9DbGlwYm9hcmREb2N1bWVudExpc3RJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29nNlRvb3RoSWNvbiB9IGZyb20gXCIuL0NvZzZUb290aEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDcmVkaXRDYXJkSWNvbiB9IGZyb20gXCIuL0NyZWRpdENhcmRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRG9jdW1lbnRUZXh0SWNvbiB9IGZyb20gXCIuL0RvY3VtZW50VGV4dEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIb21lSWNvbiB9IGZyb20gXCIuL0hvbWVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckNpcmNsZUljb24gfSBmcm9tIFwiLi9Vc2VyQ2lyY2xlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJzSWNvbiB9IGZyb20gXCIuL1VzZXJzSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BellIcon,CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,CreditCardIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UsersIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BellIcon,CheckIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BellIcon,CheckIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CheckIcon: () => (/* reexport safe */ _CheckIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ExclamationTriangleIcon: () => (/* reexport safe */ _ExclamationTriangleIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   InformationCircleIcon: () => (/* reexport safe */ _InformationCircleIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BellIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _CheckIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CheckIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _ExclamationTriangleIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ExclamationTriangleIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _InformationCircleIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InformationCircleIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./XMarkIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CZWxsSWNvbixDaGVja0ljb24sRXhjbGFtYXRpb25UcmlhbmdsZUljb24sSW5mb3JtYXRpb25DaXJjbGVJY29uLFhNYXJrSWNvbiE9IS4uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ21EO0FBQ0U7QUFDNEI7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhbGphbWVpYS9mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9hZTlhIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWxsSWNvbiB9IGZyb20gXCIuL0JlbGxJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hlY2tJY29uIH0gZnJvbSBcIi4vQ2hlY2tJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXhjbGFtYXRpb25UcmlhbmdsZUljb24gfSBmcm9tIFwiLi9FeGNsYW1hdGlvblRyaWFuZ2xlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEluZm9ybWF0aW9uQ2lyY2xlSWNvbiB9IGZyb20gXCIuL0luZm9ybWF0aW9uQ2lyY2xlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BellIcon,CheckIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ChevronDownIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronDownIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChevronDownIcon: () => (/* reexport safe */ _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChevronDownIcon.js */ "../node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js");



/***/ }),

/***/ "__barrel_optimize__?names=EnvelopeIcon,GlobeAltIcon,MapPinIcon,PhoneIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=EnvelopeIcon,GlobeAltIcon,MapPinIcon,PhoneIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnvelopeIcon: () => (/* reexport safe */ _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   GlobeAltIcon: () => (/* reexport safe */ _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MapPinIcon: () => (/* reexport safe */ _MapPinIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   PhoneIcon: () => (/* reexport safe */ _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EnvelopeIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./GlobeAltIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _MapPinIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MapPinIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PhoneIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FbnZlbG9wZUljb24sR2xvYmVBbHRJY29uLE1hcFBpbkljb24sUGhvbmVJY29uIT0hLi4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQzJEO0FBQ0E7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhbGphbWVpYS9mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz85ZDg5Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFbnZlbG9wZUljb24gfSBmcm9tIFwiLi9FbnZlbG9wZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iZUFsdEljb24gfSBmcm9tIFwiLi9HbG9iZUFsdEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYXBQaW5JY29uIH0gZnJvbSBcIi4vTWFwUGluSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBob25lSWNvbiB9IGZyb20gXCIuL1Bob25lSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=EnvelopeIcon,GlobeAltIcon,MapPinIcon,PhoneIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Menu,Transition!=!../node_modules/@headlessui/react/dist/headlessui.esm.js":
/*!************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Menu,Transition!=!../node_modules/@headlessui/react/dist/headlessui.esm.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Menu: () => (/* reexport safe */ C_Users_kalif_Documents_augment_projects_aljameia_node_modules_headlessui_react_dist_components_menu_menu_js__WEBPACK_IMPORTED_MODULE_0__.Menu),\n/* harmony export */   Transition: () => (/* reexport safe */ C_Users_kalif_Documents_augment_projects_aljameia_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_1__.Transition)\n/* harmony export */ });\n/* harmony import */ var C_Users_kalif_Documents_augment_projects_aljameia_node_modules_headlessui_react_dist_components_menu_menu_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/@headlessui/react/dist/components/menu/menu.js */ \"../node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var C_Users_kalif_Documents_augment_projects_aljameia_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../node_modules/@headlessui/react/dist/components/transitions/transition.js */ \"../node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NZW51LFRyYW5zaXRpb24hPSEuLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9oZWFkbGVzc3VpLmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNpSiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhbGphbWVpYS9mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9oZWFkbGVzc3VpLmVzbS5qcz8yZmI0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgTWVudSB9IGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxrYWxpZlxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxhbGphbWVpYVxcXFxub2RlX21vZHVsZXNcXFxcQGhlYWRsZXNzdWlcXFxccmVhY3RcXFxcZGlzdFxcXFxjb21wb25lbnRzXFxcXG1lbnVcXFxcbWVudS5qc1wiXG5leHBvcnQgeyBUcmFuc2l0aW9uIH0gZnJvbSBcIkM6XFxcXFVzZXJzXFxcXGthbGlmXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGFsamFtZWlhXFxcXG5vZGVfbW9kdWxlc1xcXFxAaGVhZGxlc3N1aVxcXFxyZWFjdFxcXFxkaXN0XFxcXGNvbXBvbmVudHNcXFxcdHJhbnNpdGlvbnNcXFxcdHJhbnNpdGlvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Menu,Transition!=!../node_modules/@headlessui/react/dist/headlessui.esm.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=formatDistanceToNow!=!../node_modules/date-fns/esm/index.js":
/*!*********************************************************************************************!*\
  !*** __barrel_optimize__?names=formatDistanceToNow!=!../node_modules/date-fns/esm/index.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   formatDistanceToNow: () => (/* reexport safe */ _formatDistanceToNow_index_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _formatDistanceToNow_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDistanceToNow/index.js */ "../node_modules/date-fns/esm/formatDistanceToNow/index.js");



/***/ }),

/***/ "../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"./src/pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,GlobeAltIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=EnvelopeIcon,GlobeAltIcon,MapPinIcon,PhoneIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\nconst Footer = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"common\");\n    const footerLinks = {\n        product: [\n            {\n                name: t(\"footer.product.features\"),\n                href: \"/features\"\n            },\n            {\n                name: t(\"footer.product.pricing\"),\n                href: \"/pricing\"\n            },\n            {\n                name: t(\"footer.product.demo\"),\n                href: \"/demo\"\n            },\n            {\n                name: t(\"footer.product.api\"),\n                href: \"/api-docs\"\n            }\n        ],\n        company: [\n            {\n                name: t(\"footer.company.about\"),\n                href: \"/about\"\n            },\n            {\n                name: t(\"footer.company.careers\"),\n                href: \"/careers\"\n            },\n            {\n                name: t(\"footer.company.blog\"),\n                href: \"/blog\"\n            },\n            {\n                name: t(\"footer.company.press\"),\n                href: \"/press\"\n            }\n        ],\n        support: [\n            {\n                name: t(\"footer.support.help_center\"),\n                href: \"/help\"\n            },\n            {\n                name: t(\"footer.support.contact\"),\n                href: \"/contact\"\n            },\n            {\n                name: t(\"footer.support.status\"),\n                href: \"/status\"\n            },\n            {\n                name: t(\"footer.support.community\"),\n                href: \"/community\"\n            }\n        ],\n        legal: [\n            {\n                name: t(\"footer.legal.privacy\"),\n                href: \"/privacy\"\n            },\n            {\n                name: t(\"footer.legal.terms\"),\n                href: \"/terms\"\n            },\n            {\n                name: t(\"footer.legal.security\"),\n                href: \"/security\"\n            },\n            {\n                name: t(\"footer.legal.compliance\"),\n                href: \"/compliance\"\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: \"Twitter\",\n            href: \"#\",\n            icon: \"\\uD83D\\uDC26\"\n        },\n        {\n            name: \"LinkedIn\",\n            href: \"#\",\n            icon: \"\\uD83D\\uDCBC\"\n        },\n        {\n            name: \"Facebook\",\n            href: \"#\",\n            icon: \"\\uD83D\\uDCD8\"\n        },\n        {\n            name: \"Instagram\",\n            href: \"#\",\n            icon: \"\\uD83D\\uDCF7\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-sm\",\n                                                children: \"AJ\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3 text-xl font-bold\",\n                                            children: t(\"brand.name\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 max-w-md\",\n                                    children: t(\"footer.description\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.EnvelopeIcon, {\n                                                    className: \"h-5 w-5 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.PhoneIcon, {\n                                                    className: \"h-5 w-5 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+966 11 123 4567\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.MapPinIcon, {\n                                                    className: \"h-5 w-5 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: t(\"footer.address\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.GlobeAltIcon, {\n                                                    className: \"h-5 w-5 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"www.aljameia.com\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold text-white uppercase tracking-wider mb-4\",\n                                    children: t(\"footer.product.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.product.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold text-white uppercase tracking-wider mb-4\",\n                                    children: t(\"footer.company.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.company.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold text-white uppercase tracking-wider mb-4\",\n                                    children: t(\"footer.support.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.support.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold text-white uppercase tracking-wider mb-4\",\n                                    children: t(\"footer.legal.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 pt-8 border-t border-gray-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    new Date().getFullYear(),\n                                    \" \",\n                                    t(\"brand.name\"),\n                                    \". \",\n                                    t(\"footer.rights_reserved\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 mt-4 md:mt-0\",\n                                children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: social.href,\n                                        className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                        \"aria-label\": social.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl\",\n                                            children: social.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, social.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Menu,Transition!=!../node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_Cog6ToothIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,Cog6ToothIcon,UserCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,Cog6ToothIcon,UserCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.ts\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Button */ \"./src/components/ui/Button.tsx\");\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./LanguageSwitcher */ \"./src/components/layout/LanguageSwitcher.tsx\");\n/* harmony import */ var _NotificationDropdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./NotificationDropdown */ \"./src/components/layout/NotificationDropdown.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__]);\n_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\n\n\nconst Header = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, isAuthenticated, logout } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigation = [\n        {\n            name: t(\"navigation.home\"),\n            href: \"/\"\n        },\n        {\n            name: t(\"navigation.features\"),\n            href: \"/features\"\n        },\n        {\n            name: t(\"navigation.pricing\"),\n            href: \"/pricing\"\n        },\n        {\n            name: t(\"navigation.about\"),\n            href: \"/about\"\n        },\n        {\n            name: t(\"navigation.contact\"),\n            href: \"/contact\"\n        }\n    ];\n    const userNavigation = [\n        {\n            name: t(\"user_menu.profile\"),\n            href: \"/profile\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_Cog6ToothIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.UserCircleIcon\n        },\n        {\n            name: t(\"user_menu.settings\"),\n            href: \"/settings\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_Cog6ToothIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.Cog6ToothIcon\n        }\n    ];\n    const handleLogout = async ()=>{\n        await logout();\n        router.push(\"/\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n            \"aria-label\": \"Top\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-sm\",\n                                                children: \"AJ\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: t(\"brand.name\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: item.href,\n                                        className: `text-sm font-medium transition-colors duration-200 ${router.pathname === item.href ? \"text-blue-600\" : \"text-gray-700 hover:text-blue-600\"}`,\n                                        children: item.name\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationDropdown__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_11__.Menu, {\n                                            as: \"div\",\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_11__.Menu.Button, {\n                                                        className: \"flex max-w-xs items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sr-only\",\n                                                                children: t(\"user_menu.open\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 99,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            user?.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                className: \"h-8 w-8 rounded-full\",\n                                                                src: user.avatar,\n                                                                alt: user.name,\n                                                                width: 32,\n                                                                height: 32\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_Cog6ToothIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.UserCircleIcon, {\n                                                                className: \"h-8 w-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 109,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_11__.Transition, {\n                                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                                    enter: \"transition ease-out duration-100\",\n                                                    enterFrom: \"transform opacity-0 scale-95\",\n                                                    enterTo: \"transform opacity-100 scale-100\",\n                                                    leave: \"transition ease-in duration-75\",\n                                                    leaveFrom: \"transform opacity-100 scale-100\",\n                                                    leaveTo: \"transform opacity-0 scale-95\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_11__.Menu.Items, {\n                                                        className: \"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-4 py-2 border-b border-gray-100\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: user?.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                        lineNumber: 124,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: user?.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                        lineNumber: 125,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            userNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_11__.Menu.Item, {\n                                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                                            href: item.href,\n                                                                            className: `${active ? \"bg-gray-100\" : \"\"} flex items-center px-4 py-2 text-sm text-gray-700`,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                                    className: \"mr-3 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                                    lineNumber: 137,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                item.name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                            lineNumber: 131,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                }, item.name, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                    lineNumber: 129,\n                                                                    columnNumber: 25\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_11__.Menu.Item, {\n                                                                children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleLogout,\n                                                                        className: `${active ? \"bg-gray-100\" : \"\"} flex w-full items-center px-4 py-2 text-sm text-gray-700`,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_Cog6ToothIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.ArrowRightOnRectangleIcon, {\n                                                                                className: \"mr-3 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                                lineNumber: 152,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            t(\"user_menu.logout\")\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                        lineNumber: 146,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/auth/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                children: t(\"auth.login\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/auth/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                size: \"sm\",\n                                                children: t(\"auth.register\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: t(\"navigation.open_menu\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_Cog6ToothIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.XMarkIcon, {\n                                                className: \"block h-6 w-6\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_Cog6ToothIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.Bars3Icon, {\n                                                className: \"block h-6 w-6\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_11__.Transition, {\n                    show: mobileMenuOpen,\n                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                    enter: \"duration-150 ease-out\",\n                    enterFrom: \"opacity-0 scale-95\",\n                    enterTo: \"opacity-100 scale-100\",\n                    leave: \"duration-100 ease-in\",\n                    leaveFrom: \"opacity-100 scale-100\",\n                    leaveTo: \"opacity-0 scale-95\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1 pb-3 pt-2\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: item.href,\n                                    className: `block px-3 py-2 text-base font-medium ${router.pathname === item.href ? \"text-blue-600 bg-blue-50\" : \"text-gray-700 hover:text-blue-600 hover:bg-gray-50\"}`,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "./src/components/layout/LanguageSwitcher.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/LanguageSwitcher.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Menu,Transition!=!../node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChevronDownIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\n\n\nconst languages = [\n    {\n        code: \"en\",\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n    },\n    {\n        code: \"ar\",\n        name: \"العربية\",\n        flag: \"\\uD83C\\uDDF8\\uD83C\\uDDE6\"\n    }\n];\nconst LanguageSwitcher = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const currentLanguage = languages.find((lang)=>lang.code === router.locale) || languages[0];\n    const handleLanguageChange = (locale)=>{\n        router.push(router.asPath, router.asPath, {\n            locale\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__.Menu, {\n        as: \"div\",\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__.Menu.Button, {\n                    className: \"flex items-center rounded-md p-2 text-sm font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-2\",\n                            children: currentLanguage.flag\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden sm:block\",\n                            children: currentLanguage.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChevronDownIcon, {\n                            className: \"ml-1 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__.Transition, {\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                enter: \"transition ease-out duration-100\",\n                enterFrom: \"transform opacity-0 scale-95\",\n                enterTo: \"transform opacity-100 scale-100\",\n                leave: \"transition ease-in duration-75\",\n                leaveFrom: \"transform opacity-100 scale-100\",\n                leaveTo: \"transform opacity-0 scale-95\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__.Menu.Items, {\n                    className: \"absolute right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                    children: languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__.Menu.Item, {\n                            children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleLanguageChange(language.code),\n                                    className: `${active ? \"bg-gray-100\" : \"\"} ${language.code === router.locale ? \"bg-blue-50 text-blue-600\" : \"text-gray-700\"} flex w-full items-center px-4 py-2 text-sm`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-3\",\n                                            children: language.flag\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        language.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 17\n                                }, undefined)\n                        }, language.code, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\LanguageSwitcher.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageSwitcher);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/LanguageSwitcher.tsx\n");

/***/ }),

/***/ "./src/components/layout/Layout.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Layout.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Header */ \"./src/components/layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Footer */ \"./src/components/layout/Footer.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Header__WEBPACK_IMPORTED_MODULE_2__]);\n_Header__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst Layout = ({ children, showSidebar = false, className = \"\" })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"common\");\n    const { user, isAuthenticated } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-gray-50 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    showSidebar && isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: `flex-1 ${showSidebar && isAuthenticated ? \"ml-64\" : \"\"}`,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/Layout.tsx\n");

/***/ }),

/***/ "./src/components/layout/NotificationDropdown.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/NotificationDropdown.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Menu,Transition!=!../node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BellIcon,CheckIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"__barrel_optimize__?names=formatDistanceToNow!=!../node_modules/date-fns/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\n\nconst NotificationDropdown = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            type: \"info\",\n            title: t(\"notifications.payment_reminder.title\"),\n            message: t(\"notifications.payment_reminder.message\"),\n            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n            read: false,\n            actionUrl: \"/payments\"\n        },\n        {\n            id: \"2\",\n            type: \"success\",\n            title: t(\"notifications.document_approved.title\"),\n            message: t(\"notifications.document_approved.message\"),\n            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),\n            read: false,\n            actionUrl: \"/documents\"\n        },\n        {\n            id: \"3\",\n            type: \"warning\",\n            title: t(\"notifications.membership_expiring.title\"),\n            message: t(\"notifications.membership_expiring.message\"),\n            timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),\n            read: true,\n            actionUrl: \"/membership\"\n        }\n    ]);\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CheckIcon, {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, undefined);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ExclamationTriangleIcon, {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, undefined);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.XMarkIcon, {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.InformationCircleIcon, {\n                    className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const markAsRead = (id)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === id ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    };\n    const markAllAsRead = ()=>{\n        setNotifications((prev)=>prev.map((notification)=>({\n                    ...notification,\n                    read: true\n                })));\n    };\n    const handleNotificationClick = (notification)=>{\n        markAsRead(notification.id);\n        if (notification.actionUrl) {\n            router.push(notification.actionUrl);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu, {\n        as: \"div\",\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Button, {\n                    className: \"relative rounded-full p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: t(\"notifications.open\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.BellIcon, {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined),\n                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-medium text-white\",\n                            children: unreadCount > 9 ? \"9+\" : unreadCount\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Transition, {\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                enter: \"transition ease-out duration-100\",\n                enterFrom: \"transform opacity-0 scale-95\",\n                enterTo: \"transform opacity-100 scale-100\",\n                leave: \"transition ease-in duration-75\",\n                leaveFrom: \"transform opacity-100 scale-100\",\n                leaveTo: \"transform opacity-0 scale-95\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Items, {\n                    className: \"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-3 border-b border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: t(\"notifications.title\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: markAllAsRead,\n                                        className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                        children: t(\"notifications.mark_all_read\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-96 overflow-y-auto\",\n                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-6 text-center text-sm text-gray-500\",\n                                children: t(\"notifications.no_notifications\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, undefined) : notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Item, {\n                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>handleNotificationClick(notification),\n                                            className: `${active ? \"bg-gray-50\" : \"\"} ${!notification.read ? \"bg-blue-50\" : \"\"} cursor-pointer px-4 py-3 border-b border-gray-100 last:border-b-0`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 mt-1\",\n                                                        children: getNotificationIcon(notification.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: `text-sm font-medium ${!notification.read ? \"text-gray-900\" : \"text-gray-700\"}`,\n                                                                        children: notification.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                        lineNumber: 158,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                        lineNumber: 164,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__.formatDistanceToNow)(notification.timestamp, {\n                                                                    addSuffix: true\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 21\n                                        }, undefined)\n                                }, notification.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined),\n                        notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-3 border-t border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/notifications\"),\n                                className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                                children: t(\"notifications.view_all\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationDropdown);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/NotificationDropdown.tsx\n");

/***/ }),

/***/ "./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,CreditCardIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BellIcon,CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,CreditCardIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UsersIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.ts\");\n\n\n\n\n\n\nconst Sidebar = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const navigation = [\n        {\n            name: t(\"sidebar.dashboard\"),\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.HomeIcon,\n            current: router.pathname === \"/dashboard\"\n        },\n        {\n            name: t(\"sidebar.financial\"),\n            href: \"/financial\",\n            icon: _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon,\n            current: router.pathname.startsWith(\"/financial\"),\n            children: [\n                {\n                    name: t(\"sidebar.transactions\"),\n                    href: \"/financial/transactions\"\n                },\n                {\n                    name: t(\"sidebar.reports\"),\n                    href: \"/financial/reports\"\n                },\n                {\n                    name: t(\"sidebar.budgets\"),\n                    href: \"/financial/budgets\"\n                }\n            ]\n        },\n        {\n            name: t(\"sidebar.members\"),\n            href: \"/members\",\n            icon: _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UsersIcon,\n            current: router.pathname.startsWith(\"/members\"),\n            children: [\n                {\n                    name: t(\"sidebar.member_list\"),\n                    href: \"/members\"\n                },\n                {\n                    name: t(\"sidebar.member_registration\"),\n                    href: \"/members/register\"\n                },\n                {\n                    name: t(\"sidebar.member_reports\"),\n                    href: \"/members/reports\"\n                }\n            ]\n        },\n        {\n            name: t(\"sidebar.documents\"),\n            href: \"/documents\",\n            icon: _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.DocumentTextIcon,\n            current: router.pathname.startsWith(\"/documents\"),\n            children: [\n                {\n                    name: t(\"sidebar.document_library\"),\n                    href: \"/documents\"\n                },\n                {\n                    name: t(\"sidebar.upload_document\"),\n                    href: \"/documents/upload\"\n                },\n                {\n                    name: t(\"sidebar.document_templates\"),\n                    href: \"/documents/templates\"\n                }\n            ]\n        },\n        {\n            name: t(\"sidebar.payments\"),\n            href: \"/payments\",\n            icon: _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CreditCardIcon,\n            current: router.pathname.startsWith(\"/payments\"),\n            children: [\n                {\n                    name: t(\"sidebar.payment_history\"),\n                    href: \"/payments\"\n                },\n                {\n                    name: t(\"sidebar.payment_methods\"),\n                    href: \"/payments/methods\"\n                },\n                {\n                    name: t(\"sidebar.invoices\"),\n                    href: \"/payments/invoices\"\n                }\n            ]\n        },\n        {\n            name: t(\"sidebar.events\"),\n            href: \"/events\",\n            icon: _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CalendarIcon,\n            current: router.pathname.startsWith(\"/events\"),\n            children: [\n                {\n                    name: t(\"sidebar.event_calendar\"),\n                    href: \"/events\"\n                },\n                {\n                    name: t(\"sidebar.create_event\"),\n                    href: \"/events/create\"\n                },\n                {\n                    name: t(\"sidebar.event_registrations\"),\n                    href: \"/events/registrations\"\n                }\n            ]\n        },\n        {\n            name: t(\"sidebar.tasks\"),\n            href: \"/tasks\",\n            icon: _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ClipboardDocumentListIcon,\n            current: router.pathname.startsWith(\"/tasks\")\n        },\n        {\n            name: t(\"sidebar.notifications\"),\n            href: \"/notifications\",\n            icon: _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.BellIcon,\n            current: router.pathname.startsWith(\"/notifications\")\n        }\n    ];\n    const bottomNavigation = [\n        {\n            name: t(\"sidebar.profile\"),\n            href: \"/profile\",\n            icon: _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserCircleIcon,\n            current: router.pathname === \"/profile\"\n        },\n        {\n            name: t(\"sidebar.settings\"),\n            href: \"/settings\",\n            icon: _barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.Cog6ToothIcon,\n            current: router.pathname.startsWith(\"/settings\")\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg border-r border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-full flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center px-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/dashboard\",\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-sm\",\n                                    children: \"AJ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-3 text-lg font-semibold text-gray-900\",\n                                children: t(\"brand.name\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_CreditCardIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserCircleIcon, {\n                                    className: \"h-6 w-6 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: user?.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: user?.role\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 px-4 py-4 space-y-1 overflow-y-auto\",\n                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${item.current ? \"bg-blue-100 text-blue-700\" : \"text-gray-700 hover:bg-gray-100 hover:text-gray-900\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: `mr-3 h-5 w-5 flex-shrink-0 ${item.current ? \"text-blue-500\" : \"text-gray-400 group-hover:text-gray-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        item.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, undefined),\n                                item.children && item.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-8 mt-1 space-y-1\",\n                                    children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: child.href,\n                                            className: `block px-2 py-1 text-sm rounded-md transition-colors duration-200 ${router.pathname === child.href ? \"text-blue-700 bg-blue-50\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"}`,\n                                            children: child.name\n                                        }, child.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-4 border-t border-gray-200 space-y-1\",\n                    children: bottomNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: item.href,\n                            className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${item.current ? \"bg-blue-100 text-blue-700\" : \"text-gray-700 hover:bg-gray-100 hover:text-gray-900\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: `mr-3 h-5 w-5 flex-shrink-0 ${item.current ? \"text-blue-500\" : \"text-gray-400 group-hover:text-gray-500\"}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, undefined),\n                                item.name\n                            ]\n                        }, item.name, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   ButtonGroup: () => (/* binding */ ButtonGroup),\n/* harmony export */   FloatingActionButton: () => (/* binding */ FloatingActionButton),\n/* harmony export */   IconButton: () => (/* binding */ IconButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/cn */ \"./src/utils/cn.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_cn__WEBPACK_IMPORTED_MODULE_2__]);\n_utils_cn__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst buttonVariants = {\n    primary: \"bg-primary-700 hover:bg-primary-800 focus:ring-primary-500 text-white shadow-sm\",\n    secondary: \"bg-white hover:bg-gray-50 focus:ring-primary-500 text-gray-900 border border-gray-300 shadow-sm\",\n    success: \"bg-green-700 hover:bg-green-800 focus:ring-green-500 text-white shadow-sm\",\n    danger: \"bg-red-700 hover:bg-red-800 focus:ring-red-500 text-white shadow-sm\",\n    warning: \"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 text-white shadow-sm\",\n    info: \"bg-blue-700 hover:bg-blue-800 focus:ring-blue-500 text-white shadow-sm\",\n    ghost: \"hover:bg-gray-100 focus:ring-primary-500 text-gray-700\",\n    link: \"text-primary-700 hover:text-primary-800 focus:ring-primary-500 underline-offset-4 hover:underline\"\n};\nconst buttonSizes = {\n    xs: \"px-2.5 py-1.5 text-xs\",\n    sm: \"px-3 py-2 text-sm\",\n    md: \"px-4 py-2 text-sm\",\n    lg: \"px-4 py-2 text-base\",\n    xl: \"px-6 py-3 text-base\"\n};\nconst iconSizes = {\n    xs: \"h-3 w-3\",\n    sm: \"h-4 w-4\",\n    md: \"h-4 w-4\",\n    lg: \"h-5 w-5\",\n    xl: \"h-5 w-5\"\n};\nconst Button = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant = \"primary\", size = \"md\", loading = false, fullWidth = false, leftIcon, rightIcon, disabled, children, ...props }, ref)=>{\n    const isDisabled = disabled || loading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(// Base styles\n        \"inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\", // Variant styles\n        buttonVariants[variant], // Size styles\n        buttonSizes[size], // Full width\n        fullWidth && \"w-full\", // Loading state\n        loading && \"cursor-wait\", // Disabled state\n        isDisabled && \"pointer-events-none\", // Dark mode adjustments\n        variant === \"secondary\" && \"dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700\", variant === \"ghost\" && \"dark:text-gray-300 dark:hover:bg-gray-800\", className),\n        disabled: isDisabled,\n        ref: ref,\n        ...props,\n        children: [\n            leftIcon && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0\", iconSizes[size], children && \"me-2\"),\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 92,\n                columnNumber: 11\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(\"animate-spin\", size === \"xs\" || size === \"sm\" ? \"h-4 w-4\" : \"h-5 w-5\", children && \"me-2\"),\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 99,\n                columnNumber: 11\n            }, undefined),\n            children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: loading ? \"opacity-70\" : \"\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 127,\n                columnNumber: 11\n            }, undefined),\n            rightIcon && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0\", iconSizes[size], children && \"ms-2\"),\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 134,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 60,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\nfunction ButtonGroup({ children, className, orientation = \"horizontal\", size, variant }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex\", orientation === \"horizontal\" ? \"flex-row\" : \"flex-col\", className),\n        role: \"group\",\n        children: react__WEBPACK_IMPORTED_MODULE_1___default().Children.map(children, (child, index)=>{\n            if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(child) && child.type === Button) {\n                const isFirst = index === 0;\n                const isLast = index === react__WEBPACK_IMPORTED_MODULE_1___default().Children.count(children) - 1;\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(child, {\n                    size: child.props.size || size,\n                    variant: child.props.variant || variant,\n                    className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(child.props.className, orientation === \"horizontal\" && [\n                        !isFirst && !isLast && \"rounded-none border-x-0\",\n                        isFirst && \"rounded-e-none border-e-0\",\n                        isLast && \"rounded-s-none border-s-0\"\n                    ], orientation === \"vertical\" && [\n                        !isFirst && !isLast && \"rounded-none border-y-0\",\n                        isFirst && \"rounded-b-none border-b-0\",\n                        isLast && \"rounded-t-none border-t-0\"\n                    ])\n                });\n            }\n            return child;\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\nconst IconButton = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ icon, className, size = \"md\", ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n        ref: ref,\n        size: size,\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-2\", size === \"xs\" && \"p-1\", size === \"sm\" && \"p-1.5\", size === \"lg\" && \"p-2.5\", size === \"xl\" && \"p-3\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: iconSizes[size],\n            children: icon\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 221,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 208,\n        columnNumber: 7\n    }, undefined);\n});\nIconButton.displayName = \"IconButton\";\nconst FloatingActionButton = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, size = \"lg\", position = \"bottom-right\", ...props }, ref)=>{\n    const positionClasses = {\n        \"bottom-right\": \"fixed bottom-6 right-6 rtl:right-auto rtl:left-6\",\n        \"bottom-left\": \"fixed bottom-6 left-6 rtl:left-auto rtl:right-6\",\n        \"top-right\": \"fixed top-6 right-6 rtl:right-auto rtl:left-6\",\n        \"top-left\": \"fixed top-6 left-6 rtl:left-auto rtl:right-6\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n        ref: ref,\n        variant: \"primary\",\n        size: size,\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-full shadow-lg hover:shadow-xl z-50\", size === \"md\" && \"h-12 w-12\", size === \"lg\" && \"h-14 w-14\", positionClasses[position], className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 247,\n        columnNumber: 7\n    }, undefined);\n});\nFloatingActionButton.displayName = \"FloatingActionButton\";\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle),\n/* harmony export */   cardVariants: () => (/* binding */ cardVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/cn */ \"./src/utils/cn.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _utils_cn__WEBPACK_IMPORTED_MODULE_3__]);\n([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _utils_cn__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst cardVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"rounded-lg border bg-white text-gray-950 shadow-sm\", {\n    variants: {\n        variant: {\n            default: \"border-gray-200\",\n            outline: \"border-gray-300\",\n            elevated: \"border-gray-200 shadow-md\",\n            interactive: \"border-gray-200 hover:shadow-md transition-shadow cursor-pointer\"\n        },\n        padding: {\n            none: \"\",\n            sm: \"p-4\",\n            default: \"p-6\",\n            lg: \"p-8\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        padding: \"default\"\n    }\n});\nconst Card = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant, padding, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(cardVariants({\n            variant,\n            padding,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 49,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-gray-500\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 75,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 97,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_SimpleAuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/SimpleAuthContext */ \"./src/store/SimpleAuthContext.tsx\");\n\n\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_store_SimpleAuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvaG9va3MvdXNlQXV0aC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW1DO0FBQ3FCO0FBRWpELE1BQU1FLFVBQVU7SUFDckIsTUFBTUMsVUFBVUgsaURBQVVBLENBQUNDLGlFQUFXQTtJQUV0QyxJQUFJRSxZQUFZQyxXQUFXO1FBQ3pCLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUVBLE9BQU9GO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BhbGphbWVpYS9mcm9udGVuZC8uL3NyYy9ob29rcy91c2VBdXRoLnRzPzNjMWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEF1dGhDb250ZXh0IH0gZnJvbSAnQC9zdG9yZS9TaW1wbGVBdXRoQ29udGV4dCc7XG5cbmV4cG9ydCBjb25zdCB1c2VBdXRoID0gKCkgPT4ge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChBdXRoQ29udGV4dCk7XG4gIFxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYW4gQXV0aFByb3ZpZGVyJyk7XG4gIH1cbiAgXG4gIHJldHVybiBjb250ZXh0O1xufTtcbiJdLCJuYW1lcyI6WyJ1c2VDb250ZXh0IiwiQXV0aENvbnRleHQiLCJ1c2VBdXRoIiwiY29udGV4dCIsInVuZGVmaW5lZCIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _store_SimpleAuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/SimpleAuthContext */ \"./src/store/SimpleAuthContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Set document direction based on locale\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const direction = router.locale === \"ar\" ? \"rtl\" : \"ltr\";\n        document.documentElement.dir = direction;\n        document.documentElement.lang = router.locale || \"en\";\n    }, [\n        router.locale\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_store_SimpleAuthContext__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.appWithTranslation)(MyApp));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"index,follow\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"googlebot\",\n                        content: \"index,follow\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1d4ed8\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#1d4ed8\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://api.aljameia.com\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"application-name\",\n                        content: \"Al-Jameia Financial Association\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"Al-Jameia\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-config\",\n                        content: \"/browserconfig.xml\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-tap-highlight\",\n                        content: \"no\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Content-Type-Options\",\n                        content: \"nosniff\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Frame-Options\",\n                        content: \"DENY\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-XSS-Protection\",\n                        content: \"1; mode=block\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"Referrer-Policy\",\n                        content: \"strict-origin-when-cross-origin\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.googleapis.com\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.gstatic.com\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//api.aljameia.com\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"3d638a474d4b4ce4\",\n                        children: \".jsx-3d638a474d4b4ce4:root{--color-primary-50:#eff6ff;--color-primary-100:#dbeafe;--color-primary-200:#bfdbfe;--color-primary-300:#93c5fd;--color-primary-400:#60a5fa;--color-primary-500:#3b82f6;--color-primary-600:#2563eb;--color-primary-700:#1d4ed8;--color-primary-800:#1e40af;--color-primary-900:#1e3a8a;--color-gray-50:#f9fafb;--color-gray-100:#f3f4f6;--color-gray-200:#e5e7eb;--color-gray-300:#d1d5db;--color-gray-400:#9ca3af;--color-gray-500:#6b7280;--color-gray-600:#4b5563;--color-gray-700:#374151;--color-gray-800:#1f2937;--color-gray-900:#111827;--color-success:#10b981;--color-warning:#f59e0b;--color-error:#ef4444;--color-info:#3b82f6;--font-arabic:'Noto Sans Arabic', 'Cairo', 'Amiri', sans-serif;--font-english:'Inter', 'Roboto', 'Helvetica Neue', sans-serif;--font-display:'Amiri', 'Georgia', serif;--space-1:0.25rem;--space-2:0.5rem;--space-3:0.75rem;--space-4:1rem;--space-5:1.25rem;--space-6:1.5rem;--space-8:2rem;--space-10:2.5rem;--space-12:3rem;--space-16:4rem;--space-20:5rem;--space-24:6rem;--shadow-sm:0 1px 2px 0 rgba(0, 0, 0, 0.05);--shadow-md:0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);--shadow-lg:0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);--shadow-xl:0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);--radius-sm:0.125rem;--radius-md:0.375rem;--radius-lg:0.5rem;--radius-xl:0.75rem;--radius-2xl:1rem;--radius-full:9999px;--transition-fast:150ms ease-in-out;--transition-normal:250ms ease-in-out;--transition-slow:350ms ease-in-out;--z-dropdown:1000;--z-sticky:1020;--z-fixed:1030;--z-modal-backdrop:1040;--z-modal:1050;--z-popover:1060;--z-tooltip:1070;--z-toast:1080}@media(prefers-color-scheme:dark){.jsx-3d638a474d4b4ce4:root{--color-gray-50:#111827;--color-gray-100:#1f2937;--color-gray-200:#374151;--color-gray-300:#4b5563;--color-gray-400:#6b7280;--color-gray-500:#9ca3af;--color-gray-600:#d1d5db;--color-gray-700:#e5e7eb;--color-gray-800:#f3f4f6;--color-gray-900:#f9fafb}}*.jsx-3d638a474d4b4ce4{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}html.jsx-3d638a474d4b4ce4{scroll-behavior:smooth;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}body.jsx-3d638a474d4b4ce4{margin:0;padding:0;font-family:var(--font-english);line-height:1.6;color:var(--color-gray-900);background-color:var(--color-gray-50);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility}[dir=\\\"rtl\\\"].jsx-3d638a474d4b4ce4 body.jsx-3d638a474d4b4ce4{font-family:var(--font-arabic)}*.jsx-3d638a474d4b4ce4:focus{outline:2px solid var(--color-primary-500);outline-offset:2px}.loading.jsx-3d638a474d4b4ce4{opacity:.6;pointer-events:none}@media(prefers-reduced-motion:reduce){*.jsx-3d638a474d4b4ce4{-webkit-animation-duration:.01ms!important;-moz-animation-duration:.01ms!important;-o-animation-duration:.01ms!important;animation-duration:.01ms!important;-webkit-animation-iteration-count:1!important;-moz-animation-iteration-count:1!important;-o-animation-iteration-count:1!important;animation-iteration-count:1!important;-webkit-transition-duration:.01ms!important;-moz-transition-duration:.01ms!important;-o-transition-duration:.01ms!important;transition-duration:.01ms!important}}\"\n                    }, void 0, false, void 0, this),\n                    process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                                async: true,\n                                src: `https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID}`,\n                                className: \"jsx-3d638a474d4b4ce4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                                dangerouslySetInnerHTML: {\n                                    __html: `\n                  window.dataLayer = window.dataLayer || [];\n                  function gtag(){dataLayer.push(arguments);}\n                  gtag('js', new Date());\n                  gtag('config', '${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID}', {\n                    page_path: window.location.pathname,\n                    anonymize_ip: true,\n                    cookie_flags: 'SameSite=Strict;Secure'\n                  });\n                `\n                                },\n                                className: \"jsx-3d638a474d4b4ce4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n                window.SENTRY_DSN = '${\"your_sentry_dsn\"}';\n              `\n                        },\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noscript\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"fixed\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                bottom: 0,\n                                backgroundColor: \"#f9fafb\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                flexDirection: \"column\",\n                                textAlign: \"center\",\n                                padding: \"2rem\",\n                                zIndex: 9999\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        color: \"#1f2937\",\n                                        marginBottom: \"1rem\"\n                                    },\n                                    children: \"JavaScript Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: \"#6b7280\",\n                                        maxWidth: \"400px\"\n                                    },\n                                    children: \"This application requires JavaScript to function properly. Please enable JavaScript in your browser settings and reload the page.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              if ('serviceWorker' in navigator && window.location.protocol === 'https:') {\n                navigator.serviceWorker.register('/sw.js')\n                  .then(function(registration) {\n                    console.log('SW registered: ', registration);\n                  })\n                  .catch(function(registrationError) {\n                    console.log('SW registration failed: ', registrationError);\n                  });\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next/serverSideTranslations */ \"next-i18next/serverSideTranslations\");\n/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_CreditCardIcon_DocumentTextIcon_GlobeAltIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,CreditCardIcon,DocumentTextIcon,GlobeAltIcon,ShieldCheckIcon,UsersIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,CreditCardIcon,DocumentTextIcon,GlobeAltIcon,ShieldCheckIcon,UsersIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/Layout */ \"./src/components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Button */ \"./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/Card */ \"./src/components/ui/Card.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_6__, _components_layout_Layout__WEBPACK_IMPORTED_MODULE_7__, _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__, _components_ui_Card__WEBPACK_IMPORTED_MODULE_9__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_6__, _components_layout_Layout__WEBPACK_IMPORTED_MODULE_7__, _components_ui_Button__WEBPACK_IMPORTED_MODULE_8__, _components_ui_Card__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\nconst HomePage = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsLoaded(true);\n    }, []);\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_CreditCardIcon_DocumentTextIcon_GlobeAltIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.ChartBarIcon,\n            title: t(\"features.financial_management.title\"),\n            description: t(\"features.financial_management.description\"),\n            color: \"bg-blue-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_CreditCardIcon_DocumentTextIcon_GlobeAltIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.UsersIcon,\n            title: t(\"features.member_management.title\"),\n            description: t(\"features.member_management.description\"),\n            color: \"bg-green-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_CreditCardIcon_DocumentTextIcon_GlobeAltIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.DocumentTextIcon,\n            title: t(\"features.document_management.title\"),\n            description: t(\"features.document_management.description\"),\n            color: \"bg-purple-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_CreditCardIcon_DocumentTextIcon_GlobeAltIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.CreditCardIcon,\n            title: t(\"features.payment_processing.title\"),\n            description: t(\"features.payment_processing.description\"),\n            color: \"bg-orange-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_CreditCardIcon_DocumentTextIcon_GlobeAltIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.ShieldCheckIcon,\n            title: t(\"features.security.title\"),\n            description: t(\"features.security.description\"),\n            color: \"bg-red-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_CreditCardIcon_DocumentTextIcon_GlobeAltIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.GlobeAltIcon,\n            title: t(\"features.multilingual.title\"),\n            description: t(\"features.multilingual.description\"),\n            color: \"bg-indigo-500\"\n        }\n    ];\n    const stats = [\n        {\n            label: t(\"stats.active_members\"),\n            value: \"2,500+\"\n        },\n        {\n            label: t(\"stats.transactions_processed\"),\n            value: \"50,000+\"\n        },\n        {\n            label: t(\"stats.documents_managed\"),\n            value: \"10,000+\"\n        },\n        {\n            label: t(\"stats.uptime\"),\n            value: \"99.9%\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            t(\"meta.home.title\"),\n                            \" | Al-Jameia\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: t(\"meta.home.description\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: t(\"meta.home.keywords\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 text-white overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: isLoaded ? 1 : 0,\n                                y: isLoaded ? 0 : 20\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                    children: t(\"hero.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto\",\n                                    children: t(\"hero.subtitle\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/auth/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                size: \"lg\",\n                                                className: \"bg-white text-blue-900 hover:bg-blue-50\",\n                                                children: [\n                                                    t(\"hero.cta.get_started\"),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_CreditCardIcon_DocumentTextIcon_GlobeAltIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.ArrowRightIcon, {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/demo\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"border-white text-white hover:bg-white hover:text-blue-900\",\n                                                children: t(\"hero.cta.view_demo\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: isLoaded ? 1 : 0,\n                                    y: isLoaded ? 0 : 20\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-blue-900 mb-2\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: t(\"features.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: t(\"features.subtitle\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: isLoaded ? 1 : 0,\n                                        y: isLoaded ? 0 : 20\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: index * 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                                        className: \"p-6 h-full hover:shadow-lg transition-shadow duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-12 h-12 ${feature.color} rounded-lg flex items-center justify-center mb-4`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-blue-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-6\",\n                                        children: t(\"benefits.title\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 mb-8\",\n                                        children: t(\"benefits.subtitle\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            t(\"benefits.list.automated_processes\"),\n                                            t(\"benefits.list.real_time_reporting\"),\n                                            t(\"benefits.list.secure_transactions\"),\n                                            t(\"benefits.list.mobile_accessibility\"),\n                                            t(\"benefits.list.compliance_ready\")\n                                        ].map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_CreditCardIcon_DocumentTextIcon_GlobeAltIcon_ShieldCheckIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.CheckCircleIcon, {\n                                                        className: \"h-6 w-6 text-green-500 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-700\",\n                                                        children: benefit\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-xl p-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-bold text-blue-900 mb-2\",\n                                                children: t(\"benefits.efficiency_improvement\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-600 mb-6\",\n                                                children: t(\"benefits.efficiency_description\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/auth/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    className: \"w-full\",\n                                                    children: t(\"benefits.cta\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-blue-900 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-6\",\n                            children: t(\"cta.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl mb-8 text-blue-100\",\n                            children: t(\"cta.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/auth/register\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-white text-blue-900 hover:bg-blue-50\",\n                                        children: t(\"cta.start_free_trial\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/contact\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        size: \"lg\",\n                                        variant: \"outline\",\n                                        className: \"border-white text-white hover:bg-white hover:text-blue-900\",\n                                        children: t(\"cta.contact_sales\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\nconst getStaticProps = async ({ locale })=>{\n    return {\n        props: {\n            ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__.serverSideTranslations)(locale ?? \"en\", [\n                \"common\"\n            ])\n        }\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/store/SimpleAuthContext.tsx":
/*!*****************************************!*\
  !*** ./src/store/SimpleAuthContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!user;\n    // Check for existing session on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuthStatus();\n    }, []);\n    const checkAuthStatus = async ()=>{\n        try {\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                setIsLoading(false);\n                return;\n            }\n            // For now, just simulate a logged-in user\n            // In real implementation, validate token with backend\n            const mockUser = {\n                id: \"user_001\",\n                name: \"Ahmed Al-Rashid\",\n                email: \"<EMAIL>\",\n                role: \"Admin\",\n                permissions: [\n                    \"read\",\n                    \"write\",\n                    \"admin\"\n                ],\n                membershipNumber: \"AJ2024001\",\n                membershipType: \"Premium\",\n                isActive: true\n            };\n            setUser(mockUser);\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n            localStorage.removeItem(\"auth_token\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Mock successful login\n            const token = \"mock_token_\" + Date.now();\n            localStorage.setItem(\"auth_token\", token);\n            const mockUser = {\n                id: \"user_001\",\n                name: \"Ahmed Al-Rashid\",\n                email: email,\n                role: \"Admin\",\n                permissions: [\n                    \"read\",\n                    \"write\",\n                    \"admin\"\n                ],\n                membershipNumber: \"AJ2024001\",\n                membershipType: \"Premium\",\n                isActive: true\n            };\n            setUser(mockUser);\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            setIsLoading(true);\n            localStorage.removeItem(\"auth_token\");\n            setUser(null);\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Mock successful registration\n            const token = \"mock_token_\" + Date.now();\n            localStorage.setItem(\"auth_token\", token);\n            const newUser = {\n                id: \"user_\" + Date.now(),\n                name: userData.name,\n                email: userData.email,\n                role: \"Member\",\n                permissions: [\n                    \"read\"\n                ],\n                membershipType: userData.membershipType,\n                isActive: true\n            };\n            setUser(newUser);\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateUser = async (userData)=>{\n        try {\n            if (!user) throw new Error(\"No user logged in\");\n            const updatedUser = {\n                ...user,\n                ...userData\n            };\n            setUser(updatedUser);\n        } catch (error) {\n            console.error(\"Update user error:\", error);\n            throw error;\n        }\n    };\n    const value = {\n        user,\n        isAuthenticated,\n        isLoading,\n        login,\n        logout,\n        register,\n        updateUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\store\\\\SimpleAuthContext.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, undefined);\n};\n\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/store/SimpleAuthContext.tsx\n");

/***/ }),

/***/ "./src/utils/cn.ts":
/*!*************************!*\
  !*** ./src/utils/cn.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvY24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZDO0FBQ0o7QUFFbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFsamFtZWlhL2Zyb250ZW5kLy4vc3JjL3V0aWxzL2NuLnRzPzEwMGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSAnY2xzeCc7XG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSAndGFpbHdpbmQtbWVyZ2UnO1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/utils/cn.ts\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next-i18next":
/*!*******************************!*\
  !*** external "next-i18next" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-i18next");

/***/ }),

/***/ "next-i18next/serverSideTranslations":
/*!******************************************************!*\
  !*** external "next-i18next/serverSideTranslations" ***!
  \******************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-i18next/serverSideTranslations");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-jsx/style");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@headlessui","vendor-chunks/date-fns","vendor-chunks/@heroicons","vendor-chunks/@babel"], () => (__webpack_exec__("../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();