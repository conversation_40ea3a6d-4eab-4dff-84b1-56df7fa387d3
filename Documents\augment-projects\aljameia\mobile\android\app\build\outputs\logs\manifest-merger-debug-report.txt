-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:139:9-147:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:143:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:141:13-64
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:142:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:140:13-62
provider#androidx.startup.InitializationProvider
INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:172:9-181:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b130cd70e504561c41ad4072d5324071\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b130cd70e504561c41ad4072d5324071\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:176:13-31
	android:authorities
		INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:174:13-68
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:175:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:173:13-67
manifest
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:2:1-256:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:2:1-256:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:2:1-256:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:2:1-256:12
MERGED from [androidx.databinding:databinding-adapters:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b40dcae04f4b36bd7d37085a67b38353\transformed\databinding-adapters-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d87a2d31cbc6e2b38f7881b887033ba3\transformed\databinding-ktx-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\63b3ceff809610e7439f598baffe7cd3\transformed\databinding-runtime-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\035b51d53ab80e9f65898435a0afaae6\transformed\viewbinding-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\1346d289b44246ee04525d6a96197cff\transformed\leakcanary-android-2.12\AndroidManifest.xml:17:1-22:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\629d1cd626e7eb381201c03ac34f698c\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed02dfe0a2f49d35bf2a7049e1d5ab1a\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\ee390bd7ace463e35705fc81371faf46\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8daf187d5cf9b8fa0834a9e23ff2d97f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc96a6a0b82e329acba2def9752cb508\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\370a254c36288f64ed35ca3e80f8bc6f\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\58af33140212bc67d0240a89ec462042\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\52c9096637ca49dec17fea5f9d1a1deb\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdc55fe2502a063bcee343c082439e6f\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2a56f6da0e2e4ab4da1b326f38c566a\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\245e41f4f9bb59b0e0cc757725ebb826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\523b9fad2b83d89c2b74860689d8fb91\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94e54b7494386206adcb15d4b3aa19fd\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad30a34aa6a81d7ba44f4c995637350b\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef935df5cddda02f6bf104f03514bba5\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e529be0c04f142fd4de9b22adde7071f\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\384a9ba72bea77d8f082de99a8229662\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:3:1-23:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\21cc0a6be925d3c2a0c747bb469e8733\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d082938884a16ba501ea606cfa36272b\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\34ac1df8d0ed1fa0a3eba8543bd2e656\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.9\transforms\19089cc562885cfe65b14a13d8265c08\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\90b0534273d440f233ebe5dad27d98b7\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23fabbbb6bd2a1a625afd08367e27d74\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\d675007904f8ec54986b8bb4c267ee7c\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1891b5ac41ccd7645e65a37115b3ef5\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18f3357489b54de390b24f569be8cabd\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\182d2c6ae256eee05b97ec0e1ae1887f\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c514f303830aa824ccabc1313adde6e2\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfd72afe637bdc634ee37ea0eef81e46\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\5c21714475114f035dd2c106dce00794\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0628a14f2fff10ba59ede51891f8816c\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\f663483284788a54fee28d9a032ee98a\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\391ee569a1581e025946ea739e1d874b\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\622eda704d1189cf2f6ab97a0d506261\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\028e5b31377ffce628375dffabee96de\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a31c7bc8ecbb90cabbd597cd96be0ab4\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ecc6ae2d8c94871a0989263475f054a\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f5c709107965f04960c972647af4cf3\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\c25f533aa572fe500e6389a51dee5024\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fcc3532d00a555645a773639a09108ed\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\effdf5520d9dd22862c97937adc0cc07\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\c795900cada6cf19a13dbec59a9919bf\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\41da5ab248ec719ca0f079a986706402\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f45dd7b25e2dc9e838bbe426c153fa1\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f2b22b2ab0b694c9d27487438f23808\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\85fae80ff7869a8da6251422548fb462\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed84789db6538adcfd143599cdbfd3e9\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\da9e84411b8dfa674a9c12485c325635\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\01084cb3d99351ce7ff26e0c991000ab\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ff2350575b098c8175150fc1d2badb08\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aeb55ba32162c8a72d07137ca2086cc1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f049c96ccc4b2745fdeedd80af2217fc\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\41d4605ffa48f4149b26ca392b66608b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3312931fad1f0966ad5bff926a56414\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3916ecf27548c09d888e2337682ecc9a\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6f91bef5ff9ce441c3e839782c5d5326\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59199bd057e6719ad768a582ee7e530d\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ae5ff5dc7e450f6cdce23a5b96816e7\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4654f7c46fc474fc84130ba98ee5fa47\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ee0e783fbcfc00087f0762de0db193a9\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a9190a9df9f7d7c9a01194f20a380125\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a18840f13b068cc94bc722ee3576fb7\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6cc3c1c2d4450495796607bf1b9da65f\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\614563d14e5403ef42af75b5b494cd3d\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3e74a7425b14daa5ccd7c3c1ea8df2a\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1fc6df18416275b07881759c39b0993f\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc76a3bb1a1076b64ac190f691dc314b\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\71e016441b58fda394d9587d7b796cad\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a971d51c4220ac8a4f96cd55a92374f8\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\251fc446fb1bfc501b82088d55168650\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfbe020969ca1ab62e936f6285ff84ee\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a2141ffcacde8eac3533d8c157bc939\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\042a995b03406a97a715fc352c38020f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0435b8a911de28aafcb8a306b6c34519\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bba51e2bfe5bb5371a063efdefd2e6f2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e12879a980b74f386227ba4c8534f549\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\77c18fb8679f0229d58d2305892349a5\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\90f91ec706ee305e2c7aeec76c183d53\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a93b900411693bc280b9f9ed2df56cd\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eaa6d4680a261792980401f1324fc001\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\755278efc6579e5529c352fcdd8e6e5b\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2f671d14904532117c2ce59f3ce701a\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\22b4f625842308e42b7a59aa60903ced\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b74f6b50a4993298e59fa45b95d9728f\transformed\recaptcha-18.1.2\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:15:1-30:12
MERGED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:15:1-35:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd001e0cc0511de757e949669444c711\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\707a1ac4515beaae9d0691978b51f131\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\430ea855c0debcd7a70d2c99ab5b3bca\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\115c6cc5e40617f502bb198ecd34781a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\140f1347c27d3e8efbe4dcb64753d522\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5a2f689c4cd5ce11424083807820f964\transformed\integrity-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5dcd4713312f640565387fe7b7560609\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8e7c02087f77b378d94ff0864afe780\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f06ef6258703397434e8870cd377f09\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9605fecf4d9c835b0522b5f76acef14\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad2b6378ae8179bec8a998a336963ff9\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6d23ccbac3cdebda656ab7fdc32b69d\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\6836bd4b1eca4d8262dd32551f342d8d\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9854085b949dbec36dacef2110accf85\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e0400d15ea5e3f3172f4dfddca2f50c\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8dfd6b78c0dfe8643216044251f9638\transformed\timber-5.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:17:1-105:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:2:1-15:12
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f80b133345ddd29304a2f5b12a7908a9\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a47bbaa6761adb29862ed60ebfe8b45\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\34499b16b36cf1b871840c6e278645ce\transformed\firebase-config-interop-16.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b38f643f5fc48aea4e2fe0c7bfa2e03\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ece352fc16fd533ab4ebb2381d9ae0b\transformed\leakcanary-object-watcher-android-androidx-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\dedfa7a24d6ca4fabb54abbfc775ef0f\transformed\leakcanary-object-watcher-android-support-fragments-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\78830fb9d41f105523294c1cc9369e5a\transformed\leakcanary-object-watcher-android-core-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:plumber-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a3cc55cd079eea5ae85dd27f7831812\transformed\plumber-android-core-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f811009b254b8e1682101e7a1d03343\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\26002c5f624bf01c443ec153e1812c21\transformed\leakcanary-android-utils-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c071a355b1c8284ab4a7018e8aa44ed\transformed\curtains-1.2.4\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\98de38fa847e13ffaeaf7ca388760da8\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a36fe51251a5d19e9712ca049808faf\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7fa46e185433946e0afd76ab50f3a251\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac3e77eb95fb5610fec3e78541f5bee5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\030e6373fe0adbf64e5eedbf48dd1c20\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b74213d6b8adb9aac93885db90c2602b\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\598e90ad319d36bb96345f53de2b1107\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d60a14d6725250920469aa5e0c24a1a\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b130cd70e504561c41ad4072d5324071\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\111e0749d92f87f646c63896fff28809\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a3ff65799b62da781ed8b5205bd71d5\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a787304a2635921725856a1debab2cc6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ecb22e8fe7497edbac1223b3021458f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc31da993183dede924656913ce7f5db\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8aaeac1f6db6d9a7699a01a4db683eee\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18c29b5ee9b260c92f1dac2a369d6eca\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c291514a71cfe17be393ae52758345ff\transformed\grpc-android-1.52.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:17:1-56:12
MERGED from [com.tom-roush:pdfbox-android:2.0.27.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b12aa28a22072038e551f52b8d2dd93d\transformed\pdfbox-android-2.0.27.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.jakewharton.threetenabp:threetenabp:1.4.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a11855d63b0679ce01a9bb3c959617b1\transformed\threetenabp-1.4.6\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8a925af5c6a144db62641a60b109549\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b74f6b50a4993298e59fa45b95d9728f\transformed\recaptcha-18.1.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b74f6b50a4993298e59fa45b95d9728f\transformed\recaptcha-18.1.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b74f6b50a4993298e59fa45b95d9728f\transformed\recaptcha-18.1.2\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b74f6b50a4993298e59fa45b95d9728f\transformed\recaptcha-18.1.2\AndroidManifest.xml:8:5-79
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c291514a71cfe17be393ae52758345ff\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c291514a71cfe17be393ae52758345ff\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:9:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:9:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:10:5-80
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:25:5-80
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:25:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:11:5-12:38
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:26:5-81
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:26:5-81
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.USE_FINGERPRINT
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:13:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\523b9fad2b83d89c2b74860689d8fb91\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\523b9fad2b83d89c2b74860689d8fb91\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:13:22-71
uses-permission#android.permission.USE_BIOMETRIC
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:14:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\523b9fad2b83d89c2b74860689d8fb91\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\523b9fad2b83d89c2b74860689d8fb91\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:14:22-69
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:15:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:15:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:16:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:16:22-78
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:17:5-66
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:17:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:18:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:18:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:19:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:19:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:20:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:20:22-74
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:21:5-95
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:21:22-92
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:24:5-82
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:24:22-79
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:27:5-29:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:29:9-33
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:28:9-47
uses-feature#android.hardware.fingerprint
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:30:5-32:36
	android:required
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:32:9-33
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:31:9-52
uses-feature#android.hardware.location
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:35:9-33
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:34:9-49
uses-feature#android.hardware.location.gps
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:38:9-33
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:37:9-53
application
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:40:5-229:19
INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:40:5-229:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2a56f6da0e2e4ab4da1b326f38c566a\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2a56f6da0e2e4ab4da1b326f38c566a\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\245e41f4f9bb59b0e0cc757725ebb826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\245e41f4f9bb59b0e0cc757725ebb826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:10:5-21:19
MERGED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:10:5-21:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\90b0534273d440f233ebe5dad27d98b7\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\90b0534273d440f233ebe5dad27d98b7\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1891b5ac41ccd7645e65a37115b3ef5\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1891b5ac41ccd7645e65a37115b3ef5\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18f3357489b54de390b24f569be8cabd\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18f3357489b54de390b24f569be8cabd\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6f91bef5ff9ce441c3e839782c5d5326\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6f91bef5ff9ce441c3e839782c5d5326\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59199bd057e6719ad768a582ee7e530d\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59199bd057e6719ad768a582ee7e530d\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ae5ff5dc7e450f6cdce23a5b96816e7\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ae5ff5dc7e450f6cdce23a5b96816e7\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:20:5-28:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:20:5-28:19
MERGED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:21:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\115c6cc5e40617f502bb198ecd34781a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\115c6cc5e40617f502bb198ecd34781a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5a2f689c4cd5ce11424083807820f964\transformed\integrity-1.1.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5a2f689c4cd5ce11424083807820f964\transformed\integrity-1.1.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5dcd4713312f640565387fe7b7560609\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5dcd4713312f640565387fe7b7560609\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8e7c02087f77b378d94ff0864afe780\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8e7c02087f77b378d94ff0864afe780\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f06ef6258703397434e8870cd377f09\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f06ef6258703397434e8870cd377f09\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9605fecf4d9c835b0522b5f76acef14\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9605fecf4d9c835b0522b5f76acef14\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad2b6378ae8179bec8a998a336963ff9\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad2b6378ae8179bec8a998a336963ff9\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8dfd6b78c0dfe8643216044251f9638\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8dfd6b78c0dfe8643216044251f9638\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b130cd70e504561c41ad4072d5324071\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b130cd70e504561c41ad4072d5324071\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a787304a2635921725856a1debab2cc6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a787304a2635921725856a1debab2cc6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.jakewharton.threetenabp:threetenabp:1.4.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a11855d63b0679ce01a9bb3c959617b1\transformed\threetenabp-1.4.6\AndroidManifest.xml:7:5-20
MERGED from [com.jakewharton.threetenabp:threetenabp:1.4.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a11855d63b0679ce01a9bb3c959617b1\transformed\threetenabp-1.4.6\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:51:9-53
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:47:9-54
	android:icon
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:45:9-43
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:50:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:46:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:44:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:52:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:42:9-36
	android:theme
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:48:9-46
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:43:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:49:9-45
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:41:9-44
activity#com.aljameia.app.ui.main.MainActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:55:9-81:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:59:13-49
	android:launchMode
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:58:13-43
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:60:13-55
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:57:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:56:13-49
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:61:13-64:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:62:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:62:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:63:17-77
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:63:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:aljameia.com+data:scheme:https
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:13-73:29
	android:autoVerify
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:28-53
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:68:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:68:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:69:17-76
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:69:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:70:17-78
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:70:27-75
data
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:71:17-72:51
	android:host
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:72:21-48
	android:scheme
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:71:23-45
	android:mimeType
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:245:19-41
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:aljameia
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:75:13-80:29
activity#com.aljameia.app.ui.auth.AuthActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:84:9-89:58
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:87:13-49
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:88:13-55
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:86:13-37
	android:theme
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:89:13-55
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:85:13-49
activity#com.aljameia.app.ui.splash.SplashActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:92:9-101:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:95:13-49
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:94:13-36
	android:theme
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:96:13-57
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:93:13-53
activity#com.aljameia.app.ui.documents.DocumentViewerActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:104:9-107:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:107:13-49
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:106:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:105:13-64
activity#com.aljameia.app.ui.settings.SettingsActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:110:9-114:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:113:13-49
	android:parentActivityName
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:114:13-63
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:112:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:111:13-57
activity#com.aljameia.app.ui.profile.ProfileActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:117:9-121:66
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:120:13-49
	android:parentActivityName
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:121:13-63
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:119:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:118:13-55
service#com.aljameia.app.services.FCMService
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:124:9-130:19
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:126:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:125:13-48
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:127:13-129:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:128:17-78
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:128:25-75
service#com.aljameia.app.services.SyncService
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:133:9-136:72
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:135:13-37
	android:permission
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:136:13-69
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:134:13-49
receiver#com.aljameia.app.receivers.BootReceiver
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:150:9-160:20
	android:enabled
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:152:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:153:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:151:13-51
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:154:13-159:29
	android:priority
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:154:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:155:17-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:155:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:156:17-84
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:156:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:157:17-81
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:157:25-78
receiver#com.aljameia.app.receivers.NetworkReceiver
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:162:9-169:20
	android:enabled
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:164:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:165:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:163:13-54
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:166:13-168:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:167:17-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:167:25-76
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:184:9-186:60
	android:resource
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:186:13-57
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:185:13-83
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:187:9-189:49
	android:resource
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:189:13-46
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:188:13-84
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:190:9-192:71
	android:value
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:192:13-68
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:191:13-89
meta-data#android.security.SECURITY_PATCH_LEVEL
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:195:9-197:42
	android:value
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:197:13-39
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:196:13-65
meta-data#android.app.shortcuts
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:200:9-202:49
	android:resource
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:202:13-46
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:201:13-49
meta-data#com.google.android.backup.api_key
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:205:9-207:54
	android:value
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:207:13-51
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:206:13-61
meta-data#com.google.android.gms.analytics.globalConfigResource
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:210:9-212:54
	android:resource
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:212:13-51
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:211:13-81
meta-data#firebase_crashlytics_collection_enabled
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:215:9-217:36
	android:value
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:217:13-33
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:216:13-67
meta-data#firebase_performance_collection_enabled
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:220:9-222:36
	android:value
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:222:13-33
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:221:13-67
meta-data#firebase_remote_config_developer_mode_enabled
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:225:9-227:37
	android:value
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:227:13-34
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:226:13-73
queries
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:232:5-254:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:233:9-237:18
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:http
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:238:9-242:18
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:243:9-246:18
action#android.intent.action.SEND
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:244:13-65
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:244:21-62
intent#action:name:android.intent.action.SENDTO+data:scheme:mailto
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:247:9-250:18
action#android.intent.action.SENDTO
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:248:13-67
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:248:21-64
intent#action:name:android.intent.action.DIAL
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:251:9-253:18
action#android.intent.action.DIAL
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:252:13-65
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:252:21-62
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:144:13-146:54
	android:resource
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:146:17-51
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:145:17-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:177:13-180:39
REJECTED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:180:17-36
	android:value
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:179:17-49
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:178:17-68
uses-sdk
INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b40dcae04f4b36bd7d37085a67b38353\transformed\databinding-adapters-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b40dcae04f4b36bd7d37085a67b38353\transformed\databinding-adapters-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d87a2d31cbc6e2b38f7881b887033ba3\transformed\databinding-ktx-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d87a2d31cbc6e2b38f7881b887033ba3\transformed\databinding-ktx-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\63b3ceff809610e7439f598baffe7cd3\transformed\databinding-runtime-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\63b3ceff809610e7439f598baffe7cd3\transformed\databinding-runtime-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\035b51d53ab80e9f65898435a0afaae6\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\035b51d53ab80e9f65898435a0afaae6\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\1346d289b44246ee04525d6a96197cff\transformed\leakcanary-android-2.12\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\1346d289b44246ee04525d6a96197cff\transformed\leakcanary-android-2.12\AndroidManifest.xml:20:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\629d1cd626e7eb381201c03ac34f698c\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\629d1cd626e7eb381201c03ac34f698c\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed02dfe0a2f49d35bf2a7049e1d5ab1a\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed02dfe0a2f49d35bf2a7049e1d5ab1a\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\ee390bd7ace463e35705fc81371faf46\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\ee390bd7ace463e35705fc81371faf46\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8daf187d5cf9b8fa0834a9e23ff2d97f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8daf187d5cf9b8fa0834a9e23ff2d97f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc96a6a0b82e329acba2def9752cb508\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc96a6a0b82e329acba2def9752cb508\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\370a254c36288f64ed35ca3e80f8bc6f\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\370a254c36288f64ed35ca3e80f8bc6f\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\58af33140212bc67d0240a89ec462042\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\58af33140212bc67d0240a89ec462042\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\52c9096637ca49dec17fea5f9d1a1deb\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\52c9096637ca49dec17fea5f9d1a1deb\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdc55fe2502a063bcee343c082439e6f\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdc55fe2502a063bcee343c082439e6f\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2a56f6da0e2e4ab4da1b326f38c566a\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2a56f6da0e2e4ab4da1b326f38c566a\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\245e41f4f9bb59b0e0cc757725ebb826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\245e41f4f9bb59b0e0cc757725ebb826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\523b9fad2b83d89c2b74860689d8fb91\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\523b9fad2b83d89c2b74860689d8fb91\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94e54b7494386206adcb15d4b3aa19fd\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94e54b7494386206adcb15d4b3aa19fd\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad30a34aa6a81d7ba44f4c995637350b\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad30a34aa6a81d7ba44f4c995637350b\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef935df5cddda02f6bf104f03514bba5\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef935df5cddda02f6bf104f03514bba5\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e529be0c04f142fd4de9b22adde7071f\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e529be0c04f142fd4de9b22adde7071f\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\384a9ba72bea77d8f082de99a8229662\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\384a9ba72bea77d8f082de99a8229662\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\21cc0a6be925d3c2a0c747bb469e8733\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\21cc0a6be925d3c2a0c747bb469e8733\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d082938884a16ba501ea606cfa36272b\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d082938884a16ba501ea606cfa36272b\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\34ac1df8d0ed1fa0a3eba8543bd2e656\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\34ac1df8d0ed1fa0a3eba8543bd2e656\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.9\transforms\19089cc562885cfe65b14a13d8265c08\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.9\transforms\19089cc562885cfe65b14a13d8265c08\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\90b0534273d440f233ebe5dad27d98b7\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\90b0534273d440f233ebe5dad27d98b7\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23fabbbb6bd2a1a625afd08367e27d74\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23fabbbb6bd2a1a625afd08367e27d74\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\d675007904f8ec54986b8bb4c267ee7c\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\d675007904f8ec54986b8bb4c267ee7c\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1891b5ac41ccd7645e65a37115b3ef5\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1891b5ac41ccd7645e65a37115b3ef5\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18f3357489b54de390b24f569be8cabd\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18f3357489b54de390b24f569be8cabd\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\182d2c6ae256eee05b97ec0e1ae1887f\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\182d2c6ae256eee05b97ec0e1ae1887f\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c514f303830aa824ccabc1313adde6e2\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c514f303830aa824ccabc1313adde6e2\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfd72afe637bdc634ee37ea0eef81e46\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfd72afe637bdc634ee37ea0eef81e46\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\5c21714475114f035dd2c106dce00794\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\5c21714475114f035dd2c106dce00794\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0628a14f2fff10ba59ede51891f8816c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0628a14f2fff10ba59ede51891f8816c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\f663483284788a54fee28d9a032ee98a\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\f663483284788a54fee28d9a032ee98a\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\391ee569a1581e025946ea739e1d874b\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\391ee569a1581e025946ea739e1d874b\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\622eda704d1189cf2f6ab97a0d506261\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\622eda704d1189cf2f6ab97a0d506261\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\028e5b31377ffce628375dffabee96de\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\028e5b31377ffce628375dffabee96de\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a31c7bc8ecbb90cabbd597cd96be0ab4\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a31c7bc8ecbb90cabbd597cd96be0ab4\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ecc6ae2d8c94871a0989263475f054a\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ecc6ae2d8c94871a0989263475f054a\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f5c709107965f04960c972647af4cf3\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f5c709107965f04960c972647af4cf3\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\c25f533aa572fe500e6389a51dee5024\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\c25f533aa572fe500e6389a51dee5024\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fcc3532d00a555645a773639a09108ed\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fcc3532d00a555645a773639a09108ed\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\effdf5520d9dd22862c97937adc0cc07\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\effdf5520d9dd22862c97937adc0cc07\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\c795900cada6cf19a13dbec59a9919bf\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\c795900cada6cf19a13dbec59a9919bf\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\41da5ab248ec719ca0f079a986706402\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\41da5ab248ec719ca0f079a986706402\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f45dd7b25e2dc9e838bbe426c153fa1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f45dd7b25e2dc9e838bbe426c153fa1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f2b22b2ab0b694c9d27487438f23808\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f2b22b2ab0b694c9d27487438f23808\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\85fae80ff7869a8da6251422548fb462\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\85fae80ff7869a8da6251422548fb462\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed84789db6538adcfd143599cdbfd3e9\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed84789db6538adcfd143599cdbfd3e9\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\da9e84411b8dfa674a9c12485c325635\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\da9e84411b8dfa674a9c12485c325635\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\01084cb3d99351ce7ff26e0c991000ab\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\01084cb3d99351ce7ff26e0c991000ab\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ff2350575b098c8175150fc1d2badb08\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ff2350575b098c8175150fc1d2badb08\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aeb55ba32162c8a72d07137ca2086cc1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aeb55ba32162c8a72d07137ca2086cc1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f049c96ccc4b2745fdeedd80af2217fc\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f049c96ccc4b2745fdeedd80af2217fc\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\41d4605ffa48f4149b26ca392b66608b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\41d4605ffa48f4149b26ca392b66608b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3312931fad1f0966ad5bff926a56414\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3312931fad1f0966ad5bff926a56414\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3916ecf27548c09d888e2337682ecc9a\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3916ecf27548c09d888e2337682ecc9a\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6f91bef5ff9ce441c3e839782c5d5326\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6f91bef5ff9ce441c3e839782c5d5326\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59199bd057e6719ad768a582ee7e530d\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59199bd057e6719ad768a582ee7e530d\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ae5ff5dc7e450f6cdce23a5b96816e7\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ae5ff5dc7e450f6cdce23a5b96816e7\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4654f7c46fc474fc84130ba98ee5fa47\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4654f7c46fc474fc84130ba98ee5fa47\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ee0e783fbcfc00087f0762de0db193a9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ee0e783fbcfc00087f0762de0db193a9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a9190a9df9f7d7c9a01194f20a380125\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a9190a9df9f7d7c9a01194f20a380125\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a18840f13b068cc94bc722ee3576fb7\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8a18840f13b068cc94bc722ee3576fb7\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6cc3c1c2d4450495796607bf1b9da65f\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6cc3c1c2d4450495796607bf1b9da65f\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\614563d14e5403ef42af75b5b494cd3d\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\614563d14e5403ef42af75b5b494cd3d\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3e74a7425b14daa5ccd7c3c1ea8df2a\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3e74a7425b14daa5ccd7c3c1ea8df2a\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1fc6df18416275b07881759c39b0993f\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1fc6df18416275b07881759c39b0993f\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc76a3bb1a1076b64ac190f691dc314b\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc76a3bb1a1076b64ac190f691dc314b\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\71e016441b58fda394d9587d7b796cad\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\71e016441b58fda394d9587d7b796cad\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a971d51c4220ac8a4f96cd55a92374f8\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a971d51c4220ac8a4f96cd55a92374f8\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\251fc446fb1bfc501b82088d55168650\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\251fc446fb1bfc501b82088d55168650\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfbe020969ca1ab62e936f6285ff84ee\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfbe020969ca1ab62e936f6285ff84ee\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a2141ffcacde8eac3533d8c157bc939\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a2141ffcacde8eac3533d8c157bc939\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\042a995b03406a97a715fc352c38020f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\042a995b03406a97a715fc352c38020f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0435b8a911de28aafcb8a306b6c34519\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0435b8a911de28aafcb8a306b6c34519\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bba51e2bfe5bb5371a063efdefd2e6f2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bba51e2bfe5bb5371a063efdefd2e6f2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e12879a980b74f386227ba4c8534f549\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e12879a980b74f386227ba4c8534f549\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\77c18fb8679f0229d58d2305892349a5\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\77c18fb8679f0229d58d2305892349a5\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\90f91ec706ee305e2c7aeec76c183d53\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\90f91ec706ee305e2c7aeec76c183d53\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a93b900411693bc280b9f9ed2df56cd\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a93b900411693bc280b9f9ed2df56cd\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eaa6d4680a261792980401f1324fc001\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eaa6d4680a261792980401f1324fc001\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\755278efc6579e5529c352fcdd8e6e5b\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\755278efc6579e5529c352fcdd8e6e5b\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2f671d14904532117c2ce59f3ce701a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\d2f671d14904532117c2ce59f3ce701a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\22b4f625842308e42b7a59aa60903ced\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\22b4f625842308e42b7a59aa60903ced\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b74f6b50a4993298e59fa45b95d9728f\transformed\recaptcha-18.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\b74f6b50a4993298e59fa45b95d9728f\transformed\recaptcha-18.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd001e0cc0511de757e949669444c711\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd001e0cc0511de757e949669444c711\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\707a1ac4515beaae9d0691978b51f131\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\707a1ac4515beaae9d0691978b51f131\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\430ea855c0debcd7a70d2c99ab5b3bca\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\430ea855c0debcd7a70d2c99ab5b3bca\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\115c6cc5e40617f502bb198ecd34781a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\115c6cc5e40617f502bb198ecd34781a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\140f1347c27d3e8efbe4dcb64753d522\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\140f1347c27d3e8efbe4dcb64753d522\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5a2f689c4cd5ce11424083807820f964\transformed\integrity-1.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5a2f689c4cd5ce11424083807820f964\transformed\integrity-1.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5dcd4713312f640565387fe7b7560609\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5dcd4713312f640565387fe7b7560609\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46d30e80df957d7052dd124c8ba0e423\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8e7c02087f77b378d94ff0864afe780\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8e7c02087f77b378d94ff0864afe780\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f06ef6258703397434e8870cd377f09\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f06ef6258703397434e8870cd377f09\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9605fecf4d9c835b0522b5f76acef14\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9605fecf4d9c835b0522b5f76acef14\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad2b6378ae8179bec8a998a336963ff9\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad2b6378ae8179bec8a998a336963ff9\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6d23ccbac3cdebda656ab7fdc32b69d\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6d23ccbac3cdebda656ab7fdc32b69d\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\6836bd4b1eca4d8262dd32551f342d8d\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\6836bd4b1eca4d8262dd32551f342d8d\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9854085b949dbec36dacef2110accf85\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9854085b949dbec36dacef2110accf85\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e0400d15ea5e3f3172f4dfddca2f50c\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e0400d15ea5e3f3172f4dfddca2f50c\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8dfd6b78c0dfe8643216044251f9638\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8dfd6b78c0dfe8643216044251f9638\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f80b133345ddd29304a2f5b12a7908a9\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f80b133345ddd29304a2f5b12a7908a9\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a47bbaa6761adb29862ed60ebfe8b45\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a47bbaa6761adb29862ed60ebfe8b45\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\34499b16b36cf1b871840c6e278645ce\transformed\firebase-config-interop-16.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\34499b16b36cf1b871840c6e278645ce\transformed\firebase-config-interop-16.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b38f643f5fc48aea4e2fe0c7bfa2e03\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b38f643f5fc48aea4e2fe0c7bfa2e03\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ece352fc16fd533ab4ebb2381d9ae0b\transformed\leakcanary-object-watcher-android-androidx-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ece352fc16fd533ab4ebb2381d9ae0b\transformed\leakcanary-object-watcher-android-androidx-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\dedfa7a24d6ca4fabb54abbfc775ef0f\transformed\leakcanary-object-watcher-android-support-fragments-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\dedfa7a24d6ca4fabb54abbfc775ef0f\transformed\leakcanary-object-watcher-android-support-fragments-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\78830fb9d41f105523294c1cc9369e5a\transformed\leakcanary-object-watcher-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\78830fb9d41f105523294c1cc9369e5a\transformed\leakcanary-object-watcher-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a3cc55cd079eea5ae85dd27f7831812\transformed\plumber-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a3cc55cd079eea5ae85dd27f7831812\transformed\plumber-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f811009b254b8e1682101e7a1d03343\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1f811009b254b8e1682101e7a1d03343\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\26002c5f624bf01c443ec153e1812c21\transformed\leakcanary-android-utils-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\26002c5f624bf01c443ec153e1812c21\transformed\leakcanary-android-utils-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c071a355b1c8284ab4a7018e8aa44ed\transformed\curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c071a355b1c8284ab4a7018e8aa44ed\transformed\curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\98de38fa847e13ffaeaf7ca388760da8\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\98de38fa847e13ffaeaf7ca388760da8\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a36fe51251a5d19e9712ca049808faf\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.9\transforms\0a36fe51251a5d19e9712ca049808faf\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7fa46e185433946e0afd76ab50f3a251\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7fa46e185433946e0afd76ab50f3a251\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac3e77eb95fb5610fec3e78541f5bee5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac3e77eb95fb5610fec3e78541f5bee5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\030e6373fe0adbf64e5eedbf48dd1c20\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\030e6373fe0adbf64e5eedbf48dd1c20\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b74213d6b8adb9aac93885db90c2602b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b74213d6b8adb9aac93885db90c2602b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\598e90ad319d36bb96345f53de2b1107\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\598e90ad319d36bb96345f53de2b1107\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d60a14d6725250920469aa5e0c24a1a\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d60a14d6725250920469aa5e0c24a1a\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b130cd70e504561c41ad4072d5324071\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b130cd70e504561c41ad4072d5324071\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\111e0749d92f87f646c63896fff28809\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\111e0749d92f87f646c63896fff28809\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a3ff65799b62da781ed8b5205bd71d5\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a3ff65799b62da781ed8b5205bd71d5\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a787304a2635921725856a1debab2cc6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a787304a2635921725856a1debab2cc6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ecb22e8fe7497edbac1223b3021458f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ecb22e8fe7497edbac1223b3021458f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc31da993183dede924656913ce7f5db\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc31da993183dede924656913ce7f5db\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8aaeac1f6db6d9a7699a01a4db683eee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8aaeac1f6db6d9a7699a01a4db683eee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18c29b5ee9b260c92f1dac2a369d6eca\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\18c29b5ee9b260c92f1dac2a369d6eca\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c291514a71cfe17be393ae52758345ff\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c291514a71cfe17be393ae52758345ff\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.tom-roush:pdfbox-android:2.0.27.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b12aa28a22072038e551f52b8d2dd93d\transformed\pdfbox-android-2.0.27.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.tom-roush:pdfbox-android:2.0.27.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b12aa28a22072038e551f52b8d2dd93d\transformed\pdfbox-android-2.0.27.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.jakewharton.threetenabp:threetenabp:1.4.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a11855d63b0679ce01a9bb3c959617b1\transformed\threetenabp-1.4.6\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.threetenabp:threetenabp:1.4.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a11855d63b0679ce01a9bb3c959617b1\transformed\threetenabp-1.4.6\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8a925af5c6a144db62641a60b109549\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8a925af5c6a144db62641a60b109549\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:21:9-27:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:21:9-27:19
MERGED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:10:13-84
meta-data#com.google.firebase.components:com.google.firebase.perf.ktx.FirebasePerfLegacyRegistrar
ADDED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:13:17-119
meta-data#com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar
ADDED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:14:13-16:85
	android:value
		ADDED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:16:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:15:17-112
meta-data#com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar
ADDED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:18:17-109
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.storage.ktx.FirebaseStorageLegacyRegistrar
ADDED from [com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:13:17-125
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar
ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:5-77
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:29:5-77
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:29:5-77
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:22-74
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:29:17-117
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9605fecf4d9c835b0522b5f76acef14\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9605fecf4d9c835b0522b5f76acef14\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:22-76
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.aljameia.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.aljameia.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:24:13-26:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:26:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:25:17-130
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:19:17-115
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:30:17-117
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.ktx.FirebaseConfigLegacyRegistrar
ADDED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar
ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:30:17-128
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar
ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:33:17-117
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d4a250898a98859d18b1a9ef8ab6162\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
property
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
meta-data#com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar
ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
provider#leakcanary.internal.LeakCanaryFileProvider
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:32:9-40:20
	android:grantUriPermissions
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:36:13-47
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:34:13-88
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:33:13-70
activity#leakcanary.internal.activity.LeakActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:42:9-73:20
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:46:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:44:13-36
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:45:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:48:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:47:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:43:13-69
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:mimeType:*/*+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\.hprof+data:pathPattern:.*\\.hprof+data:scheme:content+data:scheme:file
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:49:13-72:29
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:49:28-81
activity-alias#leakcanary.internal.activity.LeakLauncherActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:75:9-92:26
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:78:13-66
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:81:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:79:13-36
	android:targetActivity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:82:13-79
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:80:13-52
	android:banner
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:77:13-59
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:84:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:83:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:76:13-77
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:85:13-91:29
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:90:17-86
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:90:27-83
activity#leakcanary.internal.RequestPermissionActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:94:9-100:68
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:98:13-82
	android:excludeFromRecents
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:96:13-46
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:97:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:100:13-65
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:99:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:95:13-73
receiver#leakcanary.internal.NotificationReceiver
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:102:9-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:102:19-74
provider#leakcanary.internal.MainProcessAppWatcherInstaller
ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:9:13-78
provider#leakcanary.internal.PlumberInstaller
ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:10:13-69
	android:exported
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:9:13-64
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
uses-feature#android.hardware.camera.autofocus
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
uses-feature#android.hardware.camera.flash
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
