'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  CurrencyDollarIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CreditCardIcon,
  BanknotesIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import type { Jamiya, SubscriptionForm, PaymentMethod } from '@/types/jamiya';

export default function CreateSubscriptionPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [availableJamiyas, setAvailableJamiyas] = useState<Jamiya[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<SubscriptionForm>({
    jamiyaId: '',
    personalInfo: {
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      nationalId: ''
    },
    subscriptionDetails: {
      sharesCount: 1,
      pricePerShare: 0,
      monthlyAmount: 0,
      paymentMethod: 'bank_transfer',
      autoRenewal: true,
      reminderDays: [7, 3, 1]
    },
    agreesToTerms: false
  });

  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const mockJamiyas: Jamiya[] = [
    {
      id: '1',
      name: 'جمعية الأصدقاء',
      description: 'جمعية مالية بين مجموعة من الأصدقاء المقربين',
      type: 'simple',
      status: 'recruiting',
      totalMembers: 12,
      monthlyAmount: 2500, // سعر السهم الواحد
      currency: 'SAR',
      duration: 12,
      startDate: '2024-07-01',
      endDate: '2024-06-30',
      createdAt: '2024-06-01T10:00:00Z',
      updatedAt: '2024-06-18T14:30:00Z',
      members: [],
      cycles: [],
      contributions: [],
      organizer: {
        id: 'org1',
        name: 'أحمد المنظم',
        email: '<EMAIL>',
        phone: '+************',
        nationalId: '**********',
        organizingExperience: 5,
        successfulJamiyas: 8,
        rating: 4.8,
        isVerified: true
      },
      guarantors: [],
      rules: {
        allowEarlyWithdrawal: false,
        allowLateJoining: true,
        requireGuarantor: false,
        lateFee: 50,
        earlyWithdrawalDiscount: 0,
        lateWithdrawalBonus: 0,
        allowBidding: false,
        minimumBid: 0,
        biddingDeadline: 0,
        gracePeriod: 3,
        maxMissedPayments: 2,
        autoSuspension: true,
        advanceNotice: 7,
        withdrawalPenalty: 100
      },
      statistics: {
        totalCollected: 0,
        totalDistributed: 0,
        totalPending: 0,
        activeMembers: 8,
        suspendedMembers: 0,
        completedMembers: 0,
        onTimePayments: 0,
        latePayments: 0,
        missedPayments: 0,
        completedCycles: 0,
        pendingCycles: 12,
        collectionRate: 0,
        onTimeRate: 0,
        completionRate: 0
      },
      settings: {
        enableEmailNotifications: true,
        enableSmsNotifications: true,
        enablePushNotifications: true,
        paymentReminderDays: [7, 3, 1],
        cycleReminderDays: [7, 1],
        autoProcessPayments: false,
        autoDistributeFunds: false,
        autoSuspendDefaulters: true,
        showMemberDetails: true,
        showPaymentHistory: false,
        allowMemberCommunication: true,
        requireTwoFactorAuth: false,
        requirePaymentVerification: true,
        logAllActivities: true
      }
    },
    {
      id: '2',
      name: 'جمعية العائلة الكبيرة',
      description: 'جمعية مالية لأفراد العائلة الكبيرة',
      type: 'simple',
      status: 'recruiting',
      totalMembers: 20,
      monthlyAmount: 1500, // سعر السهم الواحد
      currency: 'SAR',
      duration: 20,
      startDate: '2024-08-01',
      endDate: '2026-03-31',
      createdAt: '2024-06-01T10:00:00Z',
      updatedAt: '2024-06-18T14:30:00Z',
      members: [],
      cycles: [],
      contributions: [],
      organizer: {
        id: 'org2',
        name: 'فاطمة المنظمة',
        email: '<EMAIL>',
        phone: '+966502345678',
        nationalId: '2345678901',
        organizingExperience: 3,
        successfulJamiyas: 5,
        rating: 4.6,
        isVerified: true
      },
      guarantors: [],
      rules: {
        allowEarlyWithdrawal: true,
        allowLateJoining: true,
        requireGuarantor: true,
        lateFee: 100,
        earlyWithdrawalDiscount: 5,
        lateWithdrawalBonus: 0,
        allowBidding: false,
        minimumBid: 0,
        biddingDeadline: 0,
        gracePeriod: 5,
        maxMissedPayments: 3,
        autoSuspension: false,
        advanceNotice: 10,
        withdrawalPenalty: 200
      },
      statistics: {
        totalCollected: 0,
        totalDistributed: 0,
        totalPending: 0,
        activeMembers: 15,
        suspendedMembers: 0,
        completedMembers: 0,
        onTimePayments: 0,
        latePayments: 0,
        missedPayments: 0,
        completedCycles: 0,
        pendingCycles: 20,
        collectionRate: 0,
        onTimeRate: 0,
        completionRate: 0
      },
      settings: {
        enableEmailNotifications: true,
        enableSmsNotifications: true,
        enablePushNotifications: true,
        paymentReminderDays: [10, 5, 1],
        cycleReminderDays: [7, 1],
        autoProcessPayments: false,
        autoDistributeFunds: false,
        autoSuspendDefaulters: false,
        showMemberDetails: true,
        showPaymentHistory: true,
        allowMemberCommunication: true,
        requireTwoFactorAuth: true,
        requirePaymentVerification: true,
        logAllActivities: true
      }
    }
  ];

  useEffect(() => {
    const loadJamiyas = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setAvailableJamiyas(mockJamiyas);
      setIsLoading(false);
    };

    loadJamiyas();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case 'cash':
        return <BanknotesIcon className="h-5 w-5" />;
      case 'bank_transfer':
      case 'auto_debit':
        return <CreditCardIcon className="h-5 w-5" />;
      default:
        return <CurrencyDollarIcon className="h-5 w-5" />;
    }
  };

  const getPaymentMethodText = (method: PaymentMethod) => {
    switch (method) {
      case 'cash': return 'نقداً';
      case 'bank_transfer': return 'تحويل بنكي';
      case 'online_payment': return 'دفع إلكتروني';
      case 'auto_debit': return 'خصم تلقائي';
      case 'check': return 'شيك';
      default: return method;
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => {
        const newData = {
          ...prev,
          [parent]: {
            ...prev[parent as keyof SubscriptionForm],
            [child]: value
          }
        };

        // حساب المبلغ الإجمالي عند تغيير عدد الأسهم
        if (parent === 'subscriptionDetails' && (child === 'sharesCount' || child === 'pricePerShare')) {
          const selectedJamiya = availableJamiyas.find(j => j.id === newData.jamiyaId);
          if (selectedJamiya) {
            const sharesCount = child === 'sharesCount' ? value : newData.subscriptionDetails.sharesCount;
            const pricePerShare = selectedJamiya.monthlyAmount;
            newData.subscriptionDetails.pricePerShare = pricePerShare;
            newData.subscriptionDetails.monthlyAmount = sharesCount * pricePerShare;
          }
        }

        return newData;
      });
    } else {
      setFormData(prev => {
        const newData = {
          ...prev,
          [field]: value
        };

        // تحديث سعر السهم والمبلغ الإجمالي عند اختيار الجمعية
        if (field === 'jamiyaId') {
          const selectedJamiya = availableJamiyas.find(j => j.id === value);
          if (selectedJamiya) {
            newData.subscriptionDetails.pricePerShare = selectedJamiya.monthlyAmount;
            newData.subscriptionDetails.monthlyAmount = newData.subscriptionDetails.sharesCount * selectedJamiya.monthlyAmount;
          }
        }

        return newData;
      });
    }
  };

  const validateStep = (step: number) => {
    switch (step) {
      case 1:
        return formData.jamiyaId !== '';
      case 2:
        return formData.personalInfo.name && 
               formData.personalInfo.email && 
               formData.personalInfo.phone && 
               formData.personalInfo.nationalId;
      case 3:
        return formData.subscriptionDetails.sharesCount > 0 &&
               formData.subscriptionDetails.monthlyAmount > 0 &&
               formData.subscriptionDetails.paymentMethod;
      case 4:
        return formData.agreesToTerms;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(4)) return;

    setIsSubmitting(true);
    try {
      // محاكاة إرسال البيانات
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // في التطبيق الحقيقي، سيتم إرسال البيانات إلى API
      console.log('Subscription data:', formData);
      
      // إعادة توجيه إلى صفحة النجاح
      router.push('/subscriptions?success=true');
    } catch (error) {
      console.error('Error creating subscription:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedJamiya = availableJamiyas.find(j => j.id === formData.jamiyaId);

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['subscription_management']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الجمعيات المتاحة...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['subscription_management']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">منصة الجمعيات المالية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/subscriptions">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    الاشتراكات
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="text-center">
              <CurrencyDollarIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">اشتراك جديد في الأسهم الشهرية</h1>
              <p className="text-gray-600">اشترك في إحدى الجمعيات المالية المتاحة</p>
            </div>
          </motion.div>

          {/* Progress Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mb-8"
          >
            <div className="flex items-center justify-center">
              {[1, 2, 3, 4].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2
                    ${currentStep >= step
                      ? 'bg-blue-600 border-blue-600 text-white'
                      : 'bg-white border-gray-300 text-gray-400'
                    }
                  `}>
                    {currentStep > step ? (
                      <CheckCircleIcon className="h-6 w-6" />
                    ) : (
                      <span className="text-sm font-medium">{step}</span>
                    )}
                  </div>
                  {step < 4 && (
                    <div className={`
                      w-16 h-1 mx-2
                      ${currentStep > step ? 'bg-blue-600' : 'bg-gray-300'}
                    `} />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-center mt-4">
              <div className="text-sm text-gray-600">
                الخطوة {currentStep} من 4: {
                  currentStep === 1 ? 'اختيار الجمعية' :
                  currentStep === 2 ? 'المعلومات الشخصية' :
                  currentStep === 3 ? 'تفاصيل الاشتراك' :
                  'المراجعة والتأكيد'
                }
              </div>
            </div>
          </motion.div>

          {/* Form Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            {/* Step 1: Select Jamiya */}
            {currentStep === 1 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">اختر الجمعية المالية</h3>
                <div className="space-y-4">
                  {availableJamiyas.map((jamiya) => (
                    <div
                      key={jamiya.id}
                      onClick={() => handleInputChange('jamiyaId', jamiya.id)}
                      className={`
                        border rounded-lg p-4 cursor-pointer transition-colors
                        ${formData.jamiyaId === jamiya.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                      `}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">{jamiya.name}</h4>
                          <p className="text-sm text-gray-600 mt-1">{jamiya.description}</p>
                          <div className="flex items-center mt-2 space-x-4 space-x-reverse">
                            <span className="text-sm text-gray-500">
                              سعر السهم: {formatCurrency(jamiya.monthlyAmount)}
                            </span>
                            <span className="text-sm text-gray-500">
                              المدة: {jamiya.duration} شهر
                            </span>
                            <span className="text-sm text-gray-500">
                              الأعضاء: {jamiya.statistics.activeMembers}/{jamiya.totalMembers}
                            </span>
                          </div>
                        </div>
                        <div className={`
                          w-6 h-6 rounded-full border-2 flex items-center justify-center
                          ${formData.jamiyaId === jamiya.id
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-300'
                          }
                        `}>
                          {formData.jamiyaId === jamiya.id && (
                            <CheckCircleIcon className="h-4 w-4 text-white" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Step 2: Personal Information */}
            {currentStep === 2 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الشخصية</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم الكامل *
                    </label>
                    <input
                      type="text"
                      value={formData.personalInfo.name}
                      onChange={(e) => handleInputChange('personalInfo.name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="أدخل اسمك الكامل"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني *
                    </label>
                    <input
                      type="email"
                      value={formData.personalInfo.email}
                      onChange={(e) => handleInputChange('personalInfo.email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهاتف *
                    </label>
                    <input
                      type="tel"
                      value={formData.personalInfo.phone}
                      onChange={(e) => handleInputChange('personalInfo.phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="+************"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهوية الوطنية *
                    </label>
                    <input
                      type="text"
                      value={formData.personalInfo.nationalId}
                      onChange={(e) => handleInputChange('personalInfo.nationalId', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="**********"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Subscription Details */}
            {currentStep === 3 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل الاشتراك</h3>

                {selectedJamiya && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 className="font-medium text-blue-900 mb-2">الجمعية المختارة: {selectedJamiya.name}</h4>
                    <p className="text-sm text-blue-700">
                      سعر السهم الواحد: {formatCurrency(selectedJamiya.monthlyAmount)}
                    </p>
                  </div>
                )}

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      عدد الأسهم *
                    </label>
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <button
                        type="button"
                        onClick={() => {
                          const newCount = Math.max(1, formData.subscriptionDetails.sharesCount - 1);
                          handleInputChange('subscriptionDetails.sharesCount', newCount);
                        }}
                        className="w-10 h-10 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center transition-colors"
                      >
                        <span className="text-lg font-bold">-</span>
                      </button>

                      <input
                        type="number"
                        value={formData.subscriptionDetails.sharesCount}
                        onChange={(e) => handleInputChange('subscriptionDetails.sharesCount', Math.max(1, Number(e.target.value)))}
                        className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-center"
                        min="1"
                        max="10"
                      />

                      <button
                        type="button"
                        onClick={() => {
                          const newCount = Math.min(10, formData.subscriptionDetails.sharesCount + 1);
                          handleInputChange('subscriptionDetails.sharesCount', newCount);
                        }}
                        className="w-10 h-10 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center transition-colors"
                      >
                        <span className="text-lg font-bold">+</span>
                      </button>

                      <div className="flex-1">
                        <span className="text-sm text-gray-600">سهم</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      يمكنك اختيار من 1 إلى 10 أسهم
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المبلغ الإجمالي الشهري
                    </label>
                    <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">
                          {formData.subscriptionDetails.sharesCount} سهم × {selectedJamiya ? formatCurrency(selectedJamiya.monthlyAmount) : '0 ريال'}
                        </span>
                        <span className="text-lg font-bold text-blue-600">
                          {formatCurrency(formData.subscriptionDetails.monthlyAmount)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      طريقة الدفع *
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {(['bank_transfer', 'auto_debit', 'cash', 'online_payment'] as PaymentMethod[]).map((method) => (
                        <div
                          key={method}
                          onClick={() => handleInputChange('subscriptionDetails.paymentMethod', method)}
                          className={`
                            border rounded-lg p-4 cursor-pointer transition-colors flex items-center
                            ${formData.subscriptionDetails.paymentMethod === method
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                            }
                          `}
                        >
                          <div className="flex items-center">
                            {getPaymentMethodIcon(method)}
                            <span className="mr-3 font-medium">{getPaymentMethodText(method)}</span>
                          </div>
                          <div className={`
                            mr-auto w-5 h-5 rounded-full border-2 flex items-center justify-center
                            ${formData.subscriptionDetails.paymentMethod === method
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-gray-300'
                            }
                          `}>
                            {formData.subscriptionDetails.paymentMethod === method && (
                              <CheckCircleIcon className="h-3 w-3 text-white" />
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {(formData.subscriptionDetails.paymentMethod === 'bank_transfer' ||
                    formData.subscriptionDetails.paymentMethod === 'auto_debit') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        رقم الحساب البنكي
                      </label>
                      <input
                        type="text"
                        value={formData.subscriptionDetails.bankAccount || ''}
                        onChange={(e) => handleInputChange('subscriptionDetails.bankAccount', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="************************"
                      />
                    </div>
                  )}

                  <div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="autoRenewal"
                        checked={formData.subscriptionDetails.autoRenewal}
                        onChange={(e) => handleInputChange('subscriptionDetails.autoRenewal', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="autoRenewal" className="mr-2 text-sm text-gray-700">
                        تجديد تلقائي للاشتراك
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      أيام التذكير قبل الاستحقاق
                    </label>
                    <div className="flex items-center space-x-4 space-x-reverse">
                      {[1, 3, 5, 7, 10].map((day) => (
                        <label key={day} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.subscriptionDetails.reminderDays.includes(day)}
                            onChange={(e) => {
                              const days = formData.subscriptionDetails.reminderDays;
                              if (e.target.checked) {
                                handleInputChange('subscriptionDetails.reminderDays', [...days, day]);
                              } else {
                                handleInputChange('subscriptionDetails.reminderDays', days.filter(d => d !== day));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="mr-2 text-sm text-gray-700">{day} أيام</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Review and Confirm */}
            {currentStep === 4 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">مراجعة وتأكيد الاشتراك</h3>

                <div className="space-y-6">
                  {/* Jamiya Summary */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">الجمعية المختارة</h4>
                    {selectedJamiya && (
                      <div>
                        <p className="text-sm text-gray-600">{selectedJamiya.name}</p>
                        <p className="text-xs text-gray-500">{selectedJamiya.description}</p>
                      </div>
                    )}
                  </div>

                  {/* Personal Info Summary */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">المعلومات الشخصية</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">الاسم:</span>
                        <span className="mr-2 text-gray-900">{formData.personalInfo.name}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">البريد:</span>
                        <span className="mr-2 text-gray-900">{formData.personalInfo.email}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">الهاتف:</span>
                        <span className="mr-2 text-gray-900">{formData.personalInfo.phone}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">الهوية:</span>
                        <span className="mr-2 text-gray-900">{formData.personalInfo.nationalId}</span>
                      </div>
                    </div>
                  </div>

                  {/* Subscription Details Summary */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">تفاصيل الاشتراك</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">عدد الأسهم:</span>
                        <span className="mr-2 text-gray-900 font-medium">
                          {formData.subscriptionDetails.sharesCount} سهم
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">سعر السهم:</span>
                        <span className="mr-2 text-gray-900">
                          {formatCurrency(formData.subscriptionDetails.pricePerShare)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">المبلغ الإجمالي:</span>
                        <span className="mr-2 text-gray-900 font-medium">
                          {formatCurrency(formData.subscriptionDetails.monthlyAmount)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">طريقة الدفع:</span>
                        <span className="mr-2 text-gray-900">
                          {getPaymentMethodText(formData.subscriptionDetails.paymentMethod)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">التجديد التلقائي:</span>
                        <span className="mr-2 text-gray-900">
                          {formData.subscriptionDetails.autoRenewal ? 'مفعل' : 'غير مفعل'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">أيام التذكير:</span>
                        <span className="mr-2 text-gray-900">
                          {formData.subscriptionDetails.reminderDays.join(', ')} أيام
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Terms and Conditions */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <input
                        type="checkbox"
                        id="agreesToTerms"
                        checked={formData.agreesToTerms}
                        onChange={(e) => handleInputChange('agreesToTerms', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                      />
                      <div className="mr-3">
                        <label htmlFor="agreesToTerms" className="text-sm text-gray-700">
                          أوافق على{' '}
                          <Link href="/terms" className="text-blue-600 hover:text-blue-800">
                            الشروط والأحكام
                          </Link>
                          {' '}و{' '}
                          <Link href="/privacy" className="text-blue-600 hover:text-blue-800">
                            سياسة الخصوصية
                          </Link>
                        </label>
                        <p className="text-xs text-gray-500 mt-1">
                          بالموافقة، أؤكد أنني قرأت وفهمت جميع الشروط والأحكام المتعلقة بالاشتراك في الجمعية المالية
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Important Notice */}
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex">
                      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 ml-3 mt-0.5" />
                      <div>
                        <h5 className="text-sm font-medium text-yellow-800">تنبيه مهم</h5>
                        <p className="text-sm text-yellow-700 mt-1">
                          بعد تأكيد الاشتراك، ستصبح ملزماً بدفع المبلغ الشهري في المواعيد المحددة.
                          يرجى التأكد من قدرتك على الالتزام بالمدفوعات الشهرية قبل المتابعة.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
              <button
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className={`
                  px-4 py-2 rounded-lg font-medium transition-colors
                  ${currentStep === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }
                `}
              >
                السابق
              </button>

              <div className="flex items-center space-x-3 space-x-reverse">
                {currentStep < 4 ? (
                  <button
                    onClick={handleNext}
                    disabled={!validateStep(currentStep)}
                    className={`
                      px-6 py-2 rounded-lg font-medium transition-colors
                      ${validateStep(currentStep)
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      }
                    `}
                  >
                    التالي
                  </button>
                ) : (
                  <button
                    onClick={handleSubmit}
                    disabled={!validateStep(4) || isSubmitting}
                    className={`
                      px-6 py-2 rounded-lg font-medium transition-colors flex items-center
                      ${validateStep(4) && !isSubmitting
                        ? 'bg-green-600 text-white hover:bg-green-700'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      }
                    `}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري الإرسال...
                      </>
                    ) : (
                      <>
                        <CheckCircleIcon className="h-4 w-4 ml-2" />
                        تأكيد الاشتراك
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
