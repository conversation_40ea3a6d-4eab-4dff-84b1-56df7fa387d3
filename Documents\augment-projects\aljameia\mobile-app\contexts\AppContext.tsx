import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { Alert } from 'react-native';
import {
  DashboardStats,
  Subscription,
  Payment,
  Jamiya,
  reportService,
  jamiyaService,
  paymentService
} from '../services';
import { cacheService } from '../services/cacheService';
import { notificationService } from '../services/notificationService';

// App State Types
interface AppState {
  // Dashboard
  dashboardStats: DashboardStats | null;
  isDashboardLoading: boolean;
  
  // Subscriptions
  subscriptions: Subscription[];
  isSubscriptionsLoading: boolean;
  
  // Payments
  payments: Payment[];
  isPaymentsLoading: boolean;
  
  // Jamiyas
  jamiyas: Jamiya[];
  isJamiyasLoading: boolean;
  
  // General
  isOnline: boolean;
  lastUpdated: string | null;
}

// Action Types
type AppAction =
  | { type: 'SET_DASHBOARD_LOADING'; payload: boolean }
  | { type: 'SET_DASHBOARD_STATS'; payload: DashboardStats }
  | { type: 'SET_SUBSCRIPTIONS_LOADING'; payload: boolean }
  | { type: 'SET_SUBSCRIPTIONS'; payload: Subscription[] }
  | { type: 'SET_PAYMENTS_LOADING'; payload: boolean }
  | { type: 'SET_PAYMENTS'; payload: Payment[] }
  | { type: 'SET_JAMIYAS_LOADING'; payload: boolean }
  | { type: 'SET_JAMIYAS'; payload: Jamiya[] }
  | { type: 'SET_ONLINE_STATUS'; payload: boolean }
  | { type: 'SET_LAST_UPDATED'; payload: string }
  | { type: 'RESET_STATE' };

// Initial State
const initialState: AppState = {
  dashboardStats: null,
  isDashboardLoading: false,
  subscriptions: [],
  isSubscriptionsLoading: false,
  payments: [],
  isPaymentsLoading: false,
  jamiyas: [],
  isJamiyasLoading: false,
  isOnline: true,
  lastUpdated: null,
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_DASHBOARD_LOADING':
      return { ...state, isDashboardLoading: action.payload };
    case 'SET_DASHBOARD_STATS':
      return { ...state, dashboardStats: action.payload, isDashboardLoading: false };
    case 'SET_SUBSCRIPTIONS_LOADING':
      return { ...state, isSubscriptionsLoading: action.payload };
    case 'SET_SUBSCRIPTIONS':
      return { ...state, subscriptions: action.payload, isSubscriptionsLoading: false };
    case 'SET_PAYMENTS_LOADING':
      return { ...state, isPaymentsLoading: action.payload };
    case 'SET_PAYMENTS':
      return { ...state, payments: action.payload, isPaymentsLoading: false };
    case 'SET_JAMIYAS_LOADING':
      return { ...state, isJamiyasLoading: action.payload };
    case 'SET_JAMIYAS':
      return { ...state, jamiyas: action.payload, isJamiyasLoading: false };
    case 'SET_ONLINE_STATUS':
      return { ...state, isOnline: action.payload };
    case 'SET_LAST_UPDATED':
      return { ...state, lastUpdated: action.payload };
    case 'RESET_STATE':
      return initialState;
    default:
      return state;
  }
}

// Context Type
interface AppContextType {
  state: AppState;
  
  // Dashboard Actions
  loadDashboardStats: () => Promise<void>;
  
  // Subscriptions Actions
  loadSubscriptions: () => Promise<void>;
  refreshSubscriptions: () => Promise<void>;
  
  // Payments Actions
  loadPayments: () => Promise<void>;
  createPayment: (paymentData: any) => Promise<Payment>;
  
  // Jamiyas Actions
  loadJamiyas: () => Promise<void>;
  joinJamiya: (jamiyaId: string, shares: number) => Promise<void>;
  
  // General Actions
  refreshAllData: () => Promise<void>;
  checkNetworkStatus: () => Promise<void>;
  resetAppState: () => void;
}

// Create Context
const AppContext = createContext<AppContextType | undefined>(undefined);

// App Provider Props
interface AppProviderProps {
  children: ReactNode;
}

// App Provider Component
export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Dashboard Actions
  const loadDashboardStats = async () => {
    try {
      dispatch({ type: 'SET_DASHBOARD_LOADING', payload: true });

      // Try to get from cache first
      const cachedStats = await cacheService.getDashboardStats();
      if (cachedStats) {
        dispatch({ type: 'SET_DASHBOARD_STATS', payload: cachedStats });
      }

      // Fetch fresh data
      const stats = await reportService.getDashboardStats();
      dispatch({ type: 'SET_DASHBOARD_STATS', payload: stats });
      dispatch({ type: 'SET_LAST_UPDATED', payload: new Date().toISOString() });

      // Cache the fresh data
      await cacheService.setDashboardStats(stats);
    } catch (error) {
      console.error('Failed to load dashboard stats:', error);
      dispatch({ type: 'SET_DASHBOARD_LOADING', payload: false });

      // If network fails, try to use cached data
      const cachedStats = await cacheService.getDashboardStats();
      if (cachedStats) {
        dispatch({ type: 'SET_DASHBOARD_STATS', payload: cachedStats });
        Alert.alert('تنبيه', 'تم تحميل البيانات من التخزين المحلي');
      } else {
        Alert.alert('خطأ', 'فشل في تحميل إحصائيات لوحة التحكم');
      }
    }
  };

  // Subscriptions Actions
  const loadSubscriptions = async () => {
    try {
      dispatch({ type: 'SET_SUBSCRIPTIONS_LOADING', payload: true });

      // Try to get from cache first
      const cachedSubscriptions = await cacheService.getSubscriptions();
      if (cachedSubscriptions) {
        dispatch({ type: 'SET_SUBSCRIPTIONS', payload: cachedSubscriptions });
      }

      // Fetch fresh data
      const subscriptions = await jamiyaService.getSubscriptions();
      dispatch({ type: 'SET_SUBSCRIPTIONS', payload: subscriptions });

      // Cache the fresh data
      await cacheService.setSubscriptions(subscriptions);
    } catch (error) {
      console.error('Failed to load subscriptions:', error);
      dispatch({ type: 'SET_SUBSCRIPTIONS_LOADING', payload: false });

      // If network fails, try to use cached data
      const cachedSubscriptions = await cacheService.getSubscriptions();
      if (cachedSubscriptions) {
        dispatch({ type: 'SET_SUBSCRIPTIONS', payload: cachedSubscriptions });
        Alert.alert('تنبيه', 'تم تحميل الاشتراكات من التخزين المحلي');
      } else {
        Alert.alert('خطأ', 'فشل في تحميل الاشتراكات');
      }
    }
  };

  const refreshSubscriptions = async () => {
    await loadSubscriptions();
  };

  // Payments Actions
  const loadPayments = async () => {
    try {
      dispatch({ type: 'SET_PAYMENTS_LOADING', payload: true });
      const paymentsResponse = await paymentService.getPayments({ limit: 50 });
      dispatch({ type: 'SET_PAYMENTS', payload: paymentsResponse.data });
    } catch (error) {
      console.error('Failed to load payments:', error);
      dispatch({ type: 'SET_PAYMENTS_LOADING', payload: false });
      Alert.alert('خطأ', 'فشل في تحميل المدفوعات');
    }
  };

  const createPayment = async (paymentData: any): Promise<Payment> => {
    try {
      const payment = await paymentService.createPayment(paymentData);
      
      // Refresh payments and dashboard stats
      await Promise.all([
        loadPayments(),
        loadDashboardStats(),
        loadSubscriptions()
      ]);
      
      return payment;
    } catch (error) {
      console.error('Failed to create payment:', error);
      throw error;
    }
  };

  // Jamiyas Actions
  const loadJamiyas = async () => {
    try {
      dispatch({ type: 'SET_JAMIYAS_LOADING', payload: true });
      const jamiyasResponse = await jamiyaService.getJamiyas({ limit: 50 });
      dispatch({ type: 'SET_JAMIYAS', payload: jamiyasResponse.data });
    } catch (error) {
      console.error('Failed to load jamiyas:', error);
      dispatch({ type: 'SET_JAMIYAS_LOADING', payload: false });
      Alert.alert('خطأ', 'فشل في تحميل الجمعيات');
    }
  };

  const joinJamiya = async (jamiyaId: string, shares: number) => {
    try {
      await jamiyaService.joinJamiya({ jamiyaId, shares });
      
      // Refresh related data
      await Promise.all([
        loadSubscriptions(),
        loadDashboardStats(),
        loadJamiyas()
      ]);
      
      Alert.alert('نجح', 'تم الانضمام للجمعية بنجاح');
    } catch (error) {
      console.error('Failed to join jamiya:', error);
      Alert.alert('خطأ', 'فشل في الانضمام للجمعية');
      throw error;
    }
  };

  // General Actions
  const refreshAllData = async () => {
    try {
      await Promise.all([
        loadDashboardStats(),
        loadSubscriptions(),
        loadPayments(),
        loadJamiyas()
      ]);
    } catch (error) {
      console.error('Failed to refresh all data:', error);
      Alert.alert('خطأ', 'فشل في تحديث البيانات');
    }
  };

  const checkNetworkStatus = async () => {
    try {
      // You can implement network status check here
      // For now, we'll assume online
      dispatch({ type: 'SET_ONLINE_STATUS', payload: true });
    } catch (error) {
      dispatch({ type: 'SET_ONLINE_STATUS', payload: false });
    }
  };

  const resetAppState = () => {
    dispatch({ type: 'RESET_STATE' });
  };

  // Initialize notifications and check network status on mount
  useEffect(() => {
    const initializeApp = async () => {
      await notificationService.initialize();
      await checkNetworkStatus();
    };

    initializeApp();
  }, []);

  const contextValue: AppContextType = {
    state,
    loadDashboardStats,
    loadSubscriptions,
    refreshSubscriptions,
    loadPayments,
    createPayment,
    loadJamiyas,
    joinJamiya,
    refreshAllData,
    checkNetworkStatus,
    resetAppState,
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use app context
export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  
  return context;
};

export default AppContext;
