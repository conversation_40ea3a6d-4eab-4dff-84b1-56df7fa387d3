# ==============================================
# Development Docker Compose Override
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
# ==============================================

version: '3.8'

services:
  # ==============================================
  # Database Services (Development)
  # ==============================================
  
  mongodb:
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: aljameia_dev
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./database/mongodb/init:/docker-entrypoint-initdb.d:ro
    command: mongod --replSet rs0 --bind_ip_all
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    environment:
      REDIS_PASSWORD: redis_dev_password
    ports:
      - "6379:6379"
    command: redis-server --requirepass redis_dev_password
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ==============================================
  # Application Services (Development)
  # ==============================================
  
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    environment:
      NODE_ENV: development
      PORT: 3000
      MONGODB_URI: *****************************************/aljameia_dev?authSource=admin
      REDIS_URL: redis://redis:6379
      REDIS_PASSWORD: redis_dev_password
      JWT_SECRET: dev-jwt-secret-key-not-for-production
      JWT_REFRESH_SECRET: dev-refresh-secret-key-not-for-production
      JWT_ACCESS_EXPIRY: 1h
      JWT_REFRESH_EXPIRY: 30d
      SMTP_HOST: mailhog
      SMTP_PORT: 1025
      SMTP_USER: ""
      SMTP_PASS: ""
      SMS_PROVIDER_URL: http://localhost:8080/mock-sms
      SMS_PROVIDER_KEY: dev-sms-key
      FRONTEND_URL: http://localhost:3000
      ADMIN_URL: http://localhost:3001
      LOG_LEVEL: debug
      ENABLE_CONSOLE_LOGS: "true"
    volumes:
      - ./backend/src:/app/src
      - ./backend/package.json:/app/package.json
      - ./backend/tsconfig.json:/app/tsconfig.json
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      - /app/node_modules
    ports:
      - "3000:3000"
      - "9229:9229" # Node.js debugger
    command: npm run dev
    stdin_open: true
    tty: true

  admin-dashboard:
    build:
      context: ./admin-dashboard
      dockerfile: Dockerfile
      target: development
    environment:
      NODE_ENV: development
      PORT: 3001
      NEXT_PUBLIC_API_URL: http://localhost:3000/api/v1
      NEXT_PUBLIC_WS_URL: ws://localhost:3000
      NEXTAUTH_URL: http://localhost:3001
      NEXTAUTH_SECRET: dev-nextauth-secret-not-for-production
    volumes:
      - ./admin-dashboard/src:/app/src
      - ./admin-dashboard/pages:/app/pages
      - ./admin-dashboard/public:/app/public
      - ./admin-dashboard/package.json:/app/package.json
      - ./admin-dashboard/next.config.js:/app/next.config.js
      - ./admin-dashboard/tsconfig.json:/app/tsconfig.json
      - /app/node_modules
      - /app/.next
    ports:
      - "3001:3001"
    command: npm run dev
    stdin_open: true
    tty: true

  # ==============================================
  # Development Tools
  # ==============================================
  
  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: aljameia-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - aljameia-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: aljameia-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:redis_dev_password
    ports:
      - "8081:8081"
    networks:
      - aljameia-network
    depends_on:
      - redis
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Mongo Express for MongoDB management
  mongo-express:
    image: mongo-express:latest
    container_name: aljameia-mongo-express
    restart: unless-stopped
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    ports:
      - "8082:8081"
    networks:
      - aljameia-network
    depends_on:
      - mongodb
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Mock SMS Service for testing
  mock-sms:
    image: wiremock/wiremock:latest
    container_name: aljameia-mock-sms
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./testing/wiremock:/home/<USER>
    networks:
      - aljameia-network
    command: --global-response-templating --verbose
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ==============================================
  # Development Nginx (Optional)
  # ==============================================
  
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    environment:
      NGINX_ENVSUBST_TEMPLATE_SUFFIX: .template
      BACKEND_HOST: backend
      ADMIN_HOST: admin-dashboard
    volumes:
      - ./nginx/templates:/etc/nginx/templates:ro
      - ./nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
      - app_uploads:/var/www/uploads
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - admin-dashboard

  # ==============================================
  # Development Monitoring (Optional)
  # ==============================================
  
  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: aljameia-jaeger
    restart: unless-stopped
    environment:
      COLLECTOR_OTLP_ENABLED: true
    ports:
      - "16686:16686" # Jaeger UI
      - "14268:14268" # Jaeger collector
    networks:
      - aljameia-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# ==============================================
# Development Volumes
# ==============================================
volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local

# ==============================================
# Development Networks
# ==============================================
networks:
  aljameia-network:
    driver: bridge
