package com.aljameia.app.ui.main

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
// import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
// import javax.inject.Inject

// @HiltViewModel
class MainViewModel /* @Inject constructor() */ : ViewModel() {

    private val _uiState = MutableStateFlow<MainUiState>(MainUiState.Loading)
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()

    private val _isLoading = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    init {
        loadData()
    }

    private fun loadData() {
        viewModelScope.launch {
            try {
                _uiState.value = MainUiState.Loading
                _isLoading.value = true
                
                // Simulate loading time
                delay(2000)
                
                // Simulate successful data loading
                _uiState.value = MainUiState.Success()
                _isLoading.value = false
                
            } catch (e: Exception) {
                _uiState.value = MainUiState.Error(e.message ?: "Unknown error occurred")
                _isLoading.value = false
            }
        }
    }

    fun retry() {
        loadData()
    }

    fun showMessage(message: String) {
        _uiState.value = MainUiState.Success(message)

        // Clear message after 3 seconds
        viewModelScope.launch {
            delay(3000)
            if (_uiState.value is MainUiState.Success) {
                _uiState.value = MainUiState.Success()
            }
        }
    }
}

sealed class MainUiState {
    object Loading : MainUiState()
    data class Success(val message: String? = null) : MainUiState()
    data class Error(val message: String) : MainUiState()
}
