import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/router';

// Types
interface AuthState {
  user: User | null;
  member: Member | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  requiresTwoFactor: boolean;
  tempToken: string | null;
}

type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; member?: Member } }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'AUTH_CLEAR_ERROR' }
  | { type: 'AUTH_REQUIRE_2FA'; payload: { tempToken: string } }
  | { type: 'AUTH_UPDATE_USER'; payload: User }
  | { type: 'AUTH_UPDATE_MEMBER'; payload: Member };

interface AuthContextType extends AuthState {
  login: (email: string, password: string, twoFactorCode?: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  clearError: () => void;
  checkAuth: () => Promise<void>;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  monthlyContribution?: number;
}

// Initial state
const initialState: AuthState = {
  user: null,
  member: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  requiresTwoFactor: false,
  tempToken: null,
};

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        member: action.payload.member || null,
        isAuthenticated: true,
        isLoading: false,
        error: null,
        requiresTwoFactor: false,
        tempToken: null,
      };

    case 'AUTH_FAILURE':
      return {
        ...state,
        user: null,
        member: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
        requiresTwoFactor: false,
        tempToken: null,
      };

    case 'AUTH_LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };

    case 'AUTH_CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    case 'AUTH_REQUIRE_2FA':
      return {
        ...state,
        isLoading: false,
        requiresTwoFactor: true,
        tempToken: action.payload.tempToken,
        error: null,
      };

    case 'AUTH_UPDATE_USER':
      return {
        ...state,
        user: action.payload,
      };

    case 'AUTH_UPDATE_MEMBER':
      return {
        ...state,
        member: action.payload,
      };

    default:
      return state;
  }
}

// Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const router = useRouter();

  // Check authentication status on mount
  useEffect(() => {
    checkAuth();
  }, []);

  // Auto-refresh token
  useEffect(() => {
    if (state.isAuthenticated) {
      const interval = setInterval(() => {
        refreshToken().catch(() => {
          // Silent fail - will be handled by the next API call
        });
      }, 14 * 60 * 1000); // Refresh every 14 minutes

      return () => clearInterval(interval);
    }
  }, [state.isAuthenticated]);

  // Check authentication
  const checkAuth = async (): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });
      
      const token = localStorage.getItem('accessToken');
      if (!token) {
        dispatch({ type: 'AUTH_LOGOUT' });
        return;
      }

      const response = await authService.getProfile();
      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          member: response.member,
        },
      });
    } catch (error) {
      logger.error('Auth check failed:', error);
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  };

  // Login
  const login = async (email: string, password: string, twoFactorCode?: string): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await authService.login({
        email,
        password,
        twoFactorCode,
      });

      if (response.requiresTwoFactor) {
        dispatch({
          type: 'AUTH_REQUIRE_2FA',
          payload: { tempToken: response.tempToken! },
        });
        return;
      }

      // Store tokens
      localStorage.setItem('accessToken', response.accessToken);
      localStorage.setItem('refreshToken', response.refreshToken);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          member: response.member,
        },
      });

      toast.success('تم تسجيل الدخول بنجاح');
      
      // Redirect to dashboard
      const redirectTo = router.query.redirect as string || '/dashboard';
      router.push(redirectTo);
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'فشل في تسجيل الدخول';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Register
  const register = async (data: RegisterData): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });

      await authService.register(data);

      toast.success('تم التسجيل بنجاح. يرجى التحقق من بريدك الإلكتروني');
      router.push('/auth/verify-email');
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'فشل في التسجيل';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Logout
  const logout = async (): Promise<void> => {
    try {
      await authService.logout();
    } catch (error) {
      logger.error('Logout error:', error);
    } finally {
      // Clear local storage
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      
      dispatch({ type: 'AUTH_LOGOUT' });
      toast.success('تم تسجيل الخروج بنجاح');
      router.push('/');
    }
  };

  // Refresh token
  const refreshToken = async (): Promise<void> => {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await authService.refreshToken(refreshToken);
      
      localStorage.setItem('accessToken', response.accessToken);
      localStorage.setItem('refreshToken', response.refreshToken);
    } catch (error) {
      logger.error('Token refresh failed:', error);
      
      // Clear tokens and logout
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      dispatch({ type: 'AUTH_LOGOUT' });
      
      // Redirect to login if not already there
      if (router.pathname !== '/auth/login') {
        router.push('/auth/login');
      }
      
      throw error;
    }
  };

  // Update profile
  const updateProfile = async (data: Partial<User>): Promise<void> => {
    try {
      const updatedUser = await authService.updateProfile(data);
      dispatch({ type: 'AUTH_UPDATE_USER', payload: updatedUser });
      toast.success('تم تحديث الملف الشخصي بنجاح');
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'فشل في تحديث الملف الشخصي';
      toast.error(errorMessage);
      throw error;
    }
  };

  // Clear error
  const clearError = (): void => {
    dispatch({ type: 'AUTH_CLEAR_ERROR' });
  };

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    refreshToken,
    updateProfile,
    clearError,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// HOC for protected routes
export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: { requireAuth?: boolean; requireRole?: string[] } = {}
) {
  const { requireAuth = true, requireRole = [] } = options;

  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading, user } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading) {
        if (requireAuth && !isAuthenticated) {
          router.push(`/auth/login?redirect=${encodeURIComponent(router.asPath)}`);
          return;
        }

        if (requireRole.length > 0 && user && !requireRole.includes(user.role)) {
          router.push('/unauthorized');
          return;
        }
      }
    }, [isAuthenticated, isLoading, user, router]);

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
        </div>
      );
    }

    if (requireAuth && !isAuthenticated) {
      return null;
    }

    if (requireRole.length > 0 && user && !requireRole.includes(user.role)) {
      return null;
    }

    return <WrappedComponent {...props} />;
  };
}
