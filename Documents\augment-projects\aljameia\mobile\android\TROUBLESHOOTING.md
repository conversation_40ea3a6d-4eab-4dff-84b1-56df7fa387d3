# دليل استكشاف الأخطاء - Troubleshooting Guide

## 🇸🇦 العربية

### إذا كان التطبيق لا يفتح

#### 1. تحقق من متطلبات النظام
- **نظام التشغيل**: Android 7.0 (API 24) أو أحدث
- **الذاكرة**: 2 جيجابايت RAM على الأقل
- **مساحة التخزين**: 100 ميجابايت متاحة

#### 2. خطوات استكشاف الأخطاء

**الخطوة 1: إعادة تثبيت التطبيق**
```bash
# إلغاء تثبيت النسخة القديمة
adb uninstall com.aljameia.app

# تثبيت النسخة الجديدة
adb install app/build/outputs/apk/debug/app-debug.apk
```

**الخطوة 2: تحقق من سجلات النظام**
```bash
# مسح السجلات القديمة
adb logcat -c

# عرض سجلات التطبيق
adb logcat | grep -i "aljameia\|crash\|error"
```

**الخطوة 3: تحقق من تثبيت التطبيق**
```bash
# التحقق من وجود التطبيق
adb shell pm list packages | grep aljameia

# تشغيل التطبيق يدوياً
adb shell am start -n com.aljameia.app/.app.ui.main.SimpleMainActivity
```

#### 3. الأخطاء الشائعة وحلولها

**خطأ: "App keeps stopping"**
- السبب: مشكلة في الكود أو نقص في الموارد
- الحل: تحقق من سجلات النظام لمعرفة السبب الدقيق

**خطأ: "App not installed"**
- السبب: تضارب في التوقيع أو نقص في المساحة
- الحل: احذف النسخة القديمة أولاً

**خطأ: شاشة بيضاء فقط**
- السبب: مشكلة في واجهة المستخدم
- الحل: تحقق من أن جميع الموارد موجودة

#### 4. النسخة المبسطة للاختبار

تم إنشاء نسخة مبسطة من التطبيق (`SimpleMainActivity`) تحتوي على:
- شاشة بسيطة بدون تعقيدات
- رسائل ترحيب بالعربية والإنجليزية
- زر اختبار للتأكد من عمل التطبيق

### معلومات التطبيق الحالي
- **اسم الحزمة**: `com.aljameia.app`
- **النشاط الرئيسي**: `SimpleMainActivity`
- **حجم الـ APK**: 42 ميجابايت
- **الإصدار**: 1.0.0 (Debug)

---

## 🇺🇸 English

### If the App Won't Open

#### 1. Check System Requirements
- **OS**: Android 7.0 (API 24) or higher
- **RAM**: At least 2GB
- **Storage**: 100MB available space

#### 2. Troubleshooting Steps

**Step 1: Reinstall the App**
```bash
# Uninstall old version
adb uninstall com.aljameia.app

# Install new version
adb install app/build/outputs/apk/debug/app-debug.apk
```

**Step 2: Check System Logs**
```bash
# Clear old logs
adb logcat -c

# View app logs
adb logcat | grep -i "aljameia\|crash\|error"
```

**Step 3: Verify Installation**
```bash
# Check if app is installed
adb shell pm list packages | grep aljameia

# Launch app manually
adb shell am start -n com.aljameia.app/.app.ui.main.SimpleMainActivity
```

#### 3. Common Errors and Solutions

**Error: "App keeps stopping"**
- Cause: Code issue or resource shortage
- Solution: Check system logs for exact cause

**Error: "App not installed"**
- Cause: Signature conflict or insufficient space
- Solution: Delete old version first

**Error: White screen only**
- Cause: UI rendering issue
- Solution: Verify all resources are present

#### 4. Simplified Test Version

A simplified version (`SimpleMainActivity`) has been created with:
- Simple screen without complexities
- Welcome messages in Arabic and English
- Test button to verify app functionality

### Current App Information
- **Package Name**: `com.aljameia.app`
- **Main Activity**: `SimpleMainActivity`
- **APK Size**: 42MB
- **Version**: 1.0.0 (Debug)

---

## 🔧 Developer Commands

### Build Commands
```bash
# Clean build
./gradlew clean assembleDebug

# Install on connected device
./gradlew installDebug

# Run tests
./gradlew test
```

### ADB Commands
```bash
# List connected devices
adb devices

# Install APK
adb install -r app/build/outputs/apk/debug/app-debug.apk

# Launch app
adb shell am start -n com.aljameia.app/.app.ui.main.SimpleMainActivity

# View logs
adb logcat -s "AlJameia"

# Uninstall app
adb uninstall com.aljameia.app
```

### Log Analysis
Look for these log tags:
- `AlJameia`: App-specific logs
- `AndroidRuntime`: Crash logs
- `System.err`: Error messages

---

## 📞 Support

إذا استمرت المشكلة - If the problem persists:

1. **جمع المعلومات - Collect Information**:
   - نموذج الجهاز - Device model
   - إصدار Android - Android version
   - رسائل الخطأ - Error messages
   - سجلات النظام - System logs

2. **التواصل - Contact**:
   - Email: <EMAIL>
   - Include: Device info + error logs

**تم التحديث - Last Updated**: June 18, 2024
