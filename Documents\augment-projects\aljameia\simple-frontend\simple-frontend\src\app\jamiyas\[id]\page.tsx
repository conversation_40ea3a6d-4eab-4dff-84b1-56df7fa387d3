'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  ArrowLeftIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  BanknotesIcon,
  UsersIcon,
  PencilIcon,
  PlusIcon,
  EyeIcon,
  DocumentTextIcon,
  ChartBarIcon,
  BellIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import type { Jamiya, JamiyaMember, JamiyaCycle, Contribution } from '@/types/jamiya';

interface JamiyaDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function JamiyaDetailPage({ params }: JamiyaDetailPageProps) {
  const { user } = useAuth();
  const [jamiya, setJamiya] = useState<Jamiya | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [jamiyaId, setJamiyaId] = useState<string>('');

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setJamiyaId(resolvedParams.id);
    };
    getParams();
  }, [params]);

  useEffect(() => {
    if (!jamiyaId) return;

    const loadJamiya = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));

          // Mock data - في التطبيق الحقيقي سيتم جلبها من API
      const mockJamiya: Jamiya = {
        id: jamiyaId,
        name: 'جمعية الأصدقاء',
        description: 'جمعية مالية بين مجموعة من الأصدقاء المقربين لتوفير المال وتحقيق الأهداف المالية',
        type: 'simple',
        status: 'active',
        totalMembers: 12,
        monthlyAmount: 5000,
        currency: 'SAR',
        duration: 12,
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        createdAt: '2023-12-15T10:00:00Z',
        updatedAt: '2024-06-18T14:30:00Z',
        members: [
          {
            id: '1',
            userId: 'user1',
            jamiyaId: jamiyaId,
        name: 'أحمد محمد الراشد',
        email: '<EMAIL>',
        phone: '+************',
        nationalId: '**********',
        joinDate: '2023-12-20T10:00:00Z',
        memberNumber: 1,
        status: 'active',
        assignedCycle: 1,
        totalPaid: 30000,
        totalReceived: 60000,
        balance: -30000,
        cyclePreferences: [1, 2, 3],
        hasBidOnCycle: false,
        guaranteedMembers: [],
        paymentHistory: [],
        missedPayments: 0,
        lastPaymentDate: '2024-06-01T10:00:00Z',
        isActive: true
      },
      {
        id: '2',
        userId: 'user2',
        jamiyaId: jamiyaId,
        name: 'سارة أحمد العتيبي',
        email: '<EMAIL>',
        phone: '+966502345678',
        nationalId: '2345678901',
        joinDate: '2023-12-20T11:00:00Z',
        memberNumber: 2,
        status: 'active',
        assignedCycle: 5,
        totalPaid: 30000,
        totalReceived: 0,
        balance: 30000,
        cyclePreferences: [5, 6, 7],
        hasBidOnCycle: false,
        guaranteedMembers: [],
        paymentHistory: [],
        missedPayments: 0,
        lastPaymentDate: '2024-06-01T11:00:00Z',
        isActive: true
      },
      {
        id: '3',
        userId: 'user3',
        jamiyaId: jamiyaId,
        name: 'محمد سعد الغامدي',
        email: '<EMAIL>',
        phone: '+966503456789',
        nationalId: '3456789012',
        joinDate: '2023-12-20T12:00:00Z',
        memberNumber: 3,
        status: 'late',
        assignedCycle: 8,
        totalPaid: 25000,
        totalReceived: 0,
        balance: 25000,
        cyclePreferences: [8, 9, 10],
        hasBidOnCycle: false,
        guaranteedMembers: [],
        paymentHistory: [],
        missedPayments: 1,
        lastPaymentDate: '2024-05-01T12:00:00Z',
        isActive: true
      }
    ],
    cycles: [
      {
        id: '1',
        jamiyaId: jamiyaId,
        cycleNumber: 1,
        dueDate: '2024-01-31',
        completedDate: '2024-01-31',
        totalAmount: 60000,
        collectedAmount: 60000,
        distributedAmount: 60000,
        recipientId: '1',
        recipientName: 'أحمد محمد الراشد',
        bids: [],
        contributions: [],
        status: 'completed',
        distributionDate: '2024-01-31',
        distributionMethod: 'bank_transfer'
      },
      {
        id: '2',
        jamiyaId: jamiyaId,
        cycleNumber: 2,
        dueDate: '2024-02-29',
        completedDate: '2024-02-29',
        totalAmount: 60000,
        collectedAmount: 60000,
        distributedAmount: 60000,
        recipientId: '4',
        recipientName: 'فاطمة علي الزهراني',
        bids: [],
        contributions: [],
        status: 'completed',
        distributionDate: '2024-02-29',
        distributionMethod: 'bank_transfer'
      },
      {
        id: '3',
        jamiyaId: jamiyaId,
        cycleNumber: 6,
        dueDate: '2024-06-30',
        totalAmount: 60000,
        collectedAmount: 55000,
        distributedAmount: 0,
        recipientId: '2',
        recipientName: 'سارة أحمد العتيبي',
        bids: [],
        contributions: [],
        status: 'collecting',
        distributionMethod: 'bank_transfer'
      }
    ],
    contributions: [],
    organizer: {
      id: 'org1',
      name: 'أحمد المنظم',
      email: '<EMAIL>',
      phone: '+************',
      nationalId: '**********',
      organizingExperience: 5,
      successfulJamiyas: 8,
      rating: 4.8,
      isVerified: true
    },
    guarantors: [],
    rules: {
      allowEarlyWithdrawal: false,
      allowLateJoining: false,
      requireGuarantor: true,
      lateFee: 50,
      earlyWithdrawalDiscount: 0,
      lateWithdrawalBonus: 0,
      allowBidding: false,
      minimumBid: 0,
      biddingDeadline: 0,
      gracePeriod: 3,
      maxMissedPayments: 2,
      autoSuspension: true,
      advanceNotice: 7,
      withdrawalPenalty: 100
    },
    statistics: {
      totalCollected: 240000,
      totalDistributed: 120000,
      totalPending: 120000,
      activeMembers: 11,
      suspendedMembers: 0,
      completedMembers: 2,
      onTimePayments: 64,
      latePayments: 2,
      missedPayments: 1,
      completedCycles: 2,
      pendingCycles: 10,
      collectionRate: 97.2,
      onTimeRate: 95.5,
      completionRate: 100
    },
    settings: {
      enableEmailNotifications: true,
      enableSmsNotifications: true,
      enablePushNotifications: true,
      paymentReminderDays: [7, 3, 1],
      cycleReminderDays: [7, 1],
      autoProcessPayments: false,
      autoDistributeFunds: false,
      autoSuspendDefaulters: true,
      showMemberDetails: true,
      showPaymentHistory: false,
      allowMemberCommunication: true,
      requireTwoFactorAuth: true,
      requirePaymentVerification: true,
      logAllActivities: true
    }
  };

      setJamiya(mockJamiya);
      setIsLoading(false);
    };

    loadJamiya();
  }, [jamiyaId]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-blue-500" />;
      case 'late':
        return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />;
      case 'suspended':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'completed': return 'مكتمل';
      case 'late': return 'متأخر';
      case 'suspended': return 'موقوف';
      default: return status;
    }
  };

  const getCycleStatusText = (status: string) => {
    switch (status) {
      case 'upcoming': return 'قادمة';
      case 'collecting': return 'جمع المساهمات';
      case 'ready': return 'جاهزة للتوزيع';
      case 'distributed': return 'تم التوزيع';
      case 'completed': return 'مكتملة';
      default: return status;
    }
  };

  const tabs = [
    { id: 'overview', name: 'نظرة عامة', icon: ChartBarIcon },
    { id: 'members', name: 'الأعضاء', icon: UsersIcon },
    { id: 'cycles', name: 'الدورات', icon: CalendarIcon },
    { id: 'contributions', name: 'المساهمات', icon: BanknotesIcon },
    { id: 'settings', name: 'الإعدادات', icon: CogIcon }
  ];

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['jamiya_management']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل تفاصيل الجمعية...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!jamiya) {
    return (
      <ProtectedRoute requiredPermissions={['jamiya_management']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <XCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">الجمعية غير موجودة</h2>
            <p className="text-gray-600 mb-4">لم يتم العثور على الجمعية المطلوبة</p>
            <Link href="/jamiyas">
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                العودة إلى قائمة الجمعيات
              </button>
            </Link>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['jamiya_management']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">منصة الجمعيات المالية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/jamiyas">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    إدارة الجمعيات
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Jamiya Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <h1 className="text-2xl font-bold text-gray-900 ml-3">{jamiya.name}</h1>
                    <div className="flex items-center">
                      {getStatusIcon(jamiya.status)}
                      <span className="mr-1 text-sm text-gray-600">
                        {getStatusText(jamiya.status)}
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-4">{jamiya.description}</p>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <CurrencyDollarIcon className="h-4 w-4 ml-2" />
                      <span>{formatCurrency(jamiya.monthlyAmount)} شهرياً</span>
                    </div>

                    <div className="flex items-center text-sm text-gray-500">
                      <UsersIcon className="h-4 w-4 ml-2" />
                      <span>{jamiya.statistics.activeMembers}/{jamiya.totalMembers} عضو</span>
                    </div>

                    <div className="flex items-center text-sm text-gray-500">
                      <CalendarIcon className="h-4 w-4 ml-2" />
                      <span>{jamiya.duration} شهر</span>
                    </div>

                    <div className="flex items-center text-sm text-gray-500">
                      <BanknotesIcon className="h-4 w-4 ml-2" />
                      <span>{formatCurrency(jamiya.statistics.totalCollected)}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 space-x-reverse">
                  <Link href={`/jamiyas/${jamiya.id}/edit`}>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                      <PencilIcon className="h-4 w-4 ml-2" />
                      تعديل
                    </button>
                  </Link>
                  <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                    <PlusIcon className="h-4 w-4 ml-2" />
                    إضافة عضو
                  </button>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <BanknotesIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(jamiya.statistics.totalCollected)}
                  </p>
                  <p className="text-sm text-gray-600">إجمالي المجموع</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-blue-600">
                    {formatCurrency(jamiya.statistics.totalDistributed)}
                  </p>
                  <p className="text-sm text-gray-600">إجمالي الموزع</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <UsersIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-purple-600">
                    {jamiya.statistics.activeMembers}
                  </p>
                  <p className="text-sm text-gray-600">الأعضاء النشطون</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <ChartBarIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-orange-600">
                    {jamiya.statistics.onTimeRate.toFixed(1)}%
                  </p>
                  <p className="text-sm text-gray-600">معدل الالتزام</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-6"
          >
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 space-x-reverse">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      flex items-center py-2 px-1 border-b-2 font-medium text-sm
                      ${activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <tab.icon className="h-5 w-5 ml-2" />
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>
          </motion.div>

          {/* Tab Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Current Cycle */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">الدورة الحالية</h3>
                  {jamiya.cycles.find(c => c.status === 'collecting') ? (
                    <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                      {(() => {
                        const currentCycle = jamiya.cycles.find(c => c.status === 'collecting')!;
                        return (
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-blue-900">
                                الدورة {currentCycle.cycleNumber} - {currentCycle.recipientName}
                              </h4>
                              <p className="text-sm text-blue-700">
                                تاريخ الاستحقاق: {new Date(currentCycle.dueDate).toLocaleDateString('ar-SA')}
                              </p>
                              <div className="mt-2">
                                <div className="flex items-center justify-between text-sm">
                                  <span>التقدم:</span>
                                  <span>{formatCurrency(currentCycle.collectedAmount)} / {formatCurrency(currentCycle.totalAmount)}</span>
                                </div>
                                <div className="w-full bg-blue-200 rounded-full h-2 mt-1">
                                  <div
                                    className="bg-blue-600 h-2 rounded-full"
                                    style={{ width: `${(currentCycle.collectedAmount / currentCycle.totalAmount) * 100}%` }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-2xl font-bold text-blue-600">
                                {((currentCycle.collectedAmount / currentCycle.totalAmount) * 100).toFixed(1)}%
                              </div>
                              <div className="text-sm text-blue-700">مكتمل</div>
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <CalendarIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                      <p>لا توجد دورة نشطة حالياً</p>
                    </div>
                  )}
                </div>

                {/* Recent Activity */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">النشاط الأخير</h3>
                  <div className="space-y-4">
                    <div className="flex items-center p-3 bg-green-50 rounded-lg">
                      <CheckCircleIcon className="h-5 w-5 text-green-500 ml-3" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          تم استلام مساهمة من سارة العتيبي
                        </p>
                        <p className="text-xs text-gray-500">منذ ساعتين</p>
                      </div>
                      <span className="text-sm font-medium text-green-600">
                        {formatCurrency(5000)}
                      </span>
                    </div>

                    <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
                      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 ml-3" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          تأخر في المساهمة من محمد الغامدي
                        </p>
                        <p className="text-xs text-gray-500">منذ يوم واحد</p>
                      </div>
                      <span className="text-sm font-medium text-yellow-600">
                        متأخر
                      </span>
                    </div>

                    <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                      <BanknotesIcon className="h-5 w-5 text-blue-500 ml-3" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          تم توزيع الدورة الثانية على فاطمة الزهراني
                        </p>
                        <p className="text-xs text-gray-500">منذ 3 أيام</p>
                      </div>
                      <span className="text-sm font-medium text-blue-600">
                        {formatCurrency(60000)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Members Tab */}
            {activeTab === 'members' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">قائمة الأعضاء</h3>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                      <PlusIcon className="h-4 w-4 ml-2" />
                      إضافة عضو
                    </button>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          العضو
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الدورة المخصصة
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          إجمالي المدفوع
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الرصيد
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الحالة
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {jamiya.members.map((member) => (
                        <tr key={member.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <span className="text-blue-600 font-semibold text-sm">
                                  {member.name.charAt(0)}
                                </span>
                              </div>
                              <div className="mr-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {member.name}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {member.email}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            الدورة {member.assignedCycle}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(member.totalPaid)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span className={`font-medium ${
                              member.balance >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {formatCurrency(Math.abs(member.balance))}
                              {member.balance < 0 && ' (مستحق)'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {getStatusIcon(member.status)}
                              <span className="mr-1 text-sm text-gray-600">
                                {getStatusText(member.status)}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <button className="text-blue-600 hover:text-blue-900 p-1">
                                <EyeIcon className="h-4 w-4" />
                              </button>
                              <button className="text-green-600 hover:text-green-900 p-1">
                                <PencilIcon className="h-4 w-4" />
                              </button>
                              <button className="text-red-600 hover:text-red-900 p-1">
                                <XCircleIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Cycles Tab */}
            {activeTab === 'cycles' && (
              <div className="space-y-6">
                {jamiya.cycles.map((cycle) => (
                  <div key={cycle.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">
                          الدورة {cycle.cycleNumber}
                        </h4>
                        <p className="text-sm text-gray-600">
                          المستفيد: {cycle.recipientName}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-500">
                          {getCycleStatusText(cycle.status)}
                        </div>
                        <div className="text-lg font-bold text-gray-900">
                          {formatCurrency(cycle.totalAmount)}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <span className="text-sm text-gray-500">تاريخ الاستحقاق:</span>
                        <div className="font-medium">
                          {new Date(cycle.dueDate).toLocaleDateString('ar-SA')}
                        </div>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">المبلغ المجموع:</span>
                        <div className="font-medium text-green-600">
                          {formatCurrency(cycle.collectedAmount)}
                        </div>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">المبلغ الموزع:</span>
                        <div className="font-medium text-blue-600">
                          {formatCurrency(cycle.distributedAmount)}
                        </div>
                      </div>
                    </div>

                    {cycle.status === 'collecting' && (
                      <div className="mb-4">
                        <div className="flex items-center justify-between text-sm mb-1">
                          <span>التقدم:</span>
                          <span>{((cycle.collectedAmount / cycle.totalAmount) * 100).toFixed(1)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${(cycle.collectedAmount / cycle.totalAmount) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    )}

                    {cycle.completedDate && (
                      <div className="text-sm text-gray-500">
                        تم الإكمال في: {new Date(cycle.completedDate).toLocaleDateString('ar-SA')}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Other tabs content would go here */}
            {activeTab === 'contributions' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">المساهمات</h3>
                <div className="text-center py-8 text-gray-500">
                  <BanknotesIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>سيتم إضافة تفاصيل المساهمات قريباً</p>
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">إعدادات الجمعية</h3>
                <div className="text-center py-8 text-gray-500">
                  <CogIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>سيتم إضافة إعدادات الجمعية قريباً</p>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
