import { httpClient, ApiResponse } from './api';

// Report Types
export interface DashboardStats {
  totalInvestment: number;
  totalReturns: number;
  activeShares: number;
  pendingPayments: number;
  completedJamiyas: number;
  activeJamiyas: number;
  totalEarnings: number;
  monthlyGrowth: number;
}

export interface PerformanceReport {
  period: string;
  investment: number;
  returns: number;
  profit: number;
  profitPercentage: number;
  jamiyaCount: number;
}

export interface ShareDistribution {
  jamiyaId: string;
  jamiyaName: string;
  shares: number;
  value: number;
  percentage: number;
  status: string;
}

export interface FutureProjection {
  month: string;
  projectedReturns: number;
  confidence: number;
  factors: string[];
}

export interface InvestmentCalculation {
  jamiyaType: string;
  shares: number;
  pricePerShare: number;
  duration: number;
  returnRate: number;
  riskLevel: 'low' | 'medium' | 'high';
  totalInvestment: number;
  expectedReturns: number;
  netProfit: number;
  profitPercentage: number;
  monthlyAverage: number;
  breakEvenPoint: number;
  recommendations: string[];
}

export interface ReportFilters {
  period?: 'month' | 'quarter' | 'year' | 'all';
  jamiyaId?: string;
  startDate?: string;
  endDate?: string;
  type?: string;
}

// Report Service Class
class ReportService {
  /**
   * Get dashboard statistics
   */
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      const response = await httpClient.get<DashboardStats>('/reports/dashboard');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get dashboard stats');
    } catch (error) {
      console.error('Get dashboard stats error:', error);
      throw error;
    }
  }

  /**
   * Get performance report
   */
  async getPerformanceReport(filters?: ReportFilters): Promise<PerformanceReport[]> {
    try {
      const response = await httpClient.get<PerformanceReport[]>('/reports/performance', filters);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get performance report');
    } catch (error) {
      console.error('Get performance report error:', error);
      throw error;
    }
  }

  /**
   * Get share distribution report
   */
  async getShareDistribution(): Promise<ShareDistribution[]> {
    try {
      const response = await httpClient.get<ShareDistribution[]>('/reports/shares');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get share distribution');
    } catch (error) {
      console.error('Get share distribution error:', error);
      throw error;
    }
  }

  /**
   * Get future projections
   */
  async getFutureProjections(months: number = 12): Promise<FutureProjection[]> {
    try {
      const response = await httpClient.get<FutureProjection[]>('/reports/projections', {
        months,
      });
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get future projections');
    } catch (error) {
      console.error('Get future projections error:', error);
      throw error;
    }
  }

  /**
   * Calculate investment returns
   */
  async calculateInvestment(params: {
    jamiyaType: string;
    shares: number;
    pricePerShare: number;
    duration: number;
    returnRate: number;
    riskLevel: 'low' | 'medium' | 'high';
  }): Promise<InvestmentCalculation> {
    try {
      const response = await httpClient.post<InvestmentCalculation>('/reports/calculate', params);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to calculate investment');
    } catch (error) {
      console.error('Calculate investment error:', error);
      throw error;
    }
  }

  /**
   * Export report as PDF
   */
  async exportReportPDF(reportType: string, filters?: ReportFilters): Promise<Blob> {
    try {
      const response = await fetch(`${httpClient.baseURL}/reports/export/pdf`, {
        method: 'POST',
        headers: await httpClient.getAuthHeaders(),
        body: JSON.stringify({ reportType, filters }),
      });

      if (!response.ok) {
        throw new Error('Failed to export report');
      }

      return await response.blob();
    } catch (error) {
      console.error('Export PDF error:', error);
      throw error;
    }
  }

  /**
   * Export report as Excel
   */
  async exportReportExcel(reportType: string, filters?: ReportFilters): Promise<Blob> {
    try {
      const response = await fetch(`${httpClient.baseURL}/reports/export/excel`, {
        method: 'POST',
        headers: await httpClient.getAuthHeaders(),
        body: JSON.stringify({ reportType, filters }),
      });

      if (!response.ok) {
        throw new Error('Failed to export report');
      }

      return await response.blob();
    } catch (error) {
      console.error('Export Excel error:', error);
      throw error;
    }
  }

  /**
   * Get monthly summary
   */
  async getMonthlySummary(year: number, month: number): Promise<{
    totalInvestment: number;
    totalReturns: number;
    newJamiyas: number;
    completedPayments: number;
    pendingPayments: number;
    profitLoss: number;
  }> {
    try {
      const response = await httpClient.get(`/reports/monthly/${year}/${month}`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get monthly summary');
    } catch (error) {
      console.error('Get monthly summary error:', error);
      throw error;
    }
  }

  /**
   * Get yearly summary
   */
  async getYearlySummary(year: number): Promise<{
    totalInvestment: number;
    totalReturns: number;
    totalProfit: number;
    profitPercentage: number;
    bestPerformingJamiya: string;
    worstPerformingJamiya: string;
    monthlyBreakdown: PerformanceReport[];
  }> {
    try {
      const response = await httpClient.get(`/reports/yearly/${year}`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get yearly summary');
    } catch (error) {
      console.error('Get yearly summary error:', error);
      throw error;
    }
  }

  /**
   * Get investment recommendations
   */
  async getInvestmentRecommendations(): Promise<{
    recommendations: Array<{
      type: string;
      title: string;
      description: string;
      expectedReturn: number;
      riskLevel: string;
      duration: number;
      minInvestment: number;
    }>;
    riskProfile: string;
    diversificationScore: number;
  }> {
    try {
      const response = await httpClient.get('/reports/recommendations');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get recommendations');
    } catch (error) {
      console.error('Get recommendations error:', error);
      throw error;
    }
  }

  /**
   * Get portfolio analysis
   */
  async getPortfolioAnalysis(): Promise<{
    totalValue: number;
    allocation: Array<{
      category: string;
      value: number;
      percentage: number;
    }>;
    performance: {
      daily: number;
      weekly: number;
      monthly: number;
      yearly: number;
    };
    riskMetrics: {
      volatility: number;
      sharpeRatio: number;
      maxDrawdown: number;
    };
  }> {
    try {
      const response = await httpClient.get('/reports/portfolio');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get portfolio analysis');
    } catch (error) {
      console.error('Get portfolio analysis error:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const reportService = new ReportService();
export default reportService;
