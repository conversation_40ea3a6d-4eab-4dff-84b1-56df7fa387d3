// Financial Types and Interfaces

export interface Transaction {
  id: string;
  type: TransactionType;
  category: TransactionCategory;
  amount: number;
  currency: string;
  description: string;
  date: string;
  dueDate?: string;
  status: TransactionStatus;
  paymentMethod?: PaymentMethod;
  reference: string;
  receiptUrl?: string;
  attachments: string[];
  tags: string[];
  memberId?: string;
  memberName?: string;
  approvedBy?: string;
  approvalDate?: string;
  notes?: string;
  recurringId?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  lastModifiedBy: string;
}

export interface Budget {
  id: string;
  name: string;
  description: string;
  period: BudgetPeriod;
  startDate: string;
  endDate: string;
  totalBudget: number;
  currency: string;
  categories: BudgetCategory[];
  status: BudgetStatus;
  approvedBy?: string;
  approvalDate?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface BudgetCategory {
  id: string;
  name: string;
  allocatedAmount: number;
  spentAmount: number;
  remainingAmount: number;
  percentage: number;
  transactions: string[]; // Transaction IDs
  subcategories?: BudgetSubcategory[];
}

export interface BudgetSubcategory {
  id: string;
  name: string;
  allocatedAmount: number;
  spentAmount: number;
  remainingAmount: number;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  type: InvoiceType;
  memberId?: string;
  memberName?: string;
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
  clientAddress?: Address;
  items: InvoiceItem[];
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  currency: string;
  issueDate: string;
  dueDate: string;
  status: InvoiceStatus;
  paymentTerms: string;
  notes?: string;
  attachments: string[];
  paymentHistory: PaymentRecord[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate: number;
  category: string;
}

export interface PaymentRecord {
  id: string;
  amount: number;
  date: string;
  method: PaymentMethod;
  reference: string;
  notes?: string;
}

export interface FinancialReport {
  id: string;
  name: string;
  type: ReportType;
  period: ReportPeriod;
  startDate: string;
  endDate: string;
  data: ReportData;
  generatedAt: string;
  generatedBy: string;
  format: ReportFormat;
  fileUrl?: string;
}

export interface ReportData {
  summary: FinancialSummary;
  transactions: Transaction[];
  budgetAnalysis?: BudgetAnalysis;
  categoryBreakdown: CategoryBreakdown[];
  trends: TrendData[];
  comparisons?: ComparisonData[];
}

export interface FinancialSummary {
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
  pendingIncome: number;
  pendingExpenses: number;
  cashFlow: number;
  previousPeriodComparison: {
    incomeChange: number;
    expenseChange: number;
    netIncomeChange: number;
  };
}

export interface BudgetAnalysis {
  totalBudget: number;
  totalSpent: number;
  remainingBudget: number;
  utilizationPercentage: number;
  categoryPerformance: CategoryPerformance[];
  overBudgetCategories: string[];
  underBudgetCategories: string[];
}

export interface CategoryPerformance {
  category: string;
  budgeted: number;
  spent: number;
  variance: number;
  variancePercentage: number;
}

export interface CategoryBreakdown {
  category: string;
  amount: number;
  percentage: number;
  transactionCount: number;
  trend: 'up' | 'down' | 'stable';
}

export interface TrendData {
  period: string;
  income: number;
  expenses: number;
  netIncome: number;
}

export interface ComparisonData {
  period: string;
  current: number;
  previous: number;
  change: number;
  changePercentage: number;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

// Enums
export type TransactionType = 'income' | 'expense' | 'transfer';

export type TransactionCategory = 
  | 'membership_fees'
  | 'donations'
  | 'event_revenue'
  | 'investment_income'
  | 'grants'
  | 'other_income'
  | 'office_expenses'
  | 'utilities'
  | 'salaries'
  | 'marketing'
  | 'events'
  | 'equipment'
  | 'travel'
  | 'professional_services'
  | 'insurance'
  | 'taxes'
  | 'other_expenses';

export type TransactionStatus = 'pending' | 'completed' | 'cancelled' | 'failed' | 'refunded';

export type PaymentMethod = 
  | 'cash'
  | 'bank_transfer'
  | 'credit_card'
  | 'debit_card'
  | 'online_payment'
  | 'check'
  | 'mobile_payment';

export type BudgetPeriod = 'monthly' | 'quarterly' | 'yearly' | 'custom';

export type BudgetStatus = 'draft' | 'active' | 'completed' | 'cancelled';

export type InvoiceType = 'membership' | 'event' | 'service' | 'product' | 'other';

export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';

export type ReportType = 
  | 'income_statement'
  | 'balance_sheet'
  | 'cash_flow'
  | 'budget_analysis'
  | 'transaction_summary'
  | 'member_payments'
  | 'category_analysis';

export type ReportPeriod = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';

export type ReportFormat = 'pdf' | 'excel' | 'csv' | 'json';

// Search and Filter Types
export interface TransactionFilters {
  search?: string;
  type?: TransactionType[];
  category?: TransactionCategory[];
  status?: TransactionStatus[];
  paymentMethod?: PaymentMethod[];
  dateFrom?: string;
  dateTo?: string;
  amountFrom?: number;
  amountTo?: number;
  memberId?: string;
  tags?: string[];
}

export interface TransactionSortOptions {
  field: 'date' | 'amount' | 'description' | 'category' | 'status';
  direction: 'asc' | 'desc';
}

// Form Types
export interface CreateTransactionForm {
  type: TransactionType;
  category: TransactionCategory;
  amount: number;
  description: string;
  date: string;
  dueDate?: string;
  paymentMethod?: PaymentMethod;
  memberId?: string;
  tags: string[];
  notes?: string;
  attachments: File[];
}

export interface CreateBudgetForm {
  name: string;
  description: string;
  period: BudgetPeriod;
  startDate: string;
  endDate: string;
  categories: {
    name: string;
    allocatedAmount: number;
  }[];
}

export interface CreateInvoiceForm {
  type: InvoiceType;
  memberId?: string;
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
  items: {
    description: string;
    quantity: number;
    unitPrice: number;
  }[];
  dueDate: string;
  paymentTerms: string;
  notes?: string;
}

// Statistics Types
export interface FinancialStatistics {
  currentMonth: {
    income: number;
    expenses: number;
    netIncome: number;
    transactionCount: number;
  };
  previousMonth: {
    income: number;
    expenses: number;
    netIncome: number;
    transactionCount: number;
  };
  yearToDate: {
    income: number;
    expenses: number;
    netIncome: number;
    transactionCount: number;
  };
  topCategories: {
    income: CategoryBreakdown[];
    expenses: CategoryBreakdown[];
  };
  recentTransactions: Transaction[];
  upcomingPayments: Transaction[];
  overdueInvoices: Invoice[];
}

// API Response Types
export interface TransactionListResponse {
  transactions: Transaction[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface FinancialDashboardResponse {
  statistics: FinancialStatistics;
  budgetSummary: BudgetAnalysis;
  cashFlowData: TrendData[];
  alerts: FinancialAlert[];
}

export interface FinancialAlert {
  id: string;
  type: 'budget_exceeded' | 'payment_overdue' | 'low_balance' | 'unusual_expense';
  severity: 'low' | 'medium' | 'high';
  title: string;
  message: string;
  date: string;
  isRead: boolean;
  actionUrl?: string;
}
