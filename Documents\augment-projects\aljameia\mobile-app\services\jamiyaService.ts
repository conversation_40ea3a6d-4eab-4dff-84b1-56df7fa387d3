import { httpClient, ApiResponse, PaginatedResponse } from './api';

// Jamiya Types
export interface Jamiya {
  id: string;
  name: string;
  type: 'investment' | 'savings' | 'social' | 'mixed';
  description: string;
  totalShares: number;
  sharePrice: number;
  duration: number; // in months
  startDate: string;
  endDate: string;
  status: 'planning' | 'active' | 'completed' | 'cancelled';
  memberCount: number;
  totalAmount: number;
  currentRound: number;
  isPublic: boolean;
  rules: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface JamiyaMember {
  id: string;
  jamiyaId: string;
  userId: string;
  userName: string;
  userEmail: string;
  shares: number;
  joinDate: string;
  status: 'active' | 'pending' | 'suspended' | 'left';
  totalPaid: number;
  totalDue: number;
  lastPaymentDate?: string;
  nextPaymentDate: string;
}

export interface Subscription {
  id: string;
  jamiyaId: string;
  jamiyaName: string;
  shares: number;
  pricePerShare: number;
  monthlyAmount: number;
  nextPaymentDate: string;
  status: 'active' | 'pending' | 'overdue' | 'suspended';
  totalPaid: number;
  totalDue: number;
  joinDate: string;
}

export interface CreateJamiyaRequest {
  name: string;
  type: 'investment' | 'savings' | 'social' | 'mixed';
  description: string;
  totalShares: number;
  sharePrice: number;
  duration: number;
  startDate: string;
  isPublic: boolean;
  rules: string[];
}

export interface JoinJamiyaRequest {
  jamiyaId: string;
  shares: number;
}

export interface JamiyaStats {
  totalJamiyas: number;
  activeJamiyas: number;
  totalInvestment: number;
  totalReturns: number;
  activeShares: number;
  completedJamiyas: number;
}

// Jamiya Service Class
class JamiyaService {
  /**
   * Get user's jamiya statistics
   */
  async getStats(): Promise<JamiyaStats> {
    try {
      const response = await httpClient.get<JamiyaStats>('/jamiyas/stats');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get jamiya stats');
    } catch (error) {
      console.error('Get jamiya stats error:', error);
      throw error;
    }
  }

  /**
   * Get all jamiyas with pagination
   */
  async getJamiyas(params?: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
    search?: string;
  }): Promise<PaginatedResponse<Jamiya>> {
    try {
      const response = await httpClient.get<PaginatedResponse<Jamiya>>('/jamiyas', params);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get jamiyas');
    } catch (error) {
      console.error('Get jamiyas error:', error);
      throw error;
    }
  }

  /**
   * Get jamiya by ID
   */
  async getJamiya(id: string): Promise<Jamiya> {
    try {
      const response = await httpClient.get<Jamiya>(`/jamiyas/${id}`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get jamiya');
    } catch (error) {
      console.error('Get jamiya error:', error);
      throw error;
    }
  }

  /**
   * Create new jamiya
   */
  async createJamiya(jamiyaData: CreateJamiyaRequest): Promise<Jamiya> {
    try {
      const response = await httpClient.post<Jamiya>('/jamiyas', jamiyaData);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to create jamiya');
    } catch (error) {
      console.error('Create jamiya error:', error);
      throw error;
    }
  }

  /**
   * Update jamiya
   */
  async updateJamiya(id: string, jamiyaData: Partial<CreateJamiyaRequest>): Promise<Jamiya> {
    try {
      const response = await httpClient.put<Jamiya>(`/jamiyas/${id}`, jamiyaData);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to update jamiya');
    } catch (error) {
      console.error('Update jamiya error:', error);
      throw error;
    }
  }

  /**
   * Delete jamiya
   */
  async deleteJamiya(id: string): Promise<void> {
    try {
      const response = await httpClient.delete(`/jamiyas/${id}`);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete jamiya');
      }
    } catch (error) {
      console.error('Delete jamiya error:', error);
      throw error;
    }
  }

  /**
   * Join jamiya
   */
  async joinJamiya(data: JoinJamiyaRequest): Promise<JamiyaMember> {
    try {
      const response = await httpClient.post<JamiyaMember>('/jamiyas/join', data);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to join jamiya');
    } catch (error) {
      console.error('Join jamiya error:', error);
      throw error;
    }
  }

  /**
   * Leave jamiya
   */
  async leaveJamiya(jamiyaId: string): Promise<void> {
    try {
      const response = await httpClient.post(`/jamiyas/${jamiyaId}/leave`);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to leave jamiya');
      }
    } catch (error) {
      console.error('Leave jamiya error:', error);
      throw error;
    }
  }

  /**
   * Get user's subscriptions
   */
  async getSubscriptions(): Promise<Subscription[]> {
    try {
      const response = await httpClient.get<Subscription[]>('/jamiyas/subscriptions');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get subscriptions');
    } catch (error) {
      console.error('Get subscriptions error:', error);
      throw error;
    }
  }

  /**
   * Get jamiya members
   */
  async getJamiyaMembers(jamiyaId: string, params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<PaginatedResponse<JamiyaMember>> {
    try {
      const response = await httpClient.get<PaginatedResponse<JamiyaMember>>(
        `/jamiyas/${jamiyaId}/members`,
        params
      );
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get jamiya members');
    } catch (error) {
      console.error('Get jamiya members error:', error);
      throw error;
    }
  }

  /**
   * Update member shares
   */
  async updateMemberShares(jamiyaId: string, memberId: string, shares: number): Promise<JamiyaMember> {
    try {
      const response = await httpClient.put<JamiyaMember>(
        `/jamiyas/${jamiyaId}/members/${memberId}`,
        { shares }
      );
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to update member shares');
    } catch (error) {
      console.error('Update member shares error:', error);
      throw error;
    }
  }

  /**
   * Remove member from jamiya
   */
  async removeMember(jamiyaId: string, memberId: string): Promise<void> {
    try {
      const response = await httpClient.delete(`/jamiyas/${jamiyaId}/members/${memberId}`);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to remove member');
      }
    } catch (error) {
      console.error('Remove member error:', error);
      throw error;
    }
  }

  /**
   * Search public jamiyas
   */
  async searchPublicJamiyas(query: string, filters?: {
    type?: string;
    minSharePrice?: number;
    maxSharePrice?: number;
    duration?: number;
  }): Promise<Jamiya[]> {
    try {
      const params = { search: query, ...filters };
      const response = await httpClient.get<Jamiya[]>('/jamiyas/public', params);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to search jamiyas');
    } catch (error) {
      console.error('Search jamiyas error:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const jamiyaService = new JamiyaService();
export default jamiyaService;
