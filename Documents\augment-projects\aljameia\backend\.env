# Environment Configuration
NODE_ENV=development
PORT=3001

# Database Configuration
DATABASE_URL=postgresql://aljameia_user:aljameia_password@localhost:5432/aljameia_db
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=aljameia_user
DB_PASSWORD=aljameia_password
DB_DATABASE=aljameia_db
DB_SYNCHRONIZE=true
DB_LOGGING=true
DB_MIGRATIONS_RUN=false

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production
SESSION_MAX_AGE=86400000

# API Configuration
API_VERSION=v1
API_BODY_LIMIT=10mb
API_TIMEOUT=30000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# AWS S3 Configuration (Optional)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=aljameia-documents

# Email Configuration (Nodemailer)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Al-Jameia System

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=

# Payment Gateway Configuration
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
STRIPE_PUBLISHABLE_KEY=

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_MAX_SIZE=20m
LOG_FILE_MAX_FILES=14d

# Cache Configuration
CACHE_TTL_DEFAULT=3600
CACHE_TTL_SHORT=300
CACHE_TTL_LONG=86400

# Security Configuration
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true

# Two-Factor Authentication
TWO_FACTOR_ISSUER=Al-Jameia
TWO_FACTOR_WINDOW=2

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Monitoring Configuration
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# Development Configuration
DEV_SEED_DATA=true
DEV_RESET_DB=false
DEV_MOCK_PAYMENTS=true
DEV_MOCK_SMS=true
DEV_MOCK_EMAIL=true

# Production Configuration (Override in production)
# NODE_ENV=production
# DB_SYNCHRONIZE=false
# DB_LOGGING=false
# DB_MIGRATIONS_RUN=true
# CORS_ORIGIN=https://yourdomain.com
# JWT_SECRET=your-production-jwt-secret
# SESSION_SECRET=your-production-session-secret
