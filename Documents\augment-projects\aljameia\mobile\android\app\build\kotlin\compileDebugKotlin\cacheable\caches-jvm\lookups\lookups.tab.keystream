  SuppressLint android.annotation  Activity android.app  Application android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  
AlJameiaTheme android.app.Activity  	Alignment android.app.Activity  Arrangement android.app.Activity  
AuthScreen android.app.Activity  Bundle android.app.Activity  Button android.app.Activity  Column android.app.Activity  DashboardScreen android.app.Activity  DocumentViewerScreen android.app.Activity  	Exception android.app.Activity  Intent android.app.Activity  Log android.app.Activity  MainActivity android.app.Activity  
MainScreen android.app.Activity  
MainViewModel android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  
ProfileScreen android.app.Activity  SettingsScreen android.app.Activity  Spacer android.app.Activity  SplashScreen android.app.Activity  Surface android.app.Activity  Text android.app.Activity  	TextAlign android.app.Activity  ViewModelProvider android.app.Activity  delay android.app.Activity  dp android.app.Activity  fillMaxSize android.app.Activity  finish android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  height android.app.Activity  invoke android.app.Activity  java android.app.Activity  launch android.app.Activity  lifecycleScope android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  	setWindow android.app.Activity  
startActivity android.app.Activity  window android.app.Activity  Build android.app.Application  	Exception android.app.Application  Log android.app.Application  NotificationChannel android.app.Application  NotificationManager android.app.Application  apply android.app.Application  createNotificationChannels android.app.Application  getSystemService android.app.Application  java android.app.Application  listOf android.app.Application  onCreate android.app.Application  apply android.app.NotificationChannel  description android.app.NotificationChannel  getAPPLY android.app.NotificationChannel  getApply android.app.NotificationChannel  getDESCRIPTION android.app.NotificationChannel  getDescription android.app.NotificationChannel  setDescription android.app.NotificationChannel  IMPORTANCE_DEFAULT android.app.NotificationManager  IMPORTANCE_HIGH android.app.NotificationManager  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannels android.app.NotificationManager  notify android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  Boolean android.app.Service  Intent android.app.Service  
JobParameters android.app.Service  MainActivity android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  
PendingIntent android.app.Service  R android.app.Service  
RemoteMessage android.app.Service  String android.app.Service  System android.app.Service  apply android.app.Service  getSystemService android.app.Service  java android.app.Service  let android.app.Service  onMessageReceived android.app.Service  
onNewToken android.app.Service  showNotification android.app.Service  
JobParameters android.app.job  
JobService android.app.job  Boolean android.app.job.JobService  
JobParameters android.app.job.JobService  BroadcastReceiver android.content  Context android.content  Intent android.content  ConnectivityManager !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  
AlJameiaTheme android.content.Context  	Alignment android.content.Context  Arrangement android.content.Context  
AuthScreen android.content.Context  Boolean android.content.Context  Build android.content.Context  Bundle android.content.Context  Button android.content.Context  Column android.content.Context  DashboardScreen android.content.Context  DocumentViewerScreen android.content.Context  	Exception android.content.Context  Intent android.content.Context  
JobParameters android.content.Context  Log android.content.Context  MainActivity android.content.Context  
MainScreen android.content.Context  
MainViewModel android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  
PendingIntent android.content.Context  
ProfileScreen android.content.Context  R android.content.Context  
RemoteMessage android.content.Context  SettingsScreen android.content.Context  Spacer android.content.Context  SplashScreen android.content.Context  String android.content.Context  Surface android.content.Context  System android.content.Context  Text android.content.Context  	TextAlign android.content.Context  ViewModelProvider android.content.Context  applicationContext android.content.Context  apply android.content.Context  createNotificationChannels android.content.Context  delay android.content.Context  dp android.content.Context  fillMaxSize android.content.Context  finish android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  getSystemService android.content.Context  height android.content.Context  invoke android.content.Context  java android.content.Context  launch android.content.Context  let android.content.Context  lifecycleScope android.content.Context  listOf android.content.Context  onCreate android.content.Context  onMessageReceived android.content.Context  
onNewToken android.content.Context  padding android.content.Context  setApplicationContext android.content.Context  
setContent android.content.Context  showNotification android.content.Context  
startActivity android.content.Context  
AlJameiaTheme android.content.ContextWrapper  	Alignment android.content.ContextWrapper  Arrangement android.content.ContextWrapper  
AuthScreen android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  Button android.content.ContextWrapper  Column android.content.ContextWrapper  DashboardScreen android.content.ContextWrapper  DocumentViewerScreen android.content.ContextWrapper  	Exception android.content.ContextWrapper  Intent android.content.ContextWrapper  
JobParameters android.content.ContextWrapper  Log android.content.ContextWrapper  MainActivity android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  
MainViewModel android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  
ProfileScreen android.content.ContextWrapper  R android.content.ContextWrapper  
RemoteMessage android.content.ContextWrapper  SettingsScreen android.content.ContextWrapper  Spacer android.content.ContextWrapper  SplashScreen android.content.ContextWrapper  String android.content.ContextWrapper  Surface android.content.ContextWrapper  System android.content.ContextWrapper  Text android.content.ContextWrapper  	TextAlign android.content.ContextWrapper  ViewModelProvider android.content.ContextWrapper  apply android.content.ContextWrapper  createNotificationChannels android.content.ContextWrapper  delay android.content.ContextWrapper  dp android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  finish android.content.ContextWrapper  getSystemService android.content.ContextWrapper  height android.content.ContextWrapper  invoke android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  listOf android.content.ContextWrapper  onCreate android.content.ContextWrapper  onMessageReceived android.content.ContextWrapper  
onNewToken android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  showNotification android.content.ContextWrapper  
startActivity android.content.ContextWrapper  ACTION_BOOT_COMPLETED android.content.Intent  ACTION_MY_PACKAGE_REPLACED android.content.Intent  ACTION_PACKAGE_REPLACED android.content.Intent  FLAG_ACTIVITY_CLEAR_TASK android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Intent android.content.Intent  action android.content.Intent  apply android.content.Intent  flags android.content.Intent  	getACTION android.content.Intent  getAPPLY android.content.Intent  	getAction android.content.Intent  getApply android.content.Intent  getFLAGS android.content.Intent  getFlags android.content.Intent  	setAction android.content.Intent  setFlags android.content.Intent  ConnectivityManager android.net  CONNECTIVITY_ACTION android.net.ConnectivityManager  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  Log android.util  d android.util.Log  e android.util.Log  View android.view  Window android.view  
AlJameiaTheme  android.view.ContextThemeWrapper  	Alignment  android.view.ContextThemeWrapper  Arrangement  android.view.ContextThemeWrapper  
AuthScreen  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  Column  android.view.ContextThemeWrapper  DashboardScreen  android.view.ContextThemeWrapper  DocumentViewerScreen  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  
MainViewModel  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  
ProfileScreen  android.view.ContextThemeWrapper  SettingsScreen  android.view.ContextThemeWrapper  Spacer  android.view.ContextThemeWrapper  SplashScreen  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  Text  android.view.ContextThemeWrapper  	TextAlign  android.view.ContextThemeWrapper  ViewModelProvider  android.view.ContextThemeWrapper  delay  android.view.ContextThemeWrapper  dp  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  height  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  context android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  getISInEditMode android.view.View  getIsInEditMode android.view.View  isInEditMode android.view.View  
setContext android.view.View  
setInEditMode android.view.View  getSTATUSBarColor android.view.Window  getStatusBarColor android.view.Window  setStatusBarColor android.view.Window  statusBarColor android.view.Window  ComponentActivity androidx.activity  
AlJameiaTheme #androidx.activity.ComponentActivity  	Alignment #androidx.activity.ComponentActivity  Arrangement #androidx.activity.ComponentActivity  
AuthScreen #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  Column #androidx.activity.ComponentActivity  DashboardScreen #androidx.activity.ComponentActivity  DocumentViewerScreen #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  
MainViewModel #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  
ProfileScreen #androidx.activity.ComponentActivity  SettingsScreen #androidx.activity.ComponentActivity  Spacer #androidx.activity.ComponentActivity  SplashScreen #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  Text #androidx.activity.ComponentActivity  	TextAlign #androidx.activity.ComponentActivity  ViewModelProvider #androidx.activity.ComponentActivity  delay #androidx.activity.ComponentActivity  dp #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  height #androidx.activity.ComponentActivity  invoke #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  AccountBalance "androidx.compose.foundation.layout  
AlJameiaTheme "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
Assessment "androidx.compose.foundation.layout  
AuthScreen "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CardMembership "androidx.compose.foundation.layout  ChevronRight "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  ClickableSettingItem "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContactSupport "androidx.compose.foundation.layout  DarkMode "androidx.compose.foundation.layout  DashboardScreen "androidx.compose.foundation.layout  Description "androidx.compose.foundation.layout  DocumentViewerScreen "androidx.compose.foundation.layout  ElevatedButton "androidx.compose.foundation.layout  Event "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  
ExpandLess "androidx.compose.foundation.layout  
ExpandMore "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  Favorite "androidx.compose.foundation.layout  FinancialSummaryCard "androidx.compose.foundation.layout  Fingerprint "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  History "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  Intent "androidx.compose.foundation.layout  Language "androidx.compose.foundation.layout  LanguageSettingItem "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  Lock "androidx.compose.foundation.layout  Log "androidx.compose.foundation.layout  Logout "androidx.compose.foundation.layout  MainActivity "androidx.compose.foundation.layout  
MainScreen "androidx.compose.foundation.layout  
MainViewModel "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  
Notifications "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  Payment "androidx.compose.foundation.layout  Person "androidx.compose.foundation.layout  
PrivacyTip "androidx.compose.foundation.layout  
ProfileScreen "androidx.compose.foundation.layout  QuickActionCard "androidx.compose.foundation.layout  R "androidx.compose.foundation.layout  RadioButton "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Schedule "androidx.compose.foundation.layout  SettingsScreen "androidx.compose.foundation.layout  SettingsSectionHeader "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  SplashScreen "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  	SwapHoriz "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  SwitchSettingItem "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  TransactionItem "androidx.compose.foundation.layout  
TrendingUp "androidx.compose.foundation.layout  ViewModelProvider "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  delay "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  finish "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getAboutSettings "androidx.compose.foundation.layout  getAccountSettings "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  invoke "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  lifecycleScope "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  
setContent "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  
startActivity "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  ChevronRight .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ElevatedButton .androidx.compose.foundation.layout.ColumnScope  
ExpandLess .androidx.compose.foundation.layout.ColumnScope  
ExpandMore .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Language .androidx.compose.foundation.layout.ColumnScope  Log .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFILLMaxSize .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxSize .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getISNotBlank .androidx.compose.foundation.layout.ColumnScope  
getIsNotBlank .androidx.compose.foundation.layout.ColumnScope  getLET .androidx.compose.foundation.layout.ColumnScope  getLet .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getWIDTH .androidx.compose.foundation.layout.ColumnScope  getWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  ChevronRight +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  ElevatedButton +androidx.compose.foundation.layout.RowScope  
ExpandLess +androidx.compose.foundation.layout.RowScope  
ExpandMore +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  Language +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  getLET +androidx.compose.foundation.layout.RowScope  getLet +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Arrangement .androidx.compose.foundation.lazy.LazyItemScope  ClickableSettingItem .androidx.compose.foundation.lazy.LazyItemScope  DarkMode .androidx.compose.foundation.lazy.LazyItemScope  FinancialSummaryCard .androidx.compose.foundation.lazy.LazyItemScope  Fingerprint .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  LanguageSettingItem .androidx.compose.foundation.lazy.LazyItemScope  LazyRow .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  
Notifications .androidx.compose.foundation.lazy.LazyItemScope  QuickActionCard .androidx.compose.foundation.lazy.LazyItemScope  SettingsSectionHeader .androidx.compose.foundation.lazy.LazyItemScope  SwitchSettingItem .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  TransactionItem .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  items .androidx.compose.foundation.lazy.LazyItemScope  Arrangement .androidx.compose.foundation.lazy.LazyListScope  ClickableSettingItem .androidx.compose.foundation.lazy.LazyListScope  DarkMode .androidx.compose.foundation.lazy.LazyListScope  FinancialSummaryCard .androidx.compose.foundation.lazy.LazyListScope  Fingerprint .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  LanguageSettingItem .androidx.compose.foundation.lazy.LazyListScope  LazyRow .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  
Notifications .androidx.compose.foundation.lazy.LazyListScope  QuickActionCard .androidx.compose.foundation.lazy.LazyListScope  SettingsSectionHeader .androidx.compose.foundation.lazy.LazyListScope  SwitchSettingItem .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  TransactionItem .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  getAboutSettings .androidx.compose.foundation.lazy.LazyListScope  getAccountSettings .androidx.compose.foundation.lazy.LazyListScope  getGETAboutSettings .androidx.compose.foundation.lazy.LazyListScope  getGETAccountSettings .androidx.compose.foundation.lazy.LazyListScope  getGetAboutSettings .androidx.compose.foundation.lazy.LazyListScope  getGetAccountSettings .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  AccountBalance ,androidx.compose.material.icons.Icons.Filled  
Assessment ,androidx.compose.material.icons.Icons.Filled  CardMembership ,androidx.compose.material.icons.Icons.Filled  ChevronRight ,androidx.compose.material.icons.Icons.Filled  ContactSupport ,androidx.compose.material.icons.Icons.Filled  DarkMode ,androidx.compose.material.icons.Icons.Filled  Description ,androidx.compose.material.icons.Icons.Filled  Event ,androidx.compose.material.icons.Icons.Filled  
ExpandLess ,androidx.compose.material.icons.Icons.Filled  
ExpandMore ,androidx.compose.material.icons.Icons.Filled  Favorite ,androidx.compose.material.icons.Icons.Filled  Fingerprint ,androidx.compose.material.icons.Icons.Filled  History ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  Language ,androidx.compose.material.icons.Icons.Filled  Lock ,androidx.compose.material.icons.Icons.Filled  Logout ,androidx.compose.material.icons.Icons.Filled  
Notifications ,androidx.compose.material.icons.Icons.Filled  Payment ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  
PrivacyTip ,androidx.compose.material.icons.Icons.Filled  Schedule ,androidx.compose.material.icons.Icons.Filled  	SwapHoriz ,androidx.compose.material.icons.Icons.Filled  
TrendingUp ,androidx.compose.material.icons.Icons.Filled  AccountBalance &androidx.compose.material.icons.filled  
AlJameiaTheme &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  
Assessment &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  CardMembership &androidx.compose.material.icons.filled  ChevronRight &androidx.compose.material.icons.filled  ClickableSettingItem &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  ContactSupport &androidx.compose.material.icons.filled  DarkMode &androidx.compose.material.icons.filled  DashboardScreen &androidx.compose.material.icons.filled  Description &androidx.compose.material.icons.filled  Event &androidx.compose.material.icons.filled  
ExpandLess &androidx.compose.material.icons.filled  
ExpandMore &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  FinancialSummaryCard &androidx.compose.material.icons.filled  Fingerprint &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  History &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  Language &androidx.compose.material.icons.filled  LanguageSettingItem &androidx.compose.material.icons.filled  LazyRow &androidx.compose.material.icons.filled  Lock &androidx.compose.material.icons.filled  Logout &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  Payment &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  
PrivacyTip &androidx.compose.material.icons.filled  QuickActionCard &androidx.compose.material.icons.filled  RadioButton &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Scaffold &androidx.compose.material.icons.filled  Schedule &androidx.compose.material.icons.filled  SettingsScreen &androidx.compose.material.icons.filled  SettingsSectionHeader &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  	SwapHoriz &androidx.compose.material.icons.filled  Switch &androidx.compose.material.icons.filled  SwitchSettingItem &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  	TextAlign &androidx.compose.material.icons.filled  	TopAppBar &androidx.compose.material.icons.filled  TopAppBarDefaults &androidx.compose.material.icons.filled  TransactionItem &androidx.compose.material.icons.filled  
TrendingUp &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  forEach &androidx.compose.material.icons.filled  getAboutSettings &androidx.compose.material.icons.filled  getAccountSettings &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  items &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  
setContent &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  AccountBalance androidx.compose.material3  
AlJameiaTheme androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  
Assessment androidx.compose.material3  
AuthScreen androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CardMembership androidx.compose.material3  ChevronRight androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ClickableSettingItem androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  ContactSupport androidx.compose.material3  DarkMode androidx.compose.material3  DashboardScreen androidx.compose.material3  Description androidx.compose.material3  DocumentViewerScreen androidx.compose.material3  ElevatedButton androidx.compose.material3  Event androidx.compose.material3  	Exception androidx.compose.material3  
ExpandLess androidx.compose.material3  
ExpandMore androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  Favorite androidx.compose.material3  FinancialSummaryCard androidx.compose.material3  Fingerprint androidx.compose.material3  
FontWeight androidx.compose.material3  History androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Info androidx.compose.material3  Intent androidx.compose.material3  Language androidx.compose.material3  LanguageSettingItem androidx.compose.material3  LazyRow androidx.compose.material3  Lock androidx.compose.material3  Log androidx.compose.material3  Logout androidx.compose.material3  MainActivity androidx.compose.material3  
MainScreen androidx.compose.material3  
MainViewModel androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  
Notifications androidx.compose.material3  OutlinedTextField androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  Payment androidx.compose.material3  Person androidx.compose.material3  
PrivacyTip androidx.compose.material3  
ProfileScreen androidx.compose.material3  QuickActionCard androidx.compose.material3  R androidx.compose.material3  RadioButton androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Schedule androidx.compose.material3  SettingsScreen androidx.compose.material3  SettingsSectionHeader androidx.compose.material3  Spacer androidx.compose.material3  SplashScreen androidx.compose.material3  Surface androidx.compose.material3  	SwapHoriz androidx.compose.material3  Switch androidx.compose.material3  SwitchSettingItem androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  TransactionItem androidx.compose.material3  
TrendingUp androidx.compose.material3  
Typography androidx.compose.material3  ViewModelProvider androidx.compose.material3  androidx androidx.compose.material3  darkColorScheme androidx.compose.material3  delay androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  finish androidx.compose.material3  forEach androidx.compose.material3  getAboutSettings androidx.compose.material3  getAccountSettings androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  invoke androidx.compose.material3  
isNotBlank androidx.compose.material3  items androidx.compose.material3  java androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lifecycleScope androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  
setContent androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  
startActivity androidx.compose.material3  width androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  AccountBalance androidx.compose.runtime  
AlJameiaTheme androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  
Assessment androidx.compose.runtime  
AuthScreen androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CardMembership androidx.compose.runtime  ChevronRight androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  ClickableSettingItem androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  ContactSupport androidx.compose.runtime  DarkMode androidx.compose.runtime  DashboardScreen androidx.compose.runtime  Description androidx.compose.runtime  DocumentViewerScreen androidx.compose.runtime  ElevatedButton androidx.compose.runtime  Event androidx.compose.runtime  	Exception androidx.compose.runtime  
ExpandLess androidx.compose.runtime  
ExpandMore androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  Favorite androidx.compose.runtime  FinancialSummaryCard androidx.compose.runtime  Fingerprint androidx.compose.runtime  
FontWeight androidx.compose.runtime  History androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Info androidx.compose.runtime  Intent androidx.compose.runtime  Language androidx.compose.runtime  LanguageSettingItem androidx.compose.runtime  LazyRow androidx.compose.runtime  Lock androidx.compose.runtime  Log androidx.compose.runtime  Logout androidx.compose.runtime  MainActivity androidx.compose.runtime  
MainScreen androidx.compose.runtime  
MainViewModel androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  
Notifications androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  Payment androidx.compose.runtime  Person androidx.compose.runtime  
PrivacyTip androidx.compose.runtime  
ProfileScreen androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  QuickActionCard androidx.compose.runtime  R androidx.compose.runtime  RadioButton androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Schedule androidx.compose.runtime  SettingsScreen androidx.compose.runtime  SettingsSectionHeader androidx.compose.runtime  
SideEffect androidx.compose.runtime  Spacer androidx.compose.runtime  SplashScreen androidx.compose.runtime  State androidx.compose.runtime  Surface androidx.compose.runtime  	SwapHoriz androidx.compose.runtime  Switch androidx.compose.runtime  SwitchSettingItem androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  TransactionItem androidx.compose.runtime  
TrendingUp androidx.compose.runtime  ViewModelProvider androidx.compose.runtime  androidx androidx.compose.runtime  collectAsState androidx.compose.runtime  delay androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  finish androidx.compose.runtime  forEach androidx.compose.runtime  getAboutSettings androidx.compose.runtime  getAccountSettings androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  invoke androidx.compose.runtime  
isNotBlank androidx.compose.runtime  items androidx.compose.runtime  java androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  lifecycleScope androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  
setContent androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  
startActivity androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  	getHEIGHT androidx.compose.ui.Modifier  	getHeight androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  copy "androidx.compose.ui.graphics.Color  	getTOArgb "androidx.compose.ui.graphics.Color  	getToArgb "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  LocalLayoutDirection androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  PasswordVisualTransformation androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  LayoutDirection androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  NotificationCompat androidx.core.app  
AlJameiaTheme #androidx.core.app.ComponentActivity  	Alignment #androidx.core.app.ComponentActivity  Arrangement #androidx.core.app.ComponentActivity  
AuthScreen #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  Column #androidx.core.app.ComponentActivity  DashboardScreen #androidx.core.app.ComponentActivity  DocumentViewerScreen #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  
MainViewModel #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  
ProfileScreen #androidx.core.app.ComponentActivity  SettingsScreen #androidx.core.app.ComponentActivity  Spacer #androidx.core.app.ComponentActivity  SplashScreen #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  Text #androidx.core.app.ComponentActivity  	TextAlign #androidx.core.app.ComponentActivity  ViewModelProvider #androidx.core.app.ComponentActivity  delay #androidx.core.app.ComponentActivity  dp #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  height #androidx.core.app.ComponentActivity  invoke #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  
setAutoCancel ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  WindowCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  getISAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getIsAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  setAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  LifecycleCoroutineScope androidx.lifecycle  	ViewModel androidx.lifecycle  ViewModelProvider androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  	getLAUNCH *androidx.lifecycle.LifecycleCoroutineScope  	getLaunch *androidx.lifecycle.LifecycleCoroutineScope  launch *androidx.lifecycle.LifecycleCoroutineScope  Boolean androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  MainUiState androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  
_isLoading androidx.lifecycle.ViewModel  _uiState androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  delay androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  loadData androidx.lifecycle.ViewModel  retry androidx.lifecycle.ViewModel  showMessage androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  get $androidx.lifecycle.ViewModelProvider  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  
ForeignKey 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  java 
androidx.room  CASCADE androidx.room.ForeignKey  CASCADE "androidx.room.ForeignKey.Companion  invoke "androidx.room.ForeignKey.Companion  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  AlJameiaDatabase androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  TransactionDao androidx.room.RoomDatabase  UserDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  AlJameiaDatabase $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  getSYNCHRONIZED $androidx.room.RoomDatabase.Companion  getSynchronized $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  AlJameiaApplication com.aljameia.app  Build com.aljameia.app  	Exception com.aljameia.app  Log com.aljameia.app  NotificationChannel com.aljameia.app  NotificationManager com.aljameia.app  R com.aljameia.app  apply com.aljameia.app  java com.aljameia.app  listOf com.aljameia.app  Build $com.aljameia.app.AlJameiaApplication  	Exception $com.aljameia.app.AlJameiaApplication  Log $com.aljameia.app.AlJameiaApplication  NotificationChannel $com.aljameia.app.AlJameiaApplication  NotificationManager $com.aljameia.app.AlJameiaApplication  apply $com.aljameia.app.AlJameiaApplication  createNotificationChannels $com.aljameia.app.AlJameiaApplication  getAPPLY $com.aljameia.app.AlJameiaApplication  getApply $com.aljameia.app.AlJameiaApplication  	getLISTOf $com.aljameia.app.AlJameiaApplication  	getListOf $com.aljameia.app.AlJameiaApplication  getSystemService $com.aljameia.app.AlJameiaApplication  java $com.aljameia.app.AlJameiaApplication  listOf $com.aljameia.app.AlJameiaApplication  drawable com.aljameia.app.R  string com.aljameia.app.R  ic_notification com.aljameia.app.R.drawable  app_name com.aljameia.app.R.string  Boolean com.aljameia.app.data.dao  Dao com.aljameia.app.data.dao  Delete com.aljameia.app.data.dao  Insert com.aljameia.app.data.dao  List com.aljameia.app.data.dao  OnConflictStrategy com.aljameia.app.data.dao  Query com.aljameia.app.data.dao  String com.aljameia.app.data.dao  TransactionDao com.aljameia.app.data.dao  Update com.aljameia.app.data.dao  UserDao com.aljameia.app.data.dao  java com.aljameia.app.data.dao  
BigDecimal (com.aljameia.app.data.dao.TransactionDao  Date (com.aljameia.app.data.dao.TransactionDao  Delete (com.aljameia.app.data.dao.TransactionDao  Flow (com.aljameia.app.data.dao.TransactionDao  Insert (com.aljameia.app.data.dao.TransactionDao  List (com.aljameia.app.data.dao.TransactionDao  OnConflictStrategy (com.aljameia.app.data.dao.TransactionDao  Query (com.aljameia.app.data.dao.TransactionDao  String (com.aljameia.app.data.dao.TransactionDao  Transaction (com.aljameia.app.data.dao.TransactionDao  TransactionStatus (com.aljameia.app.data.dao.TransactionDao  TransactionType (com.aljameia.app.data.dao.TransactionDao  Update (com.aljameia.app.data.dao.TransactionDao  Boolean !com.aljameia.app.data.dao.UserDao  Delete !com.aljameia.app.data.dao.UserDao  Flow !com.aljameia.app.data.dao.UserDao  Insert !com.aljameia.app.data.dao.UserDao  List !com.aljameia.app.data.dao.UserDao  OnConflictStrategy !com.aljameia.app.data.dao.UserDao  Query !com.aljameia.app.data.dao.UserDao  String !com.aljameia.app.data.dao.UserDao  Update !com.aljameia.app.data.dao.UserDao  User !com.aljameia.app.data.dao.UserDao  java !com.aljameia.app.data.dao.UserDao  AlJameiaDatabase com.aljameia.app.data.database  
BigDecimal com.aljameia.app.data.database  
Converters com.aljameia.app.data.database  Date com.aljameia.app.data.database  Document com.aljameia.app.data.database  Event com.aljameia.app.data.database  Gson com.aljameia.app.data.database  List com.aljameia.app.data.database  Long com.aljameia.app.data.database  Room com.aljameia.app.data.database  String com.aljameia.app.data.database  Transaction com.aljameia.app.data.database  User com.aljameia.app.data.database  Volatile com.aljameia.app.data.database  	emptyList com.aljameia.app.data.database  
isNullOrEmpty com.aljameia.app.data.database  java com.aljameia.app.data.database  let com.aljameia.app.data.database  synchronized com.aljameia.app.data.database  AlJameiaDatabase /com.aljameia.app.data.database.AlJameiaDatabase  	Companion /com.aljameia.app.data.database.AlJameiaDatabase  Context /com.aljameia.app.data.database.AlJameiaDatabase  Room /com.aljameia.app.data.database.AlJameiaDatabase  TransactionDao /com.aljameia.app.data.database.AlJameiaDatabase  UserDao /com.aljameia.app.data.database.AlJameiaDatabase  Volatile /com.aljameia.app.data.database.AlJameiaDatabase  java /com.aljameia.app.data.database.AlJameiaDatabase  synchronized /com.aljameia.app.data.database.AlJameiaDatabase  AlJameiaDatabase 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  Context 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  INSTANCE 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  Room 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  TransactionDao 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  UserDao 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  Volatile 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  getSYNCHRONIZED 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  getSynchronized 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  java 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  synchronized 9com.aljameia.app.data.database.AlJameiaDatabase.Companion  
BigDecimal )com.aljameia.app.data.database.Converters  Date )com.aljameia.app.data.database.Converters  Gson )com.aljameia.app.data.database.Converters  List )com.aljameia.app.data.database.Converters  Long )com.aljameia.app.data.database.Converters  String )com.aljameia.app.data.database.Converters  
TypeConverter )com.aljameia.app.data.database.Converters  	TypeToken )com.aljameia.app.data.database.Converters  	emptyList )com.aljameia.app.data.database.Converters  getEMPTYList )com.aljameia.app.data.database.Converters  getEmptyList )com.aljameia.app.data.database.Converters  getISNullOrEmpty )com.aljameia.app.data.database.Converters  getIsNullOrEmpty )com.aljameia.app.data.database.Converters  getLET )com.aljameia.app.data.database.Converters  getLet )com.aljameia.app.data.database.Converters  
isNullOrEmpty )com.aljameia.app.data.database.Converters  let )com.aljameia.app.data.database.Converters  getTYPE Icom.aljameia.app.data.database.Converters.toStringList.<no name provided>  getType Icom.aljameia.app.data.database.Converters.toStringList.<no name provided>  setType Icom.aljameia.app.data.database.Converters.toStringList.<no name provided>  Boolean com.aljameia.app.data.entities  Document com.aljameia.app.data.entities  DocumentType com.aljameia.app.data.entities  Event com.aljameia.app.data.entities  	EventType com.aljameia.app.data.entities  Int com.aljameia.app.data.entities  List com.aljameia.app.data.entities  Long com.aljameia.app.data.entities  
PaymentMethod com.aljameia.app.data.entities  String com.aljameia.app.data.entities  Transaction com.aljameia.app.data.entities  TransactionStatus com.aljameia.app.data.entities  TransactionType com.aljameia.app.data.entities  User com.aljameia.app.data.entities  	emptyList com.aljameia.app.data.entities  Boolean 'com.aljameia.app.data.entities.Document  Date 'com.aljameia.app.data.entities.Document  DocumentType 'com.aljameia.app.data.entities.Document  Int 'com.aljameia.app.data.entities.Document  List 'com.aljameia.app.data.entities.Document  Long 'com.aljameia.app.data.entities.Document  
PrimaryKey 'com.aljameia.app.data.entities.Document  String 'com.aljameia.app.data.entities.Document  	emptyList 'com.aljameia.app.data.entities.Document  
BigDecimal $com.aljameia.app.data.entities.Event  Boolean $com.aljameia.app.data.entities.Event  Date $com.aljameia.app.data.entities.Event  	EventType $com.aljameia.app.data.entities.Event  Int $com.aljameia.app.data.entities.Event  List $com.aljameia.app.data.entities.Event  
PrimaryKey $com.aljameia.app.data.entities.Event  String $com.aljameia.app.data.entities.Event  	emptyList $com.aljameia.app.data.entities.Event  
BigDecimal *com.aljameia.app.data.entities.Transaction  Date *com.aljameia.app.data.entities.Transaction  
PaymentMethod *com.aljameia.app.data.entities.Transaction  
PrimaryKey *com.aljameia.app.data.entities.Transaction  String *com.aljameia.app.data.entities.Transaction  TransactionStatus *com.aljameia.app.data.entities.Transaction  TransactionType *com.aljameia.app.data.entities.Transaction  Boolean #com.aljameia.app.data.entities.User  Date #com.aljameia.app.data.entities.User  
PrimaryKey #com.aljameia.app.data.entities.User  String #com.aljameia.app.data.entities.User  copy #com.aljameia.app.data.entities.User  email #com.aljameia.app.data.entities.User  getLET #com.aljameia.app.data.entities.User  getLet #com.aljameia.app.data.entities.User  id #com.aljameia.app.data.entities.User  isActive #com.aljameia.app.data.entities.User  let #com.aljameia.app.data.entities.User  membershipNumber #com.aljameia.app.data.entities.User  Boolean  com.aljameia.app.data.repository  Date  com.aljameia.app.data.repository  List  com.aljameia.app.data.repository  String  com.aljameia.app.data.repository  User  com.aljameia.app.data.repository  UserRepository  com.aljameia.app.data.repository  filter  com.aljameia.app.data.repository  find  com.aljameia.app.data.repository  indexOfFirst  com.aljameia.app.data.repository  kotlinx  com.aljameia.app.data.repository  let  com.aljameia.app.data.repository  
mutableListOf  com.aljameia.app.data.repository  	removeAll  com.aljameia.app.data.repository  Boolean /com.aljameia.app.data.repository.UserRepository  Date /com.aljameia.app.data.repository.UserRepository  Flow /com.aljameia.app.data.repository.UserRepository  List /com.aljameia.app.data.repository.UserRepository  String /com.aljameia.app.data.repository.UserRepository  User /com.aljameia.app.data.repository.UserRepository  filter /com.aljameia.app.data.repository.UserRepository  find /com.aljameia.app.data.repository.UserRepository  	getFILTER /com.aljameia.app.data.repository.UserRepository  getFIND /com.aljameia.app.data.repository.UserRepository  	getFilter /com.aljameia.app.data.repository.UserRepository  getFind /com.aljameia.app.data.repository.UserRepository  getINDEXOfFirst /com.aljameia.app.data.repository.UserRepository  getIndexOfFirst /com.aljameia.app.data.repository.UserRepository  
getKOTLINX /com.aljameia.app.data.repository.UserRepository  
getKotlinx /com.aljameia.app.data.repository.UserRepository  getLET /com.aljameia.app.data.repository.UserRepository  getLet /com.aljameia.app.data.repository.UserRepository  getMUTABLEListOf /com.aljameia.app.data.repository.UserRepository  getMutableListOf /com.aljameia.app.data.repository.UserRepository  getREMOVEAll /com.aljameia.app.data.repository.UserRepository  getRemoveAll /com.aljameia.app.data.repository.UserRepository  indexOfFirst /com.aljameia.app.data.repository.UserRepository  
insertUser /com.aljameia.app.data.repository.UserRepository  kotlinx /com.aljameia.app.data.repository.UserRepository  let /com.aljameia.app.data.repository.UserRepository  	mockUsers /com.aljameia.app.data.repository.UserRepository  
mutableListOf /com.aljameia.app.data.repository.UserRepository  	removeAll /com.aljameia.app.data.repository.UserRepository  
updateUser /com.aljameia.app.data.repository.UserRepository  	AppModule com.aljameia.app.di  BootReceiver com.aljameia.app.receivers  ConnectivityManager com.aljameia.app.receivers  Intent com.aljameia.app.receivers  NetworkReceiver com.aljameia.app.receivers  Context 'com.aljameia.app.receivers.BootReceiver  Intent 'com.aljameia.app.receivers.BootReceiver  ConnectivityManager *com.aljameia.app.receivers.NetworkReceiver  Context *com.aljameia.app.receivers.NetworkReceiver  Intent *com.aljameia.app.receivers.NetworkReceiver  Boolean com.aljameia.app.services  
FCMService com.aljameia.app.services  Intent com.aljameia.app.services  MainActivity com.aljameia.app.services  NotificationCompat com.aljameia.app.services  NotificationManager com.aljameia.app.services  
PendingIntent com.aljameia.app.services  R com.aljameia.app.services  String com.aljameia.app.services  SyncService com.aljameia.app.services  System com.aljameia.app.services  apply com.aljameia.app.services  java com.aljameia.app.services  let com.aljameia.app.services  Intent $com.aljameia.app.services.FCMService  MainActivity $com.aljameia.app.services.FCMService  NotificationCompat $com.aljameia.app.services.FCMService  NotificationManager $com.aljameia.app.services.FCMService  
PendingIntent $com.aljameia.app.services.FCMService  R $com.aljameia.app.services.FCMService  
RemoteMessage $com.aljameia.app.services.FCMService  String $com.aljameia.app.services.FCMService  System $com.aljameia.app.services.FCMService  apply $com.aljameia.app.services.FCMService  getAPPLY $com.aljameia.app.services.FCMService  getApply $com.aljameia.app.services.FCMService  getLET $com.aljameia.app.services.FCMService  getLet $com.aljameia.app.services.FCMService  getSystemService $com.aljameia.app.services.FCMService  java $com.aljameia.app.services.FCMService  let $com.aljameia.app.services.FCMService  showNotification $com.aljameia.app.services.FCMService  Boolean %com.aljameia.app.services.SyncService  
JobParameters %com.aljameia.app.services.SyncService  
AlJameiaTheme com.aljameia.app.ui.auth  Arrangement com.aljameia.app.ui.auth  AuthActivity com.aljameia.app.ui.auth  
AuthScreen com.aljameia.app.ui.auth  Button com.aljameia.app.ui.auth  CircularProgressIndicator com.aljameia.app.ui.auth  Column com.aljameia.app.ui.auth  
Composable com.aljameia.app.ui.auth  ExperimentalMaterial3Api com.aljameia.app.ui.auth  
MaterialTheme com.aljameia.app.ui.auth  Modifier com.aljameia.app.ui.auth  OptIn com.aljameia.app.ui.auth  OutlinedTextField com.aljameia.app.ui.auth  PasswordVisualTransformation com.aljameia.app.ui.auth  Spacer com.aljameia.app.ui.auth  Surface com.aljameia.app.ui.auth  Text com.aljameia.app.ui.auth  	TextAlign com.aljameia.app.ui.auth  
TextButton com.aljameia.app.ui.auth  fillMaxSize com.aljameia.app.ui.auth  fillMaxWidth com.aljameia.app.ui.auth  getValue com.aljameia.app.ui.auth  height com.aljameia.app.ui.auth  
isNotBlank com.aljameia.app.ui.auth  mutableStateOf com.aljameia.app.ui.auth  padding com.aljameia.app.ui.auth  provideDelegate com.aljameia.app.ui.auth  remember com.aljameia.app.ui.auth  
setContent com.aljameia.app.ui.auth  setValue com.aljameia.app.ui.auth  size com.aljameia.app.ui.auth  
AlJameiaTheme %com.aljameia.app.ui.auth.AuthActivity  
AuthScreen %com.aljameia.app.ui.auth.AuthActivity  Bundle %com.aljameia.app.ui.auth.AuthActivity  
getSETContent %com.aljameia.app.ui.auth.AuthActivity  
getSetContent %com.aljameia.app.ui.auth.AuthActivity  
setContent %com.aljameia.app.ui.auth.AuthActivity  AccountBalance com.aljameia.app.ui.dashboard  
AlJameiaTheme com.aljameia.app.ui.dashboard  	Alignment com.aljameia.app.ui.dashboard  Arrangement com.aljameia.app.ui.dashboard  
Assessment com.aljameia.app.ui.dashboard  Card com.aljameia.app.ui.dashboard  CardDefaults com.aljameia.app.ui.dashboard  CardMembership com.aljameia.app.ui.dashboard  Column com.aljameia.app.ui.dashboard  
Composable com.aljameia.app.ui.dashboard  DashboardActivity com.aljameia.app.ui.dashboard  DashboardScreen com.aljameia.app.ui.dashboard  Event com.aljameia.app.ui.dashboard  ExperimentalMaterial3Api com.aljameia.app.ui.dashboard  Favorite com.aljameia.app.ui.dashboard  FinancialSummaryCard com.aljameia.app.ui.dashboard  FinancialSummaryCardData com.aljameia.app.ui.dashboard  
FontWeight com.aljameia.app.ui.dashboard  History com.aljameia.app.ui.dashboard  Icon com.aljameia.app.ui.dashboard  LazyRow com.aljameia.app.ui.dashboard  List com.aljameia.app.ui.dashboard  
MaterialTheme com.aljameia.app.ui.dashboard  Modifier com.aljameia.app.ui.dashboard  OptIn com.aljameia.app.ui.dashboard  Payment com.aljameia.app.ui.dashboard  QuickActionCard com.aljameia.app.ui.dashboard  QuickActionData com.aljameia.app.ui.dashboard  Row com.aljameia.app.ui.dashboard  Scaffold com.aljameia.app.ui.dashboard  Schedule com.aljameia.app.ui.dashboard  Spacer com.aljameia.app.ui.dashboard  String com.aljameia.app.ui.dashboard  	SwapHoriz com.aljameia.app.ui.dashboard  Text com.aljameia.app.ui.dashboard  	TextAlign com.aljameia.app.ui.dashboard  	TopAppBar com.aljameia.app.ui.dashboard  TopAppBarDefaults com.aljameia.app.ui.dashboard  TransactionItem com.aljameia.app.ui.dashboard  TransactionItemData com.aljameia.app.ui.dashboard  
TrendingUp com.aljameia.app.ui.dashboard  androidx com.aljameia.app.ui.dashboard  fillMaxSize com.aljameia.app.ui.dashboard  fillMaxWidth com.aljameia.app.ui.dashboard  getFinancialSummaryCards com.aljameia.app.ui.dashboard  getQuickActions com.aljameia.app.ui.dashboard  getRecentTransactions com.aljameia.app.ui.dashboard  height com.aljameia.app.ui.dashboard  items com.aljameia.app.ui.dashboard  listOf com.aljameia.app.ui.dashboard  padding com.aljameia.app.ui.dashboard  
setContent com.aljameia.app.ui.dashboard  size com.aljameia.app.ui.dashboard  width com.aljameia.app.ui.dashboard  
AlJameiaTheme /com.aljameia.app.ui.dashboard.DashboardActivity  Bundle /com.aljameia.app.ui.dashboard.DashboardActivity  DashboardScreen /com.aljameia.app.ui.dashboard.DashboardActivity  
getSETContent /com.aljameia.app.ui.dashboard.DashboardActivity  
getSetContent /com.aljameia.app.ui.dashboard.DashboardActivity  
setContent /com.aljameia.app.ui.dashboard.DashboardActivity  ImageVector 6com.aljameia.app.ui.dashboard.FinancialSummaryCardData  String 6com.aljameia.app.ui.dashboard.FinancialSummaryCardData  amount 6com.aljameia.app.ui.dashboard.FinancialSummaryCardData  androidx 6com.aljameia.app.ui.dashboard.FinancialSummaryCardData  backgroundColor 6com.aljameia.app.ui.dashboard.FinancialSummaryCardData  icon 6com.aljameia.app.ui.dashboard.FinancialSummaryCardData  	iconColor 6com.aljameia.app.ui.dashboard.FinancialSummaryCardData  	textColor 6com.aljameia.app.ui.dashboard.FinancialSummaryCardData  title 6com.aljameia.app.ui.dashboard.FinancialSummaryCardData  ImageVector -com.aljameia.app.ui.dashboard.QuickActionData  String -com.aljameia.app.ui.dashboard.QuickActionData  icon -com.aljameia.app.ui.dashboard.QuickActionData  title -com.aljameia.app.ui.dashboard.QuickActionData  ImageVector 1com.aljameia.app.ui.dashboard.TransactionItemData  String 1com.aljameia.app.ui.dashboard.TransactionItemData  amount 1com.aljameia.app.ui.dashboard.TransactionItemData  amountColor 1com.aljameia.app.ui.dashboard.TransactionItemData  androidx 1com.aljameia.app.ui.dashboard.TransactionItemData  date 1com.aljameia.app.ui.dashboard.TransactionItemData  description 1com.aljameia.app.ui.dashboard.TransactionItemData  icon 1com.aljameia.app.ui.dashboard.TransactionItemData  	iconColor 1com.aljameia.app.ui.dashboard.TransactionItemData  type 1com.aljameia.app.ui.dashboard.TransactionItemData  
AlJameiaTheme com.aljameia.app.ui.documents  Arrangement com.aljameia.app.ui.documents  Column com.aljameia.app.ui.documents  
Composable com.aljameia.app.ui.documents  DocumentViewerActivity com.aljameia.app.ui.documents  DocumentViewerScreen com.aljameia.app.ui.documents  ExperimentalMaterial3Api com.aljameia.app.ui.documents  
MaterialTheme com.aljameia.app.ui.documents  Modifier com.aljameia.app.ui.documents  OptIn com.aljameia.app.ui.documents  Scaffold com.aljameia.app.ui.documents  Spacer com.aljameia.app.ui.documents  Text com.aljameia.app.ui.documents  	TopAppBar com.aljameia.app.ui.documents  fillMaxSize com.aljameia.app.ui.documents  height com.aljameia.app.ui.documents  padding com.aljameia.app.ui.documents  
setContent com.aljameia.app.ui.documents  
AlJameiaTheme 4com.aljameia.app.ui.documents.DocumentViewerActivity  Bundle 4com.aljameia.app.ui.documents.DocumentViewerActivity  DocumentViewerScreen 4com.aljameia.app.ui.documents.DocumentViewerActivity  
getSETContent 4com.aljameia.app.ui.documents.DocumentViewerActivity  
getSetContent 4com.aljameia.app.ui.documents.DocumentViewerActivity  
setContent 4com.aljameia.app.ui.documents.DocumentViewerActivity  
AlJameiaTheme com.aljameia.app.ui.main  	Alignment com.aljameia.app.ui.main  Arrangement com.aljameia.app.ui.main  Boolean com.aljameia.app.ui.main  Button com.aljameia.app.ui.main  Card com.aljameia.app.ui.main  CardDefaults com.aljameia.app.ui.main  CircularProgressIndicator com.aljameia.app.ui.main  Column com.aljameia.app.ui.main  
Composable com.aljameia.app.ui.main  ElevatedButton com.aljameia.app.ui.main  	Exception com.aljameia.app.ui.main  ExperimentalMaterial3Api com.aljameia.app.ui.main  Log com.aljameia.app.ui.main  MainActivity com.aljameia.app.ui.main  
MainScreen com.aljameia.app.ui.main  MainUiState com.aljameia.app.ui.main  
MainViewModel com.aljameia.app.ui.main  
MaterialTheme com.aljameia.app.ui.main  Modifier com.aljameia.app.ui.main  MutableStateFlow com.aljameia.app.ui.main  OptIn com.aljameia.app.ui.main  R com.aljameia.app.ui.main  Row com.aljameia.app.ui.main  Scaffold com.aljameia.app.ui.main  SimpleMainActivity com.aljameia.app.ui.main  Spacer com.aljameia.app.ui.main  String com.aljameia.app.ui.main  Surface com.aljameia.app.ui.main  Text com.aljameia.app.ui.main  	TextAlign com.aljameia.app.ui.main  	TopAppBar com.aljameia.app.ui.main  ViewModelProvider com.aljameia.app.ui.main  
_isLoading com.aljameia.app.ui.main  _uiState com.aljameia.app.ui.main  asStateFlow com.aljameia.app.ui.main  delay com.aljameia.app.ui.main  fillMaxSize com.aljameia.app.ui.main  fillMaxWidth com.aljameia.app.ui.main  getValue com.aljameia.app.ui.main  height com.aljameia.app.ui.main  invoke com.aljameia.app.ui.main  java com.aljameia.app.ui.main  launch com.aljameia.app.ui.main  let com.aljameia.app.ui.main  padding com.aljameia.app.ui.main  provideDelegate com.aljameia.app.ui.main  
setContent com.aljameia.app.ui.main  viewModelScope com.aljameia.app.ui.main  
AlJameiaTheme %com.aljameia.app.ui.main.MainActivity  Bundle %com.aljameia.app.ui.main.MainActivity  	Exception %com.aljameia.app.ui.main.MainActivity  Log %com.aljameia.app.ui.main.MainActivity  
MainScreen %com.aljameia.app.ui.main.MainActivity  
MainViewModel %com.aljameia.app.ui.main.MainActivity  ViewModelProvider %com.aljameia.app.ui.main.MainActivity  
getSETContent %com.aljameia.app.ui.main.MainActivity  
getSetContent %com.aljameia.app.ui.main.MainActivity  java %com.aljameia.app.ui.main.MainActivity  
setContent %com.aljameia.app.ui.main.MainActivity  	viewModel %com.aljameia.app.ui.main.MainActivity  Error $com.aljameia.app.ui.main.MainUiState  Loading $com.aljameia.app.ui.main.MainUiState  MainUiState $com.aljameia.app.ui.main.MainUiState  String $com.aljameia.app.ui.main.MainUiState  Success $com.aljameia.app.ui.main.MainUiState  String *com.aljameia.app.ui.main.MainUiState.Error  message *com.aljameia.app.ui.main.MainUiState.Error  String ,com.aljameia.app.ui.main.MainUiState.Success  message ,com.aljameia.app.ui.main.MainUiState.Success  Boolean &com.aljameia.app.ui.main.MainViewModel  	Exception &com.aljameia.app.ui.main.MainViewModel  MainUiState &com.aljameia.app.ui.main.MainViewModel  MutableStateFlow &com.aljameia.app.ui.main.MainViewModel  	StateFlow &com.aljameia.app.ui.main.MainViewModel  String &com.aljameia.app.ui.main.MainViewModel  
_isLoading &com.aljameia.app.ui.main.MainViewModel  _uiState &com.aljameia.app.ui.main.MainViewModel  asStateFlow &com.aljameia.app.ui.main.MainViewModel  delay &com.aljameia.app.ui.main.MainViewModel  getASStateFlow &com.aljameia.app.ui.main.MainViewModel  getAsStateFlow &com.aljameia.app.ui.main.MainViewModel  getDELAY &com.aljameia.app.ui.main.MainViewModel  getDelay &com.aljameia.app.ui.main.MainViewModel  	getLAUNCH &com.aljameia.app.ui.main.MainViewModel  	getLaunch &com.aljameia.app.ui.main.MainViewModel  getVIEWModelScope &com.aljameia.app.ui.main.MainViewModel  getViewModelScope &com.aljameia.app.ui.main.MainViewModel  launch &com.aljameia.app.ui.main.MainViewModel  loadData &com.aljameia.app.ui.main.MainViewModel  retry &com.aljameia.app.ui.main.MainViewModel  showMessage &com.aljameia.app.ui.main.MainViewModel  uiState &com.aljameia.app.ui.main.MainViewModel  viewModelScope &com.aljameia.app.ui.main.MainViewModel  	Alignment +com.aljameia.app.ui.main.SimpleMainActivity  Arrangement +com.aljameia.app.ui.main.SimpleMainActivity  Bundle +com.aljameia.app.ui.main.SimpleMainActivity  Button +com.aljameia.app.ui.main.SimpleMainActivity  Column +com.aljameia.app.ui.main.SimpleMainActivity  	Exception +com.aljameia.app.ui.main.SimpleMainActivity  Log +com.aljameia.app.ui.main.SimpleMainActivity  
MaterialTheme +com.aljameia.app.ui.main.SimpleMainActivity  Modifier +com.aljameia.app.ui.main.SimpleMainActivity  Spacer +com.aljameia.app.ui.main.SimpleMainActivity  Surface +com.aljameia.app.ui.main.SimpleMainActivity  Text +com.aljameia.app.ui.main.SimpleMainActivity  	TextAlign +com.aljameia.app.ui.main.SimpleMainActivity  dp +com.aljameia.app.ui.main.SimpleMainActivity  fillMaxSize +com.aljameia.app.ui.main.SimpleMainActivity  getFILLMaxSize +com.aljameia.app.ui.main.SimpleMainActivity  getFillMaxSize +com.aljameia.app.ui.main.SimpleMainActivity  	getHEIGHT +com.aljameia.app.ui.main.SimpleMainActivity  	getHeight +com.aljameia.app.ui.main.SimpleMainActivity  
getPADDING +com.aljameia.app.ui.main.SimpleMainActivity  
getPadding +com.aljameia.app.ui.main.SimpleMainActivity  
getSETContent +com.aljameia.app.ui.main.SimpleMainActivity  
getSetContent +com.aljameia.app.ui.main.SimpleMainActivity  height +com.aljameia.app.ui.main.SimpleMainActivity  invoke +com.aljameia.app.ui.main.SimpleMainActivity  padding +com.aljameia.app.ui.main.SimpleMainActivity  
setContent +com.aljameia.app.ui.main.SimpleMainActivity  
AlJameiaTheme com.aljameia.app.ui.profile  Arrangement com.aljameia.app.ui.profile  Column com.aljameia.app.ui.profile  
Composable com.aljameia.app.ui.profile  ExperimentalMaterial3Api com.aljameia.app.ui.profile  
MaterialTheme com.aljameia.app.ui.profile  Modifier com.aljameia.app.ui.profile  OptIn com.aljameia.app.ui.profile  ProfileActivity com.aljameia.app.ui.profile  
ProfileScreen com.aljameia.app.ui.profile  Scaffold com.aljameia.app.ui.profile  Spacer com.aljameia.app.ui.profile  Text com.aljameia.app.ui.profile  	TopAppBar com.aljameia.app.ui.profile  fillMaxSize com.aljameia.app.ui.profile  height com.aljameia.app.ui.profile  padding com.aljameia.app.ui.profile  
setContent com.aljameia.app.ui.profile  
AlJameiaTheme +com.aljameia.app.ui.profile.ProfileActivity  Bundle +com.aljameia.app.ui.profile.ProfileActivity  
ProfileScreen +com.aljameia.app.ui.profile.ProfileActivity  
getSETContent +com.aljameia.app.ui.profile.ProfileActivity  
getSetContent +com.aljameia.app.ui.profile.ProfileActivity  
setContent +com.aljameia.app.ui.profile.ProfileActivity  
AlJameiaTheme com.aljameia.app.ui.settings  	Alignment com.aljameia.app.ui.settings  Arrangement com.aljameia.app.ui.settings  Boolean com.aljameia.app.ui.settings  Card com.aljameia.app.ui.settings  ChevronRight com.aljameia.app.ui.settings  ClickableSettingItem com.aljameia.app.ui.settings  Column com.aljameia.app.ui.settings  
Composable com.aljameia.app.ui.settings  ContactSupport com.aljameia.app.ui.settings  DarkMode com.aljameia.app.ui.settings  Description com.aljameia.app.ui.settings  
ExpandLess com.aljameia.app.ui.settings  
ExpandMore com.aljameia.app.ui.settings  ExperimentalMaterial3Api com.aljameia.app.ui.settings  Fingerprint com.aljameia.app.ui.settings  
FontWeight com.aljameia.app.ui.settings  Icon com.aljameia.app.ui.settings  
IconButton com.aljameia.app.ui.settings  Icons com.aljameia.app.ui.settings  Info com.aljameia.app.ui.settings  Language com.aljameia.app.ui.settings  LanguageSettingItem com.aljameia.app.ui.settings  List com.aljameia.app.ui.settings  Lock com.aljameia.app.ui.settings  Logout com.aljameia.app.ui.settings  
MaterialTheme com.aljameia.app.ui.settings  Modifier com.aljameia.app.ui.settings  
Notifications com.aljameia.app.ui.settings  OptIn com.aljameia.app.ui.settings  Person com.aljameia.app.ui.settings  
PrivacyTip com.aljameia.app.ui.settings  RadioButton com.aljameia.app.ui.settings  Row com.aljameia.app.ui.settings  Scaffold com.aljameia.app.ui.settings  SettingItem com.aljameia.app.ui.settings  SettingsActivity com.aljameia.app.ui.settings  SettingsScreen com.aljameia.app.ui.settings  SettingsSectionHeader com.aljameia.app.ui.settings  Spacer com.aljameia.app.ui.settings  String com.aljameia.app.ui.settings  Switch com.aljameia.app.ui.settings  SwitchSettingItem com.aljameia.app.ui.settings  Text com.aljameia.app.ui.settings  	TopAppBar com.aljameia.app.ui.settings  TopAppBarDefaults com.aljameia.app.ui.settings  Unit com.aljameia.app.ui.settings  fillMaxSize com.aljameia.app.ui.settings  fillMaxWidth com.aljameia.app.ui.settings  forEach com.aljameia.app.ui.settings  getAboutSettings com.aljameia.app.ui.settings  getAccountSettings com.aljameia.app.ui.settings  getValue com.aljameia.app.ui.settings  height com.aljameia.app.ui.settings  items com.aljameia.app.ui.settings  let com.aljameia.app.ui.settings  listOf com.aljameia.app.ui.settings  mutableStateOf com.aljameia.app.ui.settings  padding com.aljameia.app.ui.settings  provideDelegate com.aljameia.app.ui.settings  remember com.aljameia.app.ui.settings  
setContent com.aljameia.app.ui.settings  setValue com.aljameia.app.ui.settings  size com.aljameia.app.ui.settings  width com.aljameia.app.ui.settings  ImageVector (com.aljameia.app.ui.settings.SettingItem  String (com.aljameia.app.ui.settings.SettingItem  icon (com.aljameia.app.ui.settings.SettingItem  subtitle (com.aljameia.app.ui.settings.SettingItem  title (com.aljameia.app.ui.settings.SettingItem  
AlJameiaTheme -com.aljameia.app.ui.settings.SettingsActivity  Bundle -com.aljameia.app.ui.settings.SettingsActivity  SettingsScreen -com.aljameia.app.ui.settings.SettingsActivity  
getSETContent -com.aljameia.app.ui.settings.SettingsActivity  
getSetContent -com.aljameia.app.ui.settings.SettingsActivity  
setContent -com.aljameia.app.ui.settings.SettingsActivity  
AlJameiaTheme com.aljameia.app.ui.splash  	Alignment com.aljameia.app.ui.splash  Arrangement com.aljameia.app.ui.splash  Box com.aljameia.app.ui.splash  Card com.aljameia.app.ui.splash  CardDefaults com.aljameia.app.ui.splash  CircularProgressIndicator com.aljameia.app.ui.splash  Column com.aljameia.app.ui.splash  
Composable com.aljameia.app.ui.splash  	Exception com.aljameia.app.ui.splash  Intent com.aljameia.app.ui.splash  Log com.aljameia.app.ui.splash  MainActivity com.aljameia.app.ui.splash  
MaterialTheme com.aljameia.app.ui.splash  Modifier com.aljameia.app.ui.splash  Spacer com.aljameia.app.ui.splash  SplashActivity com.aljameia.app.ui.splash  SplashScreen com.aljameia.app.ui.splash  Surface com.aljameia.app.ui.splash  Text com.aljameia.app.ui.splash  	TextAlign com.aljameia.app.ui.splash  delay com.aljameia.app.ui.splash  fillMaxSize com.aljameia.app.ui.splash  finish com.aljameia.app.ui.splash  height com.aljameia.app.ui.splash  java com.aljameia.app.ui.splash  launch com.aljameia.app.ui.splash  lifecycleScope com.aljameia.app.ui.splash  padding com.aljameia.app.ui.splash  
setContent com.aljameia.app.ui.splash  size com.aljameia.app.ui.splash  
startActivity com.aljameia.app.ui.splash  
AlJameiaTheme )com.aljameia.app.ui.splash.SplashActivity  Bundle )com.aljameia.app.ui.splash.SplashActivity  	Exception )com.aljameia.app.ui.splash.SplashActivity  Intent )com.aljameia.app.ui.splash.SplashActivity  Log )com.aljameia.app.ui.splash.SplashActivity  MainActivity )com.aljameia.app.ui.splash.SplashActivity  SplashScreen )com.aljameia.app.ui.splash.SplashActivity  delay )com.aljameia.app.ui.splash.SplashActivity  finish )com.aljameia.app.ui.splash.SplashActivity  getDELAY )com.aljameia.app.ui.splash.SplashActivity  getDelay )com.aljameia.app.ui.splash.SplashActivity  	getLAUNCH )com.aljameia.app.ui.splash.SplashActivity  getLIFECYCLEScope )com.aljameia.app.ui.splash.SplashActivity  	getLaunch )com.aljameia.app.ui.splash.SplashActivity  getLifecycleScope )com.aljameia.app.ui.splash.SplashActivity  
getSETContent )com.aljameia.app.ui.splash.SplashActivity  
getSetContent )com.aljameia.app.ui.splash.SplashActivity  java )com.aljameia.app.ui.splash.SplashActivity  launch )com.aljameia.app.ui.splash.SplashActivity  lifecycleScope )com.aljameia.app.ui.splash.SplashActivity  
setContent )com.aljameia.app.ui.splash.SplashActivity  
startActivity )com.aljameia.app.ui.splash.SplashActivity  
AlJameiaTheme com.aljameia.app.ui.theme  Boolean com.aljameia.app.ui.theme  Build com.aljameia.app.ui.theme  DarkColorScheme com.aljameia.app.ui.theme  LightColorScheme com.aljameia.app.ui.theme  Pink40 com.aljameia.app.ui.theme  Pink80 com.aljameia.app.ui.theme  Purple40 com.aljameia.app.ui.theme  Purple80 com.aljameia.app.ui.theme  PurpleGrey40 com.aljameia.app.ui.theme  PurpleGrey80 com.aljameia.app.ui.theme  
Typography com.aljameia.app.ui.theme  Unit com.aljameia.app.ui.theme  WindowCompat com.aljameia.app.ui.theme  FirebaseMessagingService com.google.firebase.messaging  
RemoteMessage com.google.firebase.messaging  Intent 3com.google.firebase.messaging.EnhancedIntentService  MainActivity 3com.google.firebase.messaging.EnhancedIntentService  NotificationCompat 3com.google.firebase.messaging.EnhancedIntentService  NotificationManager 3com.google.firebase.messaging.EnhancedIntentService  
PendingIntent 3com.google.firebase.messaging.EnhancedIntentService  R 3com.google.firebase.messaging.EnhancedIntentService  
RemoteMessage 3com.google.firebase.messaging.EnhancedIntentService  String 3com.google.firebase.messaging.EnhancedIntentService  System 3com.google.firebase.messaging.EnhancedIntentService  apply 3com.google.firebase.messaging.EnhancedIntentService  getSystemService 3com.google.firebase.messaging.EnhancedIntentService  java 3com.google.firebase.messaging.EnhancedIntentService  let 3com.google.firebase.messaging.EnhancedIntentService  onMessageReceived 3com.google.firebase.messaging.EnhancedIntentService  
onNewToken 3com.google.firebase.messaging.EnhancedIntentService  showNotification 3com.google.firebase.messaging.EnhancedIntentService  Intent 6com.google.firebase.messaging.FirebaseMessagingService  MainActivity 6com.google.firebase.messaging.FirebaseMessagingService  NotificationCompat 6com.google.firebase.messaging.FirebaseMessagingService  NotificationManager 6com.google.firebase.messaging.FirebaseMessagingService  
PendingIntent 6com.google.firebase.messaging.FirebaseMessagingService  R 6com.google.firebase.messaging.FirebaseMessagingService  
RemoteMessage 6com.google.firebase.messaging.FirebaseMessagingService  String 6com.google.firebase.messaging.FirebaseMessagingService  System 6com.google.firebase.messaging.FirebaseMessagingService  apply 6com.google.firebase.messaging.FirebaseMessagingService  getSystemService 6com.google.firebase.messaging.FirebaseMessagingService  java 6com.google.firebase.messaging.FirebaseMessagingService  let 6com.google.firebase.messaging.FirebaseMessagingService  onMessageReceived 6com.google.firebase.messaging.FirebaseMessagingService  
onNewToken 6com.google.firebase.messaging.FirebaseMessagingService  showNotification 6com.google.firebase.messaging.FirebaseMessagingService  Notification +com.google.firebase.messaging.RemoteMessage  getNOTIFICATION +com.google.firebase.messaging.RemoteMessage  getNotification +com.google.firebase.messaging.RemoteMessage  notification +com.google.firebase.messaging.RemoteMessage  setNotification +com.google.firebase.messaging.RemoteMessage  body 8com.google.firebase.messaging.RemoteMessage.Notification  getBODY 8com.google.firebase.messaging.RemoteMessage.Notification  getBody 8com.google.firebase.messaging.RemoteMessage.Notification  getLET 8com.google.firebase.messaging.RemoteMessage.Notification  getLet 8com.google.firebase.messaging.RemoteMessage.Notification  getTITLE 8com.google.firebase.messaging.RemoteMessage.Notification  getTitle 8com.google.firebase.messaging.RemoteMessage.Notification  let 8com.google.firebase.messaging.RemoteMessage.Notification  setBody 8com.google.firebase.messaging.RemoteMessage.Notification  setTitle 8com.google.firebase.messaging.RemoteMessage.Notification  title 8com.google.firebase.messaging.RemoteMessage.Notification  Gson com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  	TypeToken com.google.gson.reflect  AlJameiaDatabase 	java.lang  
AlJameiaTheme 	java.lang  	Alignment 	java.lang  Arrangement 	java.lang  
AuthScreen 	java.lang  
BigDecimal 	java.lang  Box 	java.lang  Build 	java.lang  Button 	java.lang  Card 	java.lang  CardDefaults 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  ClickableSettingItem 	java.lang  Column 	java.lang  ConnectivityManager 	java.lang  
Converters 	java.lang  DashboardScreen 	java.lang  Date 	java.lang  Document 	java.lang  DocumentViewerScreen 	java.lang  ElevatedButton 	java.lang  Event 	java.lang  	Exception 	java.lang  ExperimentalMaterial3Api 	java.lang  FinancialSummaryCard 	java.lang  
FontWeight 	java.lang  Gson 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  Intent 	java.lang  LanguageSettingItem 	java.lang  LazyRow 	java.lang  Log 	java.lang  MainActivity 	java.lang  
MainScreen 	java.lang  MainUiState 	java.lang  
MainViewModel 	java.lang  
MaterialTheme 	java.lang  Modifier 	java.lang  MutableStateFlow 	java.lang  NotificationChannel 	java.lang  NotificationCompat 	java.lang  NotificationManager 	java.lang  OnConflictStrategy 	java.lang  OutlinedTextField 	java.lang  PasswordVisualTransformation 	java.lang  
PendingIntent 	java.lang  
ProfileScreen 	java.lang  QuickActionCard 	java.lang  R 	java.lang  RadioButton 	java.lang  Room 	java.lang  Row 	java.lang  SettingsScreen 	java.lang  SettingsSectionHeader 	java.lang  Spacer 	java.lang  SplashScreen 	java.lang  Surface 	java.lang  Switch 	java.lang  SwitchSettingItem 	java.lang  System 	java.lang  Text 	java.lang  	TextAlign 	java.lang  
TextButton 	java.lang  Transaction 	java.lang  TransactionItem 	java.lang  User 	java.lang  ViewModelProvider 	java.lang  WindowCompat 	java.lang  
_isLoading 	java.lang  _uiState 	java.lang  androidx 	java.lang  apply 	java.lang  asStateFlow 	java.lang  delay 	java.lang  	emptyList 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  filter 	java.lang  find 	java.lang  finish 	java.lang  forEach 	java.lang  getAboutSettings 	java.lang  getAccountSettings 	java.lang  height 	java.lang  indexOfFirst 	java.lang  
isNotBlank 	java.lang  
isNullOrEmpty 	java.lang  java 	java.lang  kotlinx 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  
mutableListOf 	java.lang  padding 	java.lang  provideDelegate 	java.lang  	removeAll 	java.lang  size 	java.lang  
startActivity 	java.lang  synchronized 	java.lang  width 	java.lang  message java.lang.Exception  currentTimeMillis java.lang.System  Type java.lang.reflect  
BigDecimal 	java.math  toString java.math.BigDecimal  NumberFormat 	java.text  AccountBalance 	java.util  
AlJameiaTheme 	java.util  	Alignment 	java.util  Arrangement 	java.util  
Assessment 	java.util  Card 	java.util  CardDefaults 	java.util  CardMembership 	java.util  Column 	java.util  
Composable 	java.util  DashboardScreen 	java.util  Date 	java.util  Event 	java.util  ExperimentalMaterial3Api 	java.util  Favorite 	java.util  FinancialSummaryCard 	java.util  
FontWeight 	java.util  History 	java.util  Icon 	java.util  LazyRow 	java.util  
MaterialTheme 	java.util  Modifier 	java.util  Payment 	java.util  QuickActionCard 	java.util  Row 	java.util  Scaffold 	java.util  Schedule 	java.util  Spacer 	java.util  	SwapHoriz 	java.util  Text 	java.util  	TextAlign 	java.util  	TopAppBar 	java.util  TopAppBarDefaults 	java.util  TransactionItem 	java.util  
TrendingUp 	java.util  androidx 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  height 	java.util  items 	java.util  listOf 	java.util  padding 	java.util  
setContent 	java.util  size 	java.util  width 	java.util  getTIME java.util.Date  getTime java.util.Date  setTime java.util.Date  time java.util.Date  Inject javax.inject  	Singleton javax.inject  AlJameiaDatabase kotlin  
AlJameiaTheme kotlin  	Alignment kotlin  Arrangement kotlin  Array kotlin  
AuthScreen kotlin  
BigDecimal kotlin  Boolean kotlin  Box kotlin  Build kotlin  Button kotlin  Card kotlin  CardDefaults kotlin  CircularProgressIndicator kotlin  ClickableSettingItem kotlin  Column kotlin  ConnectivityManager kotlin  
Converters kotlin  DashboardScreen kotlin  Date kotlin  Document kotlin  DocumentViewerScreen kotlin  Double kotlin  ElevatedButton kotlin  Event kotlin  	Exception kotlin  ExperimentalMaterial3Api kotlin  FinancialSummaryCard kotlin  Float kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Gson kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  Int kotlin  Intent kotlin  LanguageSettingItem kotlin  LazyRow kotlin  Log kotlin  Long kotlin  MainActivity kotlin  
MainScreen kotlin  MainUiState kotlin  
MainViewModel kotlin  
MaterialTheme kotlin  Modifier kotlin  MutableStateFlow kotlin  Nothing kotlin  NotificationChannel kotlin  NotificationCompat kotlin  NotificationManager kotlin  OnConflictStrategy kotlin  OptIn kotlin  OutlinedTextField kotlin  PasswordVisualTransformation kotlin  
PendingIntent kotlin  
ProfileScreen kotlin  QuickActionCard kotlin  R kotlin  RadioButton kotlin  Room kotlin  Row kotlin  SettingsScreen kotlin  SettingsSectionHeader kotlin  Spacer kotlin  SplashScreen kotlin  String kotlin  Surface kotlin  Switch kotlin  SwitchSettingItem kotlin  System kotlin  Text kotlin  	TextAlign kotlin  
TextButton kotlin  Transaction kotlin  TransactionItem kotlin  Unit kotlin  User kotlin  ViewModelProvider kotlin  Volatile kotlin  WindowCompat kotlin  
_isLoading kotlin  _uiState kotlin  androidx kotlin  apply kotlin  arrayOf kotlin  asStateFlow kotlin  delay kotlin  	emptyList kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  filter kotlin  find kotlin  finish kotlin  forEach kotlin  getAboutSettings kotlin  getAccountSettings kotlin  height kotlin  indexOfFirst kotlin  
isNotBlank kotlin  
isNullOrEmpty kotlin  java kotlin  kotlinx kotlin  launch kotlin  let kotlin  listOf kotlin  
mutableListOf kotlin  padding kotlin  provideDelegate kotlin  	removeAll kotlin  size kotlin  
startActivity kotlin  synchronized kotlin  width kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  getLET kotlin.Long  getLet kotlin.Long  
getISNotBlank 
kotlin.String  getISNullOrEmpty 
kotlin.String  
getIsNotBlank 
kotlin.String  getIsNullOrEmpty 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  
isNotBlank 
kotlin.String  
isNullOrEmpty 
kotlin.String  AlJameiaDatabase kotlin.annotation  
AlJameiaTheme kotlin.annotation  	Alignment kotlin.annotation  Arrangement kotlin.annotation  
AuthScreen kotlin.annotation  
BigDecimal kotlin.annotation  Box kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  Card kotlin.annotation  CardDefaults kotlin.annotation  CircularProgressIndicator kotlin.annotation  ClickableSettingItem kotlin.annotation  Column kotlin.annotation  ConnectivityManager kotlin.annotation  
Converters kotlin.annotation  DashboardScreen kotlin.annotation  Date kotlin.annotation  Document kotlin.annotation  DocumentViewerScreen kotlin.annotation  ElevatedButton kotlin.annotation  Event kotlin.annotation  	Exception kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  FinancialSummaryCard kotlin.annotation  
FontWeight kotlin.annotation  Gson kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  Intent kotlin.annotation  LanguageSettingItem kotlin.annotation  LazyRow kotlin.annotation  Log kotlin.annotation  MainActivity kotlin.annotation  
MainScreen kotlin.annotation  MainUiState kotlin.annotation  
MainViewModel kotlin.annotation  
MaterialTheme kotlin.annotation  Modifier kotlin.annotation  MutableStateFlow kotlin.annotation  NotificationChannel kotlin.annotation  NotificationCompat kotlin.annotation  NotificationManager kotlin.annotation  OnConflictStrategy kotlin.annotation  OutlinedTextField kotlin.annotation  PasswordVisualTransformation kotlin.annotation  
PendingIntent kotlin.annotation  
ProfileScreen kotlin.annotation  QuickActionCard kotlin.annotation  R kotlin.annotation  RadioButton kotlin.annotation  Room kotlin.annotation  Row kotlin.annotation  SettingsScreen kotlin.annotation  SettingsSectionHeader kotlin.annotation  Spacer kotlin.annotation  SplashScreen kotlin.annotation  Surface kotlin.annotation  Switch kotlin.annotation  SwitchSettingItem kotlin.annotation  System kotlin.annotation  Text kotlin.annotation  	TextAlign kotlin.annotation  
TextButton kotlin.annotation  Transaction kotlin.annotation  TransactionItem kotlin.annotation  User kotlin.annotation  ViewModelProvider kotlin.annotation  Volatile kotlin.annotation  WindowCompat kotlin.annotation  
_isLoading kotlin.annotation  _uiState kotlin.annotation  androidx kotlin.annotation  apply kotlin.annotation  asStateFlow kotlin.annotation  delay kotlin.annotation  	emptyList kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  filter kotlin.annotation  find kotlin.annotation  finish kotlin.annotation  forEach kotlin.annotation  getAboutSettings kotlin.annotation  getAccountSettings kotlin.annotation  height kotlin.annotation  indexOfFirst kotlin.annotation  
isNotBlank kotlin.annotation  
isNullOrEmpty kotlin.annotation  java kotlin.annotation  kotlinx kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  
mutableListOf kotlin.annotation  padding kotlin.annotation  provideDelegate kotlin.annotation  	removeAll kotlin.annotation  size kotlin.annotation  
startActivity kotlin.annotation  synchronized kotlin.annotation  width kotlin.annotation  AlJameiaDatabase kotlin.collections  
AlJameiaTheme kotlin.collections  	Alignment kotlin.collections  Arrangement kotlin.collections  
AuthScreen kotlin.collections  
BigDecimal kotlin.collections  Box kotlin.collections  Build kotlin.collections  Button kotlin.collections  Card kotlin.collections  CardDefaults kotlin.collections  CircularProgressIndicator kotlin.collections  ClickableSettingItem kotlin.collections  Column kotlin.collections  ConnectivityManager kotlin.collections  
Converters kotlin.collections  DashboardScreen kotlin.collections  Date kotlin.collections  Document kotlin.collections  DocumentViewerScreen kotlin.collections  ElevatedButton kotlin.collections  Event kotlin.collections  	Exception kotlin.collections  ExperimentalMaterial3Api kotlin.collections  FinancialSummaryCard kotlin.collections  
FontWeight kotlin.collections  Gson kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  Intent kotlin.collections  LanguageSettingItem kotlin.collections  LazyRow kotlin.collections  List kotlin.collections  Log kotlin.collections  MainActivity kotlin.collections  
MainScreen kotlin.collections  MainUiState kotlin.collections  
MainViewModel kotlin.collections  
MaterialTheme kotlin.collections  Modifier kotlin.collections  MutableList kotlin.collections  MutableStateFlow kotlin.collections  NotificationChannel kotlin.collections  NotificationCompat kotlin.collections  NotificationManager kotlin.collections  OnConflictStrategy kotlin.collections  OutlinedTextField kotlin.collections  PasswordVisualTransformation kotlin.collections  
PendingIntent kotlin.collections  
ProfileScreen kotlin.collections  QuickActionCard kotlin.collections  R kotlin.collections  RadioButton kotlin.collections  Room kotlin.collections  Row kotlin.collections  SettingsScreen kotlin.collections  SettingsSectionHeader kotlin.collections  Spacer kotlin.collections  SplashScreen kotlin.collections  Surface kotlin.collections  Switch kotlin.collections  SwitchSettingItem kotlin.collections  System kotlin.collections  Text kotlin.collections  	TextAlign kotlin.collections  
TextButton kotlin.collections  Transaction kotlin.collections  TransactionItem kotlin.collections  User kotlin.collections  ViewModelProvider kotlin.collections  Volatile kotlin.collections  WindowCompat kotlin.collections  
_isLoading kotlin.collections  _uiState kotlin.collections  androidx kotlin.collections  apply kotlin.collections  asStateFlow kotlin.collections  delay kotlin.collections  	emptyList kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  filter kotlin.collections  find kotlin.collections  finish kotlin.collections  forEach kotlin.collections  getAboutSettings kotlin.collections  getAccountSettings kotlin.collections  height kotlin.collections  indexOfFirst kotlin.collections  
isNotBlank kotlin.collections  
isNullOrEmpty kotlin.collections  java kotlin.collections  kotlinx kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  padding kotlin.collections  provideDelegate kotlin.collections  	removeAll kotlin.collections  size kotlin.collections  
startActivity kotlin.collections  synchronized kotlin.collections  width kotlin.collections  	getFILTER kotlin.collections.MutableList  getFIND kotlin.collections.MutableList  	getFilter kotlin.collections.MutableList  getFind kotlin.collections.MutableList  getINDEXOfFirst kotlin.collections.MutableList  getIndexOfFirst kotlin.collections.MutableList  getREMOVEAll kotlin.collections.MutableList  getRemoveAll kotlin.collections.MutableList  AlJameiaDatabase kotlin.comparisons  
AlJameiaTheme kotlin.comparisons  	Alignment kotlin.comparisons  Arrangement kotlin.comparisons  
AuthScreen kotlin.comparisons  
BigDecimal kotlin.comparisons  Box kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  Card kotlin.comparisons  CardDefaults kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  ClickableSettingItem kotlin.comparisons  Column kotlin.comparisons  ConnectivityManager kotlin.comparisons  
Converters kotlin.comparisons  DashboardScreen kotlin.comparisons  Date kotlin.comparisons  Document kotlin.comparisons  DocumentViewerScreen kotlin.comparisons  ElevatedButton kotlin.comparisons  Event kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  FinancialSummaryCard kotlin.comparisons  
FontWeight kotlin.comparisons  Gson kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  Intent kotlin.comparisons  LanguageSettingItem kotlin.comparisons  LazyRow kotlin.comparisons  Log kotlin.comparisons  MainActivity kotlin.comparisons  
MainScreen kotlin.comparisons  MainUiState kotlin.comparisons  
MainViewModel kotlin.comparisons  
MaterialTheme kotlin.comparisons  Modifier kotlin.comparisons  MutableStateFlow kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationCompat kotlin.comparisons  NotificationManager kotlin.comparisons  OnConflictStrategy kotlin.comparisons  OutlinedTextField kotlin.comparisons  PasswordVisualTransformation kotlin.comparisons  
PendingIntent kotlin.comparisons  
ProfileScreen kotlin.comparisons  QuickActionCard kotlin.comparisons  R kotlin.comparisons  RadioButton kotlin.comparisons  Room kotlin.comparisons  Row kotlin.comparisons  SettingsScreen kotlin.comparisons  SettingsSectionHeader kotlin.comparisons  Spacer kotlin.comparisons  SplashScreen kotlin.comparisons  Surface kotlin.comparisons  Switch kotlin.comparisons  SwitchSettingItem kotlin.comparisons  System kotlin.comparisons  Text kotlin.comparisons  	TextAlign kotlin.comparisons  
TextButton kotlin.comparisons  Transaction kotlin.comparisons  TransactionItem kotlin.comparisons  User kotlin.comparisons  ViewModelProvider kotlin.comparisons  Volatile kotlin.comparisons  WindowCompat kotlin.comparisons  
_isLoading kotlin.comparisons  _uiState kotlin.comparisons  androidx kotlin.comparisons  apply kotlin.comparisons  asStateFlow kotlin.comparisons  delay kotlin.comparisons  	emptyList kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  filter kotlin.comparisons  find kotlin.comparisons  finish kotlin.comparisons  forEach kotlin.comparisons  getAboutSettings kotlin.comparisons  getAccountSettings kotlin.comparisons  height kotlin.comparisons  indexOfFirst kotlin.comparisons  
isNotBlank kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  java kotlin.comparisons  kotlinx kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  
mutableListOf kotlin.comparisons  padding kotlin.comparisons  provideDelegate kotlin.comparisons  	removeAll kotlin.comparisons  size kotlin.comparisons  
startActivity kotlin.comparisons  synchronized kotlin.comparisons  width kotlin.comparisons  SuspendFunction1 kotlin.coroutines  AlJameiaDatabase 	kotlin.io  
AlJameiaTheme 	kotlin.io  	Alignment 	kotlin.io  Arrangement 	kotlin.io  
AuthScreen 	kotlin.io  
BigDecimal 	kotlin.io  Box 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  Card 	kotlin.io  CardDefaults 	kotlin.io  CircularProgressIndicator 	kotlin.io  ClickableSettingItem 	kotlin.io  Column 	kotlin.io  ConnectivityManager 	kotlin.io  
Converters 	kotlin.io  DashboardScreen 	kotlin.io  Date 	kotlin.io  Document 	kotlin.io  DocumentViewerScreen 	kotlin.io  ElevatedButton 	kotlin.io  Event 	kotlin.io  	Exception 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  FinancialSummaryCard 	kotlin.io  
FontWeight 	kotlin.io  Gson 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  Intent 	kotlin.io  LanguageSettingItem 	kotlin.io  LazyRow 	kotlin.io  Log 	kotlin.io  MainActivity 	kotlin.io  
MainScreen 	kotlin.io  MainUiState 	kotlin.io  
MainViewModel 	kotlin.io  
MaterialTheme 	kotlin.io  Modifier 	kotlin.io  MutableStateFlow 	kotlin.io  NotificationChannel 	kotlin.io  NotificationCompat 	kotlin.io  NotificationManager 	kotlin.io  OnConflictStrategy 	kotlin.io  OutlinedTextField 	kotlin.io  PasswordVisualTransformation 	kotlin.io  
PendingIntent 	kotlin.io  
ProfileScreen 	kotlin.io  QuickActionCard 	kotlin.io  R 	kotlin.io  RadioButton 	kotlin.io  Room 	kotlin.io  Row 	kotlin.io  SettingsScreen 	kotlin.io  SettingsSectionHeader 	kotlin.io  Spacer 	kotlin.io  SplashScreen 	kotlin.io  Surface 	kotlin.io  Switch 	kotlin.io  SwitchSettingItem 	kotlin.io  System 	kotlin.io  Text 	kotlin.io  	TextAlign 	kotlin.io  
TextButton 	kotlin.io  Transaction 	kotlin.io  TransactionItem 	kotlin.io  User 	kotlin.io  ViewModelProvider 	kotlin.io  Volatile 	kotlin.io  WindowCompat 	kotlin.io  
_isLoading 	kotlin.io  _uiState 	kotlin.io  androidx 	kotlin.io  apply 	kotlin.io  asStateFlow 	kotlin.io  delay 	kotlin.io  	emptyList 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  filter 	kotlin.io  find 	kotlin.io  finish 	kotlin.io  forEach 	kotlin.io  getAboutSettings 	kotlin.io  getAccountSettings 	kotlin.io  height 	kotlin.io  indexOfFirst 	kotlin.io  
isNotBlank 	kotlin.io  
isNullOrEmpty 	kotlin.io  java 	kotlin.io  kotlinx 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  
mutableListOf 	kotlin.io  padding 	kotlin.io  provideDelegate 	kotlin.io  	removeAll 	kotlin.io  size 	kotlin.io  
startActivity 	kotlin.io  synchronized 	kotlin.io  width 	kotlin.io  AlJameiaDatabase 
kotlin.jvm  
AlJameiaTheme 
kotlin.jvm  	Alignment 
kotlin.jvm  Arrangement 
kotlin.jvm  
AuthScreen 
kotlin.jvm  
BigDecimal 
kotlin.jvm  Box 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  Card 
kotlin.jvm  CardDefaults 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  ClickableSettingItem 
kotlin.jvm  Column 
kotlin.jvm  ConnectivityManager 
kotlin.jvm  
Converters 
kotlin.jvm  DashboardScreen 
kotlin.jvm  Date 
kotlin.jvm  Document 
kotlin.jvm  DocumentViewerScreen 
kotlin.jvm  ElevatedButton 
kotlin.jvm  Event 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  FinancialSummaryCard 
kotlin.jvm  
FontWeight 
kotlin.jvm  Gson 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  Intent 
kotlin.jvm  LanguageSettingItem 
kotlin.jvm  LazyRow 
kotlin.jvm  Log 
kotlin.jvm  MainActivity 
kotlin.jvm  
MainScreen 
kotlin.jvm  MainUiState 
kotlin.jvm  
MainViewModel 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Modifier 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationCompat 
kotlin.jvm  NotificationManager 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  PasswordVisualTransformation 
kotlin.jvm  
PendingIntent 
kotlin.jvm  
ProfileScreen 
kotlin.jvm  QuickActionCard 
kotlin.jvm  R 
kotlin.jvm  RadioButton 
kotlin.jvm  Room 
kotlin.jvm  Row 
kotlin.jvm  SettingsScreen 
kotlin.jvm  SettingsSectionHeader 
kotlin.jvm  Spacer 
kotlin.jvm  SplashScreen 
kotlin.jvm  Surface 
kotlin.jvm  Switch 
kotlin.jvm  SwitchSettingItem 
kotlin.jvm  System 
kotlin.jvm  Text 
kotlin.jvm  	TextAlign 
kotlin.jvm  
TextButton 
kotlin.jvm  Transaction 
kotlin.jvm  TransactionItem 
kotlin.jvm  User 
kotlin.jvm  ViewModelProvider 
kotlin.jvm  Volatile 
kotlin.jvm  WindowCompat 
kotlin.jvm  
_isLoading 
kotlin.jvm  _uiState 
kotlin.jvm  androidx 
kotlin.jvm  apply 
kotlin.jvm  asStateFlow 
kotlin.jvm  delay 
kotlin.jvm  	emptyList 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  filter 
kotlin.jvm  find 
kotlin.jvm  finish 
kotlin.jvm  forEach 
kotlin.jvm  getAboutSettings 
kotlin.jvm  getAccountSettings 
kotlin.jvm  height 
kotlin.jvm  indexOfFirst 
kotlin.jvm  
isNotBlank 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  java 
kotlin.jvm  kotlinx 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  padding 
kotlin.jvm  provideDelegate 
kotlin.jvm  	removeAll 
kotlin.jvm  size 
kotlin.jvm  
startActivity 
kotlin.jvm  synchronized 
kotlin.jvm  width 
kotlin.jvm  AlJameiaDatabase 
kotlin.ranges  
AlJameiaTheme 
kotlin.ranges  	Alignment 
kotlin.ranges  Arrangement 
kotlin.ranges  
AuthScreen 
kotlin.ranges  
BigDecimal 
kotlin.ranges  Box 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  Card 
kotlin.ranges  CardDefaults 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  ClickableSettingItem 
kotlin.ranges  Column 
kotlin.ranges  ConnectivityManager 
kotlin.ranges  
Converters 
kotlin.ranges  DashboardScreen 
kotlin.ranges  Date 
kotlin.ranges  Document 
kotlin.ranges  DocumentViewerScreen 
kotlin.ranges  ElevatedButton 
kotlin.ranges  Event 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  FinancialSummaryCard 
kotlin.ranges  
FontWeight 
kotlin.ranges  Gson 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  Intent 
kotlin.ranges  LanguageSettingItem 
kotlin.ranges  LazyRow 
kotlin.ranges  Log 
kotlin.ranges  MainActivity 
kotlin.ranges  
MainScreen 
kotlin.ranges  MainUiState 
kotlin.ranges  
MainViewModel 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Modifier 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationCompat 
kotlin.ranges  NotificationManager 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  PasswordVisualTransformation 
kotlin.ranges  
PendingIntent 
kotlin.ranges  
ProfileScreen 
kotlin.ranges  QuickActionCard 
kotlin.ranges  R 
kotlin.ranges  RadioButton 
kotlin.ranges  Room 
kotlin.ranges  Row 
kotlin.ranges  SettingsScreen 
kotlin.ranges  SettingsSectionHeader 
kotlin.ranges  Spacer 
kotlin.ranges  SplashScreen 
kotlin.ranges  Surface 
kotlin.ranges  Switch 
kotlin.ranges  SwitchSettingItem 
kotlin.ranges  System 
kotlin.ranges  Text 
kotlin.ranges  	TextAlign 
kotlin.ranges  
TextButton 
kotlin.ranges  Transaction 
kotlin.ranges  TransactionItem 
kotlin.ranges  User 
kotlin.ranges  ViewModelProvider 
kotlin.ranges  Volatile 
kotlin.ranges  WindowCompat 
kotlin.ranges  
_isLoading 
kotlin.ranges  _uiState 
kotlin.ranges  androidx 
kotlin.ranges  apply 
kotlin.ranges  asStateFlow 
kotlin.ranges  delay 
kotlin.ranges  	emptyList 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  filter 
kotlin.ranges  find 
kotlin.ranges  finish 
kotlin.ranges  forEach 
kotlin.ranges  getAboutSettings 
kotlin.ranges  getAccountSettings 
kotlin.ranges  height 
kotlin.ranges  indexOfFirst 
kotlin.ranges  
isNotBlank 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  java 
kotlin.ranges  kotlinx 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  padding 
kotlin.ranges  provideDelegate 
kotlin.ranges  	removeAll 
kotlin.ranges  size 
kotlin.ranges  
startActivity 
kotlin.ranges  synchronized 
kotlin.ranges  width 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  AlJameiaDatabase kotlin.sequences  
AlJameiaTheme kotlin.sequences  	Alignment kotlin.sequences  Arrangement kotlin.sequences  
AuthScreen kotlin.sequences  
BigDecimal kotlin.sequences  Box kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  Card kotlin.sequences  CardDefaults kotlin.sequences  CircularProgressIndicator kotlin.sequences  ClickableSettingItem kotlin.sequences  Column kotlin.sequences  ConnectivityManager kotlin.sequences  
Converters kotlin.sequences  DashboardScreen kotlin.sequences  Date kotlin.sequences  Document kotlin.sequences  DocumentViewerScreen kotlin.sequences  ElevatedButton kotlin.sequences  Event kotlin.sequences  	Exception kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  FinancialSummaryCard kotlin.sequences  
FontWeight kotlin.sequences  Gson kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  Intent kotlin.sequences  LanguageSettingItem kotlin.sequences  LazyRow kotlin.sequences  Log kotlin.sequences  MainActivity kotlin.sequences  
MainScreen kotlin.sequences  MainUiState kotlin.sequences  
MainViewModel kotlin.sequences  
MaterialTheme kotlin.sequences  Modifier kotlin.sequences  MutableStateFlow kotlin.sequences  NotificationChannel kotlin.sequences  NotificationCompat kotlin.sequences  NotificationManager kotlin.sequences  OnConflictStrategy kotlin.sequences  OutlinedTextField kotlin.sequences  PasswordVisualTransformation kotlin.sequences  
PendingIntent kotlin.sequences  
ProfileScreen kotlin.sequences  QuickActionCard kotlin.sequences  R kotlin.sequences  RadioButton kotlin.sequences  Room kotlin.sequences  Row kotlin.sequences  SettingsScreen kotlin.sequences  SettingsSectionHeader kotlin.sequences  Spacer kotlin.sequences  SplashScreen kotlin.sequences  Surface kotlin.sequences  Switch kotlin.sequences  SwitchSettingItem kotlin.sequences  System kotlin.sequences  Text kotlin.sequences  	TextAlign kotlin.sequences  
TextButton kotlin.sequences  Transaction kotlin.sequences  TransactionItem kotlin.sequences  User kotlin.sequences  ViewModelProvider kotlin.sequences  Volatile kotlin.sequences  WindowCompat kotlin.sequences  
_isLoading kotlin.sequences  _uiState kotlin.sequences  androidx kotlin.sequences  apply kotlin.sequences  asStateFlow kotlin.sequences  delay kotlin.sequences  	emptyList kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  finish kotlin.sequences  forEach kotlin.sequences  getAboutSettings kotlin.sequences  getAccountSettings kotlin.sequences  height kotlin.sequences  indexOfFirst kotlin.sequences  
isNotBlank kotlin.sequences  
isNullOrEmpty kotlin.sequences  java kotlin.sequences  kotlinx kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  
mutableListOf kotlin.sequences  padding kotlin.sequences  provideDelegate kotlin.sequences  	removeAll kotlin.sequences  size kotlin.sequences  
startActivity kotlin.sequences  synchronized kotlin.sequences  width kotlin.sequences  AlJameiaDatabase kotlin.text  
AlJameiaTheme kotlin.text  	Alignment kotlin.text  Arrangement kotlin.text  
AuthScreen kotlin.text  
BigDecimal kotlin.text  Box kotlin.text  Build kotlin.text  Button kotlin.text  Card kotlin.text  CardDefaults kotlin.text  CircularProgressIndicator kotlin.text  ClickableSettingItem kotlin.text  Column kotlin.text  ConnectivityManager kotlin.text  
Converters kotlin.text  DashboardScreen kotlin.text  Date kotlin.text  Document kotlin.text  DocumentViewerScreen kotlin.text  ElevatedButton kotlin.text  Event kotlin.text  	Exception kotlin.text  ExperimentalMaterial3Api kotlin.text  FinancialSummaryCard kotlin.text  
FontWeight kotlin.text  Gson kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  Intent kotlin.text  LanguageSettingItem kotlin.text  LazyRow kotlin.text  Log kotlin.text  MainActivity kotlin.text  
MainScreen kotlin.text  MainUiState kotlin.text  
MainViewModel kotlin.text  
MaterialTheme kotlin.text  Modifier kotlin.text  MutableStateFlow kotlin.text  NotificationChannel kotlin.text  NotificationCompat kotlin.text  NotificationManager kotlin.text  OnConflictStrategy kotlin.text  OutlinedTextField kotlin.text  PasswordVisualTransformation kotlin.text  
PendingIntent kotlin.text  
ProfileScreen kotlin.text  QuickActionCard kotlin.text  R kotlin.text  RadioButton kotlin.text  Room kotlin.text  Row kotlin.text  SettingsScreen kotlin.text  SettingsSectionHeader kotlin.text  Spacer kotlin.text  SplashScreen kotlin.text  Surface kotlin.text  Switch kotlin.text  SwitchSettingItem kotlin.text  System kotlin.text  Text kotlin.text  	TextAlign kotlin.text  
TextButton kotlin.text  Transaction kotlin.text  TransactionItem kotlin.text  User kotlin.text  ViewModelProvider kotlin.text  Volatile kotlin.text  WindowCompat kotlin.text  
_isLoading kotlin.text  _uiState kotlin.text  androidx kotlin.text  apply kotlin.text  asStateFlow kotlin.text  delay kotlin.text  	emptyList kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  filter kotlin.text  find kotlin.text  finish kotlin.text  forEach kotlin.text  getAboutSettings kotlin.text  getAccountSettings kotlin.text  height kotlin.text  indexOfFirst kotlin.text  
isNotBlank kotlin.text  
isNullOrEmpty kotlin.text  java kotlin.text  kotlinx kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  
mutableListOf kotlin.text  padding kotlin.text  provideDelegate kotlin.text  	removeAll kotlin.text  size kotlin.text  
startActivity kotlin.text  synchronized kotlin.text  width kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  Intent !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  MainActivity !kotlinx.coroutines.CoroutineScope  MainUiState !kotlinx.coroutines.CoroutineScope  
_isLoading !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  finish !kotlinx.coroutines.CoroutineScope  getDELAY !kotlinx.coroutines.CoroutineScope  getDelay !kotlinx.coroutines.CoroutineScope  	getFINISH !kotlinx.coroutines.CoroutineScope  	getFinish !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getSTARTActivity !kotlinx.coroutines.CoroutineScope  getStartActivity !kotlinx.coroutines.CoroutineScope  
get_isLoading !kotlinx.coroutines.CoroutineScope  get_uiState !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  
startActivity !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  getCOLLECTAsState !kotlinx.coroutines.flow.StateFlow  getCollectAsState !kotlinx.coroutines.flow.StateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          