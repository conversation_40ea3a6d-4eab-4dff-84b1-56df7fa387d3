'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import {
  ChartBarIcon,
  DocumentChartBarIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ArrowDownTrayIcon,
  FunnelIcon,
  EyeIcon,
  PrinterIcon
} from '@heroicons/react/24/outline';

export default function ReportsPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState('this_month');
  const [selectedReport, setSelectedReport] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);

  // Mock data for reports
  const reportData = {
    overview: {
      totalInvestment: 156000,
      totalReturns: 23400,
      activeShares: 47,
      completedCycles: 12,
      averageReturn: 15.2,
      portfolioGrowth: 18.5
    },
    performance: {
      monthlyReturns: [
        { month: 'يناير', returns: 12500, investment: 45000 },
        { month: 'فبراير', returns: 15200, investment: 52000 },
        { month: 'مارس', returns: 18900, investment: 58000 },
        { month: 'أبريل', returns: 21300, investment: 65000 },
        { month: 'مايو', returns: 19800, investment: 71000 },
        { month: 'يونيو', returns: 23400, investment: 78000 }
      ]
    },
    shares: {
      byJamiya: [
        { name: 'جمعية الأصدقاء', shares: 15, value: 37500, returns: 8200 },
        { name: 'جمعية العائلة', shares: 12, value: 18000, returns: 4100 },
        { name: 'جمعية الأحياء', shares: 20, value: 100000, returns: 11100 }
      ]
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const reportTypes = [
    {
      id: 'overview',
      name: 'نظرة عامة',
      description: 'ملخص شامل للاستثمارات والعوائد',
      icon: ChartBarIcon,
      color: 'blue'
    },
    {
      id: 'performance',
      name: 'تقرير الأداء',
      description: 'تحليل مفصل لأداء الاستثمارات',
      icon: TrendingUpIcon,
      color: 'green'
    },
    {
      id: 'shares',
      name: 'تقرير الأسهم',
      description: 'تفاصيل الأسهم حسب الجمعيات',
      icon: CurrencyDollarIcon,
      color: 'purple'
    },
    {
      id: 'projections',
      name: 'التوقعات المستقبلية',
      description: 'توقعات العوائد والنمو',
      icon: DocumentChartBarIcon,
      color: 'orange'
    }
  ];

  const periods = [
    { value: 'this_month', label: 'هذا الشهر' },
    { value: 'last_month', label: 'الشهر الماضي' },
    { value: 'this_quarter', label: 'هذا الربع' },
    { value: 'this_year', label: 'هذا العام' },
    { value: 'all_time', label: 'جميع الأوقات' }
  ];

  const handleExportReport = (format: 'pdf' | 'excel') => {
    setIsLoading(true);
    // Simulate export process
    setTimeout(() => {
      setIsLoading(false);
      alert(`تم تصدير التقرير بصيغة ${format.toUpperCase()} بنجاح!`);
    }, 2000);
  };

  return (
    <ProtectedRoute requiredPermissions={['reports']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">التقارير والتحليلات</h1>
                <p className="text-gray-600 mt-1">
                  تقارير شاملة ومفصلة لاستثماراتك وعوائدك
                </p>
              </div>
              <div className="flex items-center space-x-4 space-x-reverse">
                <button
                  onClick={() => handleExportReport('pdf')}
                  disabled={isLoading}
                  className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
                >
                  <ArrowDownTrayIcon className="h-5 w-5 ml-2" />
                  تصدير PDF
                </button>
                <button
                  onClick={() => handleExportReport('excel')}
                  disabled={isLoading}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  <ArrowDownTrayIcon className="h-5 w-5 ml-2" />
                  تصدير Excel
                </button>
                <button className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                  <PrinterIcon className="h-5 w-5 ml-2" />
                  طباعة
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع التقرير
                </label>
                <select
                  value={selectedReport}
                  onChange={(e) => setSelectedReport(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {reportTypes.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفترة الزمنية
                </label>
                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {periods.map((period) => (
                    <option key={period.value} value={period.value}>
                      {period.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-end">
                <button className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  <FunnelIcon className="h-5 w-5 ml-2" />
                  تطبيق الفلاتر
                </button>
              </div>
            </div>
          </motion.div>

          {/* Report Types Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            {reportTypes.map((type) => (
              <div
                key={type.id}
                onClick={() => setSelectedReport(type.id)}
                className={`
                  p-6 rounded-lg border-2 cursor-pointer transition-all duration-200
                  ${selectedReport === type.id 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 bg-white hover:border-gray-300'
                  }
                `}
              >
                <div className="flex items-center mb-4">
                  <div className={`
                    p-3 rounded-lg
                    ${type.color === 'blue' ? 'bg-blue-100' :
                      type.color === 'green' ? 'bg-green-100' :
                      type.color === 'purple' ? 'bg-purple-100' :
                      type.color === 'orange' ? 'bg-orange-100' :
                      'bg-gray-100'
                    }
                  `}>
                    <type.icon className={`
                      h-6 w-6
                      ${type.color === 'blue' ? 'text-blue-600' :
                        type.color === 'green' ? 'text-green-600' :
                        type.color === 'purple' ? 'text-purple-600' :
                        type.color === 'orange' ? 'text-orange-600' :
                        'text-gray-600'
                      }
                    `} />
                  </div>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{type.name}</h3>
                <p className="text-sm text-gray-600">{type.description}</p>
              </div>
            ))}
          </motion.div>

          {/* Report Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-8"
          >
            {selectedReport === 'overview' && (
              <div className="space-y-6">
                {/* Overview Stats */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div className="flex items-center">
                      <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-2xl font-bold text-blue-600">
                          {formatCurrency(reportData.overview.totalInvestment)}
                        </p>
                        <p className="text-sm text-gray-600">إجمالي الاستثمارات</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div className="flex items-center">
                      <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <TrendingUpIcon className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-2xl font-bold text-green-600">
                          {formatCurrency(reportData.overview.totalReturns)}
                        </p>
                        <p className="text-sm text-gray-600">إجمالي العوائد</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div className="flex items-center">
                      <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <ChartBarIcon className="h-6 w-6 text-purple-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-2xl font-bold text-purple-600">
                          {reportData.overview.activeShares}
                        </p>
                        <p className="text-sm text-gray-600">الأسهم النشطة</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div className="flex items-center">
                      <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <CalendarIcon className="h-6 w-6 text-orange-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-2xl font-bold text-orange-600">
                          {reportData.overview.completedCycles}
                        </p>
                        <p className="text-sm text-gray-600">الدورات المكتملة</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div className="flex items-center">
                      <div className="h-12 w-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <TrendingUpIcon className="h-6 w-6 text-indigo-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-2xl font-bold text-indigo-600">
                          %{reportData.overview.averageReturn}
                        </p>
                        <p className="text-sm text-gray-600">متوسط العائد</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div className="flex items-center">
                      <div className="h-12 w-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                        <TrendingUpIcon className="h-6 w-6 text-emerald-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-2xl font-bold text-emerald-600">
                          %{reportData.overview.portfolioGrowth}
                        </p>
                        <p className="text-sm text-gray-600">نمو المحفظة</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Performance Chart Placeholder */}
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">أداء المحفظة الاستثمارية</h3>
                  <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">رسم بياني تفاعلي لأداء المحفظة</p>
                      <p className="text-sm text-gray-400 mt-1">سيتم إضافة مكتبة الرسوم البيانية قريباً</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {selectedReport === 'performance' && (
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">الأداء الشهري</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الشهر</th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاستثمار</th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">العوائد</th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">نسبة العائد</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {reportData.performance.monthlyReturns.map((month, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {month.month}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatCurrency(month.investment)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                              {formatCurrency(month.returns)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              %{((month.returns / month.investment) * 100).toFixed(1)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {selectedReport === 'shares' && (
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع الأسهم حسب الجمعيات</h3>
                  <div className="space-y-4">
                    {reportData.shares.byJamiya.map((jamiya, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{jamiya.name}</h4>
                          <span className="text-sm text-gray-500">{jamiya.shares} سهم</span>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">القيمة الإجمالية:</span>
                            <span className="mr-2 font-medium">{formatCurrency(jamiya.value)}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">العوائد:</span>
                            <span className="mr-2 font-medium text-green-600">{formatCurrency(jamiya.returns)}</span>
                          </div>
                        </div>
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${(jamiya.returns / jamiya.value) * 100}%` }}
                            ></div>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            نسبة العائد: %{((jamiya.returns / jamiya.value) * 100).toFixed(1)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {selectedReport === 'projections' && (
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">التوقعات المستقبلية</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-2">التوقع للشهر القادم</h4>
                      <p className="text-2xl font-bold text-blue-600">{formatCurrency(28500)}</p>
                      <p className="text-sm text-blue-700">عوائد متوقعة</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-medium text-green-900 mb-2">التوقع للربع القادم</h4>
                      <p className="text-2xl font-bold text-green-600">{formatCurrency(89200)}</p>
                      <p className="text-sm text-green-700">عوائد متوقعة</p>
                    </div>
                  </div>
                  <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
                    <h4 className="font-medium text-yellow-900 mb-2">توصيات الاستثمار</h4>
                    <ul className="text-sm text-yellow-800 space-y-1">
                      <li>• زيادة الاستثمار في جمعية الأحياء لارتفاع العوائد</li>
                      <li>• تنويع المحفظة بإضافة جمعيات جديدة</li>
                      <li>• مراجعة أداء جمعية العائلة</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
