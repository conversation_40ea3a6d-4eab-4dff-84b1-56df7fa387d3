import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { useApp } from '../../contexts/AppContext';
import { formatCurrency, getStatusColor, getStatusText } from '../../services';

export default function SubscriptionsScreen() {
  const { isAuthenticated } = useAuth();
  const { state, loadSubscriptions, refreshSubscriptions } = useApp();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (isAuthenticated && state.subscriptions.length === 0) {
      loadSubscriptions();
    }
  }, [isAuthenticated]);

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshSubscriptions();
    setRefreshing(false);
  };

  // Use fallback data if no real data is available
  const subscriptions = state.subscriptions.length > 0 ? state.subscriptions : [
    {
      id: '1',
      jamiyaId: '1',
      jamiyaName: 'جمعية الأصدقاء',
      shares: 2,
      pricePerShare: 2500,
      monthlyAmount: 5000,
      nextPaymentDate: '2024-07-01',
      status: 'active' as const,
      totalPaid: 30000,
      totalDue: 60000,
      joinDate: '2024-01-01',
    },
    {
      id: '2',
      jamiyaId: '2',
      jamiyaName: 'جمعية العائلة',
      shares: 1,
      pricePerShare: 3000,
      monthlyAmount: 3000,
      nextPaymentDate: '2024-06-25',
      status: 'pending' as const,
      totalPaid: 12000,
      totalDue: 36000,
      joinDate: '2024-02-01',
    },
    {
      id: '3',
      jamiyaId: '3',
      jamiyaName: 'جمعية الأحياء',
      shares: 4,
      pricePerShare: 500,
      monthlyAmount: 2000,
      nextPaymentDate: '2024-06-20',
      status: 'overdue' as const,
      totalPaid: 8000,
      totalDue: 24000,
      joinDate: '2024-03-01',
    },
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#10B981';
      case 'pending':
        return '#F59E0B';
      case 'overdue':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'pending':
        return 'معلق';
      case 'overdue':
        return 'متأخر';
      default:
        return status;
    }
  };

  const handlePayNow = (subscription: Subscription) => {
    Alert.alert(
      'دفع المساهمة',
      `هل تريد دفع مساهمة ${subscription.jamiyaName}؟\nالمبلغ: ${formatCurrency(subscription.monthlyAmount)}`,
      [
        { text: 'إلغاء', style: 'cancel' },
        { text: 'دفع الآن', onPress: () => Alert.alert('نجح الدفع', 'تم دفع المساهمة بنجاح') }
      ]
    );
  };

  const renderSubscriptionCard = ({ item }: { item: Subscription }) => (
    <View style={styles.subscriptionCard}>
      <View style={styles.cardHeader}>
        <View>
          <Text style={styles.jamiyaName}>{item.jamiyaName}</Text>
          <Text style={styles.sharesInfo}>
            {item.shares} سهم × {formatCurrency(item.pricePerShare)}
          </Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>

      <View style={styles.cardContent}>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>المبلغ الشهري:</Text>
          <Text style={styles.infoValue}>{formatCurrency(item.monthlyAmount)}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>الدفعة القادمة:</Text>
          <Text style={styles.infoValue}>
            {new Date(item.nextPaymentDate).toLocaleDateString('ar-SA')}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>إجمالي مدفوع:</Text>
          <Text style={styles.infoValue}>{formatCurrency(item.totalPaid)}</Text>
        </View>
      </View>

      <View style={styles.cardActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.payButton]}
          onPress={() => handlePayNow(item)}
        >
          <Ionicons name="card-outline" size={16} color="white" />
          <Text style={styles.payButtonText}>ادفع الآن</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.actionButton, styles.detailsButton]}>
          <Ionicons name="eye-outline" size={16} color="#3B82F6" />
          <Text style={styles.detailsButtonText}>التفاصيل</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const totalShares = subscriptions.reduce((sum, sub) => sum + sub.shares, 0);
  const totalMonthly = subscriptions.reduce((sum, sub) => sum + sub.monthlyAmount, 0);
  const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active').length;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>اشتراكاتي</Text>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Summary Stats */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryValue}>{activeSubscriptions}</Text>
          <Text style={styles.summaryLabel}>اشتراكات نشطة</Text>
        </View>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryValue}>{totalShares}</Text>
          <Text style={styles.summaryLabel}>إجمالي الأسهم</Text>
        </View>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryValue}>{formatCurrency(totalMonthly)}</Text>
          <Text style={styles.summaryLabel}>إجمالي شهري</Text>
        </View>
      </View>

      {/* Subscriptions List */}
      <FlatList
        data={subscriptions}
        renderItem={renderSubscriptionCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#3B82F6',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryContainer: {
    flexDirection: 'row',
    padding: 20,
    justifyContent: 'space-between',
  },
  summaryCard: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  listContainer: {
    padding: 20,
  },
  subscriptionCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  jamiyaName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  sharesInfo: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardContent: {
    marginBottom: 15,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 5,
  },
  payButton: {
    backgroundColor: '#10B981',
  },
  payButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  detailsButton: {
    backgroundColor: '#EFF6FF',
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  detailsButtonText: {
    color: '#3B82F6',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});
