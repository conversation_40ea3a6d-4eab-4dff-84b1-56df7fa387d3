# 🐳 Docker Configuration for Aljameia Platform

This document provides comprehensive instructions for running the Aljameia platform using Docker containers.

## 📋 Overview

The Aljameia platform is containerized using Docker with the following services:

- **MongoDB** - Primary database
- **Redis** - Caching and session storage
- **Backend API** - Node.js/Express application
- **Admin Dashboard** - React-based admin interface
- **Nginx** - Reverse proxy and load balancer
- **Monitoring** - Prometheus, Grafana, ELK stack (optional)

## 🚀 Quick Start

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM
- 10GB free disk space

### Development Environment

1. **Clone the repository**
```bash
git clone <repository-url>
cd aljameia
```

2. **Set up environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start development environment**
```bash
# Using the helper script
chmod +x scripts/docker-dev.sh
./scripts/docker-dev.sh start

# Or using docker-compose directly
docker-compose up -d
```

4. **Access the application**
- API: http://localhost:3000
- Admin Dashboard: http://localhost:3001
- Nginx: http://localhost:80
- MongoDB: localhost:27017
- Redis: localhost:6379

## 🏗 Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │  Admin Dashboard │    │   Web Client    │
│   (React Native)│    │     (React)     │    │     (React)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │      Nginx      │
                    │ (Reverse Proxy) │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Backend API   │
                    │ (Node.js/Express)│
                    └─────────────────┘
                                 │
                ┌────────────────┼────────────────┐
                │                │                │
       ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
       │     MongoDB     │ │      Redis      │ │   File Storage  │
       │   (Database)    │ │     (Cache)     │ │   (AWS S3/Local)│
       └─────────────────┘ └─────────────────┘ └─────────────────┘
```

## 🔧 Configuration

### Environment Files

- `.env.example` - Template with all available options
- `.env` - Development configuration
- `.env.production` - Production configuration

### Key Configuration Sections

#### Database
```env
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=secure_password
MONGO_DATABASE=aljameia
REDIS_PASSWORD=redis_password
```

#### Security
```env
JWT_SECRET=your-jwt-secret-key
JWT_REFRESH_SECRET=your-refresh-secret-key
ENCRYPTION_KEY=your-32-character-encryption-key
```

#### Payment Gateway
```env
PAYMENT_GATEWAY_URL=https://api.payment-gateway.com
PAYMENT_GATEWAY_KEY=your-gateway-key
PAYMENT_GATEWAY_SECRET=your-gateway-secret
```

## 🛠 Development Commands

### Using Helper Scripts

```bash
# Start development environment
./scripts/docker-dev.sh start

# Stop environment
./scripts/docker-dev.sh stop

# Restart environment
./scripts/docker-dev.sh restart

# View logs
./scripts/docker-dev.sh logs [service]

# Run database migrations
./scripts/docker-dev.sh migrate

# Seed database with sample data
./scripts/docker-dev.sh seed

# Run tests
./scripts/docker-dev.sh test

# Access backend shell
./scripts/docker-dev.sh shell

# Monitor services
./scripts/docker-dev.sh monitor

# Create database backup
./scripts/docker-dev.sh backup

# Clean up resources
./scripts/docker-dev.sh cleanup
```

### Using Docker Compose Directly

```bash
# Start all services
docker-compose up -d

# Start specific service
docker-compose up -d mongodb redis

# View logs
docker-compose logs -f [service]

# Execute commands in containers
docker-compose exec backend npm run migrate
docker-compose exec mongodb mongosh

# Scale services
docker-compose up -d --scale backend=3

# Stop services
docker-compose down

# Remove volumes (⚠️ Data loss!)
docker-compose down -v
```

## 🚀 Production Deployment

### Prerequisites

- Production server with Docker
- SSL certificates
- Domain name configured
- Production environment variables

### Deployment Steps

1. **Prepare production environment**
```bash
cp .env.production .env
# Update .env with production values
```

2. **Build production images**
```bash
./scripts/docker-prod.sh build
```

3. **Deploy to production**
```bash
./scripts/docker-prod.sh deploy
```

4. **Monitor deployment**
```bash
./scripts/docker-prod.sh monitor
```

### Production Commands

```bash
# Build and push images
./scripts/docker-prod.sh build
./scripts/docker-prod.sh push

# Deploy application
./scripts/docker-prod.sh deploy

# Rolling update
./scripts/docker-prod.sh update

# Rollback to previous version
./scripts/docker-prod.sh rollback v1.2.3

# Scale services
./scripts/docker-prod.sh scale backend 3

# Create backup
./scripts/docker-prod.sh backup

# View logs
./scripts/docker-prod.sh logs backend 100

# Security scan
./scripts/docker-prod.sh security
```

## 📊 Monitoring

### Built-in Monitoring Stack

The platform includes optional monitoring services:

- **Prometheus** - Metrics collection (http://localhost:9090)
- **Grafana** - Dashboards and visualization (http://localhost:3002)
- **ELK Stack** - Log aggregation and analysis
  - Elasticsearch (http://localhost:9200)
  - Kibana (http://localhost:5601)

### Enable Monitoring

```bash
# Start with monitoring profile
docker-compose --profile monitoring up -d

# Or start specific monitoring services
docker-compose up -d prometheus grafana
```

### Health Checks

All services include health checks:

```bash
# Check service health
docker-compose ps

# Manual health check
curl http://localhost/health
curl http://localhost:3000/api/health
```

## 🔒 Security

### Security Features

- **Container Security**: Non-root users, minimal base images
- **Network Security**: Isolated networks, no unnecessary port exposure
- **Data Security**: Encrypted volumes, secure secrets management
- **Access Control**: Authentication required for all admin interfaces

### Security Best Practices

1. **Change default passwords** in production
2. **Use SSL certificates** for HTTPS
3. **Enable firewall** on production servers
4. **Regular security updates** for base images
5. **Monitor security logs** and alerts

### Security Scanning

```bash
# Scan images for vulnerabilities
./scripts/docker-prod.sh security

# Manual scan with Trivy
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image aljameia/aljameia-backend:latest
```

## 💾 Backup & Recovery

### Automated Backups

Backups are created automatically using the backup service:

```bash
# Manual backup
./scripts/docker-dev.sh backup

# Production backup
./scripts/docker-prod.sh backup
```

### Backup Contents

- MongoDB database dump
- Redis data snapshot
- Uploaded files
- Configuration files

### Recovery Process

```bash
# Restore from backup
./scripts/docker-dev.sh restore /path/to/backup

# Manual MongoDB restore
docker-compose exec mongodb mongorestore \
  --host localhost:27017 \
  --db aljameia \
  --username admin \
  --password password123 \
  --authenticationDatabase admin \
  /path/to/backup
```

## 🐛 Troubleshooting

### Common Issues

#### Services won't start
```bash
# Check Docker daemon
docker info

# Check logs
docker-compose logs [service]

# Check disk space
df -h

# Check memory usage
free -h
```

#### Database connection issues
```bash
# Check MongoDB status
docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')"

# Check Redis status
docker-compose exec redis redis-cli ping
```

#### Permission issues
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh
```

#### Port conflicts
```bash
# Check port usage
netstat -tulpn | grep :3000

# Change ports in .env file
BACKEND_PORT=3001
```

### Performance Issues

#### High memory usage
```bash
# Check container stats
docker stats

# Limit container memory
# Add to docker-compose.yml:
deploy:
  resources:
    limits:
      memory: 1G
```

#### Slow database queries
```bash
# Enable MongoDB profiling
docker-compose exec mongodb mongosh
db.setProfilingLevel(2)
db.system.profile.find().sort({ts: -1}).limit(5)
```

## 📚 Additional Resources

### Documentation
- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [MongoDB Docker Hub](https://hub.docker.com/_/mongo)
- [Redis Docker Hub](https://hub.docker.com/_/redis)

### Monitoring
- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [ELK Stack Documentation](https://www.elastic.co/guide/)

### Security
- [Docker Security Best Practices](https://docs.docker.com/engine/security/)
- [Container Security Guide](https://kubernetes.io/docs/concepts/security/)

## 🤝 Support

For issues and questions:

1. Check the troubleshooting section above
2. Review Docker and application logs
3. Search existing issues in the repository
4. Create a new issue with detailed information

---

**Happy Dockerizing! 🐳**
