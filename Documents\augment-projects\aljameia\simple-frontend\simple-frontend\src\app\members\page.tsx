'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  UsersIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  UserCircleIcon,
  PhoneIcon,
  EnvelopeIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import type { Member, MemberStatus, MembershipType } from '@/types/member';

export default function MembersPage() {
  const { user } = useAuth();
  const [members, setMembers] = useState<Member[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<MemberStatus | 'all'>('all');
  const [selectedType, setSelectedType] = useState<MembershipType | 'all'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  const membersPerPage = 10;

  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const mockMembers: Member[] = [
    {
      id: '1',
      membershipNumber: 'AJ2024001',
      personalInfo: {
        firstName: 'أحمد',
        lastName: 'الراشد',
        fullNameArabic: 'أحمد محمد الراشد',
        fullNameEnglish: 'Ahmed Mohammed Al-Rashid',
        dateOfBirth: '1985-03-15',
        gender: 'male',
        nationality: 'سعودي',
        nationalId: '1234567890',
        maritalStatus: 'married',
        profession: 'مهندس برمجيات',
        employer: 'شركة التقنية المتقدمة',
        education: 'بكالوريوس هندسة حاسوب',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
      },
      contactInfo: {
        email: '<EMAIL>',
        phone: '+966501234567',
        whatsapp: '+966501234567',
        address: {
          street: 'شارع الملك فهد',
          city: 'الرياض',
          state: 'الرياض',
          postalCode: '12345',
          country: 'السعودية'
        }
      },
      membershipInfo: {
        type: 'premium',
        category: 'individual',
        joinDate: '2024-01-15',
        renewalDate: '2025-01-15',
        expiryDate: '2025-01-15',
        isActive: true,
        benefits: ['خصم 20%', 'دعوات خاصة', 'استشارات مجانية'],
        restrictions: []
      },
      emergencyContact: {
        name: 'فاطمة الراشد',
        relationship: 'زوجة',
        phone: '+966507654321',
        email: '<EMAIL>'
      },
      documents: [],
      payments: [],
      activities: [],
      status: 'active',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-06-18T15:30:00Z',
      createdBy: 'admin',
      lastModifiedBy: 'admin'
    },
    {
      id: '2',
      membershipNumber: 'AJ2024002',
      personalInfo: {
        firstName: 'سارة',
        lastName: 'العتيبي',
        fullNameArabic: 'سارة عبدالله العتيبي',
        dateOfBirth: '1990-07-22',
        gender: 'female',
        nationality: 'سعودي',
        nationalId: '2345678901',
        maritalStatus: 'single',
        profession: 'طبيبة',
        employer: 'مستشفى الملك فيصل',
        education: 'دكتوراه طب'
      },
      contactInfo: {
        email: '<EMAIL>',
        phone: '+966502345678',
        address: {
          street: 'شارع العليا',
          city: 'الرياض',
          state: 'الرياض',
          postalCode: '12346',
          country: 'السعودية'
        }
      },
      membershipInfo: {
        type: 'basic',
        category: 'individual',
        joinDate: '2024-02-01',
        renewalDate: '2025-02-01',
        expiryDate: '2025-02-01',
        isActive: true,
        benefits: ['خصم 10%'],
        restrictions: []
      },
      emergencyContact: {
        name: 'عبدالله العتيبي',
        relationship: 'والد',
        phone: '+966503456789'
      },
      documents: [],
      payments: [],
      activities: [],
      status: 'active',
      createdAt: '2024-02-01T09:00:00Z',
      updatedAt: '2024-06-18T14:20:00Z',
      createdBy: 'admin',
      lastModifiedBy: 'admin'
    },
    {
      id: '3',
      membershipNumber: 'AJ2024003',
      personalInfo: {
        firstName: 'محمد',
        lastName: 'الغامدي',
        fullNameArabic: 'محمد سعد الغامدي',
        dateOfBirth: '1978-11-10',
        gender: 'male',
        nationality: 'سعودي',
        nationalId: '3456789012',
        maritalStatus: 'married',
        profession: 'محاسب',
        employer: 'شركة المحاسبة المتقدمة',
        education: 'ماجستير محاسبة'
      },
      contactInfo: {
        email: '<EMAIL>',
        phone: '+966503456789',
        address: {
          street: 'شارع الأمير سلطان',
          city: 'جدة',
          state: 'مكة المكرمة',
          postalCode: '21234',
          country: 'السعودية'
        }
      },
      membershipInfo: {
        type: 'vip',
        category: 'individual',
        joinDate: '2024-01-01',
        renewalDate: '2025-01-01',
        expiryDate: '2025-01-01',
        isActive: false,
        benefits: ['خصم 30%', 'دعوات VIP', 'استشارات مجانية', 'خدمة عملاء مخصصة'],
        restrictions: []
      },
      emergencyContact: {
        name: 'نورا الغامدي',
        relationship: 'زوجة',
        phone: '+966504567890'
      },
      documents: [],
      payments: [],
      activities: [],
      status: 'suspended',
      createdAt: '2024-01-01T08:00:00Z',
      updatedAt: '2024-06-10T11:15:00Z',
      createdBy: 'admin',
      lastModifiedBy: 'admin'
    }
  ];

  useEffect(() => {
    // محاكاة تحميل البيانات
    const loadMembers = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMembers(mockMembers);
      setIsLoading(false);
    };

    loadMembers();
  }, []);

  // تصفية الأعضاء
  const filteredMembers = members.filter(member => {
    const matchesSearch = searchTerm === '' || 
      member.personalInfo.fullNameArabic.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.membershipNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.contactInfo.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = selectedStatus === 'all' || member.status === selectedStatus;
    const matchesType = selectedType === 'all' || member.membershipInfo.type === selectedType;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  // تقسيم الصفحات
  const totalPages = Math.ceil(filteredMembers.length / membersPerPage);
  const startIndex = (currentPage - 1) * membersPerPage;
  const paginatedMembers = filteredMembers.slice(startIndex, startIndex + membersPerPage);

  const getStatusIcon = (status: MemberStatus) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'inactive':
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
      case 'suspended':
        return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'expired':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: MemberStatus) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'suspended': return 'موقوف';
      case 'pending': return 'في الانتظار';
      case 'expired': return 'منتهي الصلاحية';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getMembershipTypeText = (type: MembershipType) => {
    switch (type) {
      case 'basic': return 'أساسية';
      case 'premium': return 'مميزة';
      case 'vip': return 'VIP';
      case 'honorary': return 'فخرية';
      case 'student': return 'طلابية';
      case 'corporate': return 'مؤسسية';
      default: return type;
    }
  };

  const getMembershipTypeColor = (type: MembershipType) => {
    switch (type) {
      case 'basic': return 'bg-gray-100 text-gray-800';
      case 'premium': return 'bg-blue-100 text-blue-800';
      case 'vip': return 'bg-purple-100 text-purple-800';
      case 'honorary': return 'bg-yellow-100 text-yellow-800';
      case 'student': return 'bg-green-100 text-green-800';
      case 'corporate': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['members']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل بيانات الأعضاء...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['members']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">الجمعية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/dashboard">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                    لوحة التحكم
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <UsersIcon className="h-8 w-8 text-blue-600 ml-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">إدارة الأعضاء</h1>
                  <p className="text-gray-600">إدارة وتتبع جميع أعضاء الجمعية</p>
                </div>
              </div>
              
              <Link href="/members/add">
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                  <PlusIcon className="h-5 w-5 ml-2" />
                  إضافة عضو جديد
                </button>
              </Link>
            </div>
          </motion.div>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
          >
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    placeholder="البحث بالاسم أو رقم العضوية أو البريد الإلكتروني..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex gap-4">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value as MemberStatus | 'all')}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="active">نشط</option>
                  <option value="inactive">غير نشط</option>
                  <option value="suspended">موقوف</option>
                  <option value="pending">في الانتظار</option>
                  <option value="expired">منتهي الصلاحية</option>
                </select>

                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value as MembershipType | 'all')}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">جميع الأنواع</option>
                  <option value="basic">أساسية</option>
                  <option value="premium">مميزة</option>
                  <option value="vip">VIP</option>
                  <option value="honorary">فخرية</option>
                  <option value="student">طلابية</option>
                  <option value="corporate">مؤسسية</option>
                </select>

                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center"
                >
                  <FunnelIcon className="h-5 w-5 ml-2" />
                  فلاتر متقدمة
                </button>
              </div>
            </div>

            {/* Results Summary */}
            <div className="mt-4 text-sm text-gray-600">
              عرض {paginatedMembers.length} من أصل {filteredMembers.length} عضو
            </div>
          </motion.div>

          {/* Members Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          >
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      العضو
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      رقم العضوية
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      نوع العضوية
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      تاريخ الانضمام
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedMembers.map((member) => (
                    <tr key={member.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {member.personalInfo.avatar ? (
                              <img
                                className="h-10 w-10 rounded-full"
                                src={member.personalInfo.avatar}
                                alt={member.personalInfo.fullNameArabic}
                              />
                            ) : (
                              <UserCircleIcon className="h-10 w-10 text-gray-400" />
                            )}
                          </div>
                          <div className="mr-4">
                            <div className="text-sm font-medium text-gray-900">
                              {member.personalInfo.fullNameArabic}
                            </div>
                            <div className="text-sm text-gray-500 flex items-center">
                              <EnvelopeIcon className="h-4 w-4 ml-1" />
                              {member.contactInfo.email}
                            </div>
                            <div className="text-sm text-gray-500 flex items-center">
                              <PhoneIcon className="h-4 w-4 ml-1" />
                              {member.contactInfo.phone}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {member.membershipNumber}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getMembershipTypeColor(member.membershipInfo.type)}`}>
                          {getMembershipTypeText(member.membershipInfo.type)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(member.status)}
                          <span className="mr-2 text-sm text-gray-900">
                            {getStatusText(member.status)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          <CalendarIcon className="h-4 w-4 ml-1" />
                          {new Date(member.membershipInfo.joinDate).toLocaleDateString('ar-SA')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Link href={`/members/${member.id}`}>
                            <button className="text-blue-600 hover:text-blue-900 p-1">
                              <EyeIcon className="h-4 w-4" />
                            </button>
                          </Link>
                          <Link href={`/members/${member.id}/edit`}>
                            <button className="text-green-600 hover:text-green-900 p-1">
                              <PencilIcon className="h-4 w-4" />
                            </button>
                          </Link>
                          <button className="text-red-600 hover:text-red-900 p-1">
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    السابق
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="mr-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    التالي
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      عرض{' '}
                      <span className="font-medium">{startIndex + 1}</span>
                      {' '}إلى{' '}
                      <span className="font-medium">
                        {Math.min(startIndex + membersPerPage, filteredMembers.length)}
                      </span>
                      {' '}من{' '}
                      <span className="font-medium">{filteredMembers.length}</span>
                      {' '}نتيجة
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        السابق
                      </button>
                      
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            page === currentPage
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      ))}
                      
                      <button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        التالي
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
