# ==============================================
# Makefile for Aljameia Platform
# ==============================================

# Variables
COMPOSE_FILE_DEV = docker-compose.yml -f docker-compose.dev.yml
COMPOSE_FILE_PROD = docker-compose.yml -f docker-compose.prod.yml
PROJECT_NAME = aljameia

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

# Default target
.DEFAULT_GOAL := help

# ==============================================
# Help
# ==============================================
.PHONY: help
help: ## Show this help message
	@echo "$(BLUE)Aljameia Platform - Available Commands$(NC)"
	@echo "======================================"
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make $(YELLOW)<target>$(NC)\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2 } /^##@/ { printf "\n$(BLUE)%s$(NC)\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Development Commands

.PHONY: dev-setup
dev-setup: ## Setup development environment
	@echo "$(BLUE)Setting up development environment...$(NC)"
	@cp .env.example .env
	@echo "$(GREEN)✓ Environment file created$(NC)"
	@echo "$(YELLOW)Please update .env file with your configuration$(NC)"

.PHONY: dev-up
dev-up: ## Start development environment
	@echo "$(BLUE)Starting development environment...$(NC)"
	@docker-compose $(COMPOSE_FILE_DEV) up -d
	@echo "$(GREEN)✓ Development environment started$(NC)"
	@echo "$(YELLOW)Backend API: http://localhost:3000$(NC)"
	@echo "$(YELLOW)Admin Dashboard: http://localhost:3001$(NC)"
	@echo "$(YELLOW)MailHog: http://localhost:8025$(NC)"
	@echo "$(YELLOW)Redis Commander: http://localhost:8081$(NC)"
	@echo "$(YELLOW)Mongo Express: http://localhost:8082$(NC)"

.PHONY: dev-down
dev-down: ## Stop development environment
	@echo "$(BLUE)Stopping development environment...$(NC)"
	@docker-compose $(COMPOSE_FILE_DEV) down
	@echo "$(GREEN)✓ Development environment stopped$(NC)"

.PHONY: dev-logs
dev-logs: ## Show development logs
	@docker-compose $(COMPOSE_FILE_DEV) logs -f

.PHONY: dev-restart
dev-restart: ## Restart development environment
	@echo "$(BLUE)Restarting development environment...$(NC)"
	@docker-compose $(COMPOSE_FILE_DEV) restart
	@echo "$(GREEN)✓ Development environment restarted$(NC)"

##@ Production Commands

.PHONY: prod-build
prod-build: ## Build production images
	@echo "$(BLUE)Building production images...$(NC)"
	@docker-compose $(COMPOSE_FILE_PROD) build --no-cache
	@echo "$(GREEN)✓ Production images built$(NC)"

.PHONY: prod-up
prod-up: ## Start production environment
	@echo "$(BLUE)Starting production environment...$(NC)"
	@docker-compose $(COMPOSE_FILE_PROD) up -d
	@echo "$(GREEN)✓ Production environment started$(NC)"

.PHONY: prod-down
prod-down: ## Stop production environment
	@echo "$(BLUE)Stopping production environment...$(NC)"
	@docker-compose $(COMPOSE_FILE_PROD) down
	@echo "$(GREEN)✓ Production environment stopped$(NC)"

.PHONY: prod-logs
prod-logs: ## Show production logs
	@docker-compose $(COMPOSE_FILE_PROD) logs -f

.PHONY: prod-restart
prod-restart: ## Restart production environment
	@echo "$(BLUE)Restarting production environment...$(NC)"
	@docker-compose $(COMPOSE_FILE_PROD) restart
	@echo "$(GREEN)✓ Production environment restarted$(NC)"

##@ Database Commands

.PHONY: db-seed
db-seed: ## Seed database with sample data
	@echo "$(BLUE)Seeding database...$(NC)"
	@docker-compose exec mongodb mongosh --eval "load('/docker-entrypoint-initdb.d/02-seed-data.js')"
	@echo "$(GREEN)✓ Database seeded$(NC)"

.PHONY: db-backup
db-backup: ## Create database backup
	@echo "$(BLUE)Creating database backup...$(NC)"
	@docker-compose exec backup /usr/local/bin/backup.sh
	@echo "$(GREEN)✓ Database backup created$(NC)"

.PHONY: db-restore
db-restore: ## Restore database from backup (requires BACKUP_FILE variable)
	@echo "$(BLUE)Restoring database from backup...$(NC)"
	@if [ -z "$(BACKUP_FILE)" ]; then \
		echo "$(RED)Error: BACKUP_FILE variable is required$(NC)"; \
		echo "$(YELLOW)Usage: make db-restore BACKUP_FILE=backup_file.tar.gz$(NC)"; \
		exit 1; \
	fi
	@docker-compose exec backup /usr/local/bin/restore.sh $(BACKUP_FILE)
	@echo "$(GREEN)✓ Database restored$(NC)"

.PHONY: db-reset
db-reset: ## Reset database (WARNING: This will delete all data)
	@echo "$(RED)WARNING: This will delete all data!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	@echo "$(BLUE)Resetting database...$(NC)"
	@docker-compose exec mongodb mongosh --eval "db.dropDatabase()"
	@make db-seed
	@echo "$(GREEN)✓ Database reset$(NC)"

##@ Testing Commands

.PHONY: test
test: ## Run all tests
	@echo "$(BLUE)Running tests...$(NC)"
	@docker-compose exec backend npm test
	@docker-compose exec admin-dashboard npm test
	@echo "$(GREEN)✓ Tests completed$(NC)"

.PHONY: test-backend
test-backend: ## Run backend tests
	@echo "$(BLUE)Running backend tests...$(NC)"
	@docker-compose exec backend npm test
	@echo "$(GREEN)✓ Backend tests completed$(NC)"

.PHONY: test-admin
test-admin: ## Run admin dashboard tests
	@echo "$(BLUE)Running admin dashboard tests...$(NC)"
	@docker-compose exec admin-dashboard npm test
	@echo "$(GREEN)✓ Admin dashboard tests completed$(NC)"

.PHONY: test-coverage
test-coverage: ## Run tests with coverage
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	@docker-compose exec backend npm run test:coverage
	@docker-compose exec admin-dashboard npm run test:coverage
	@echo "$(GREEN)✓ Test coverage completed$(NC)"

##@ Maintenance Commands

.PHONY: clean
clean: ## Clean up Docker resources
	@echo "$(BLUE)Cleaning up Docker resources...$(NC)"
	@docker system prune -f
	@docker volume prune -f
	@echo "$(GREEN)✓ Docker resources cleaned$(NC)"

.PHONY: clean-all
clean-all: ## Clean up all Docker resources (including volumes)
	@echo "$(RED)WARNING: This will delete all volumes and data!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	@echo "$(BLUE)Cleaning up all Docker resources...$(NC)"
	@docker-compose $(COMPOSE_FILE_DEV) down -v
	@docker-compose $(COMPOSE_FILE_PROD) down -v
	@docker system prune -af
	@docker volume prune -f
	@echo "$(GREEN)✓ All Docker resources cleaned$(NC)"

.PHONY: update
update: ## Update all Docker images
	@echo "$(BLUE)Updating Docker images...$(NC)"
	@docker-compose $(COMPOSE_FILE_DEV) pull
	@docker-compose $(COMPOSE_FILE_PROD) pull
	@echo "$(GREEN)✓ Docker images updated$(NC)"

##@ Monitoring Commands

.PHONY: status
status: ## Show service status
	@echo "$(BLUE)Service Status:$(NC)"
	@docker-compose ps

.PHONY: health
health: ## Check service health
	@echo "$(BLUE)Health Check:$(NC)"
	@docker-compose exec backend curl -f http://localhost:3000/health || echo "$(RED)Backend: Unhealthy$(NC)"
	@docker-compose exec admin-dashboard curl -f http://localhost:3001/api/health || echo "$(RED)Admin: Unhealthy$(NC)"
	@docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')" || echo "$(RED)MongoDB: Unhealthy$(NC)"
	@docker-compose exec redis redis-cli ping || echo "$(RED)Redis: Unhealthy$(NC)"

.PHONY: logs-backend
logs-backend: ## Show backend logs
	@docker-compose logs -f backend

.PHONY: logs-admin
logs-admin: ## Show admin dashboard logs
	@docker-compose logs -f admin-dashboard

.PHONY: logs-nginx
logs-nginx: ## Show nginx logs
	@docker-compose logs -f nginx

.PHONY: logs-db
logs-db: ## Show database logs
	@docker-compose logs -f mongodb redis

##@ SSL Commands

.PHONY: ssl-generate
ssl-generate: ## Generate self-signed SSL certificates
	@echo "$(BLUE)Generating SSL certificates...$(NC)"
	@mkdir -p ./nginx/ssl
	@openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
		-keyout ./nginx/ssl/private.key \
		-out ./nginx/ssl/cert.pem \
		-subj "/C=SA/ST=Riyadh/L=Riyadh/O=Aljameia/OU=IT/CN=localhost"
	@cp ./nginx/ssl/cert.pem ./nginx/ssl/chain.pem
	@echo "$(GREEN)✓ SSL certificates generated$(NC)"

.PHONY: ssl-renew
ssl-renew: ## Renew Let's Encrypt certificates
	@echo "$(BLUE)Renewing SSL certificates...$(NC)"
	@docker-compose exec nginx /usr/local/bin/renew-ssl.sh
	@echo "$(GREEN)✓ SSL certificates renewed$(NC)"

##@ Development Tools

.PHONY: shell-backend
shell-backend: ## Open shell in backend container
	@docker-compose exec backend /bin/bash

.PHONY: shell-admin
shell-admin: ## Open shell in admin dashboard container
	@docker-compose exec admin-dashboard /bin/bash

.PHONY: shell-db
shell-db: ## Open MongoDB shell
	@docker-compose exec mongodb mongosh

.PHONY: shell-redis
shell-redis: ## Open Redis CLI
	@docker-compose exec redis redis-cli

.PHONY: install-backend
install-backend: ## Install backend dependencies
	@echo "$(BLUE)Installing backend dependencies...$(NC)"
	@docker-compose exec backend npm install
	@echo "$(GREEN)✓ Backend dependencies installed$(NC)"

.PHONY: install-admin
install-admin: ## Install admin dashboard dependencies
	@echo "$(BLUE)Installing admin dashboard dependencies...$(NC)"
	@docker-compose exec admin-dashboard npm install
	@echo "$(GREEN)✓ Admin dashboard dependencies installed$(NC)"

##@ Deployment Commands

.PHONY: deploy-staging
deploy-staging: ## Deploy to staging environment
	@echo "$(BLUE)Deploying to staging...$(NC)"
	@echo "$(YELLOW)This would typically involve CI/CD pipeline$(NC)"
	@echo "$(GREEN)✓ Staging deployment initiated$(NC)"

.PHONY: deploy-production
deploy-production: ## Deploy to production environment
	@echo "$(BLUE)Deploying to production...$(NC)"
	@echo "$(YELLOW)This would typically involve CI/CD pipeline$(NC)"
	@echo "$(GREEN)✓ Production deployment initiated$(NC)"

##@ Quick Commands

.PHONY: quick-start
quick-start: dev-setup ssl-generate dev-up ## Quick start development environment
	@echo "$(GREEN)✓ Development environment is ready!$(NC)"

.PHONY: quick-reset
quick-reset: dev-down clean dev-up ## Quick reset development environment
	@echo "$(GREEN)✓ Development environment reset!$(NC)"
