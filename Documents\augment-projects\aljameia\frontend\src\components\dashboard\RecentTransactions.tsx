import { useTranslation } from 'next-i18next';
import Link from 'next/link';
import { 
  ArrowUpIcon, 
  ArrowDownIcon, 
  EyeIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface Transaction {
  id: string;
  type: 'income' | 'expense';
  category: string;
  description: string;
  amount: number;
  status: 'completed' | 'pending' | 'failed';
  date: string;
  member?: {
    name: string;
    id: string;
  };
  paymentMethod: string;
}

const RecentTransactions = () => {
  const { t } = useTranslation('dashboard');

  const transactions: Transaction[] = [
    {
      id: 'TXN-001',
      type: 'income',
      category: 'Membership Fee',
      description: 'Annual membership renewal',
      amount: 2500,
      status: 'completed',
      date: '2024-06-18',
      member: { name: '<PERSON>', id: 'MEM-001' },
      paymentMethod: 'Bank Transfer'
    },
    {
      id: 'TXN-002',
      type: 'expense',
      category: 'Office Supplies',
      description: 'Stationery and printing materials',
      amount: 850,
      status: 'completed',
      date: '2024-06-17',
      paymentMethod: 'Credit Card'
    },
    {
      id: 'TXN-003',
      type: 'income',
      category: 'Event Registration',
      description: 'Annual conference registration fee',
      amount: 1200,
      status: 'pending',
      date: '2024-06-17',
      member: { name: 'Fatima Al-Zahra', id: 'MEM-002' },
      paymentMethod: 'Digital Wallet'
    },
    {
      id: 'TXN-004',
      type: 'income',
      category: 'Donation',
      description: 'Charitable contribution',
      amount: 5000,
      status: 'completed',
      date: '2024-06-16',
      member: { name: 'Mohammed Al-Saud', id: 'MEM-003' },
      paymentMethod: 'Bank Transfer'
    },
    {
      id: 'TXN-005',
      type: 'expense',
      category: 'Utilities',
      description: 'Monthly electricity bill',
      amount: 1500,
      status: 'failed',
      date: '2024-06-16',
      paymentMethod: 'Auto-debit'
    },
    {
      id: 'TXN-006',
      type: 'income',
      category: 'Membership Fee',
      description: 'Monthly membership fee',
      amount: 500,
      status: 'completed',
      date: '2024-06-15',
      member: { name: 'Sara Al-Mansouri', id: 'MEM-004' },
      paymentMethod: 'Credit Card'
    }
  ];

  const getStatusIcon = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'failed':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusText = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return t('transactions.status.completed');
      case 'pending':
        return t('transactions.status.pending');
      case 'failed':
        return t('transactions.status.failed');
    }
  };

  const getStatusColor = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'failed':
        return 'text-red-600 bg-red-50';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{t('transactions.recent_transactions')}</CardTitle>
          <Link href="/financial/transactions">
            <Button variant="outline" size="sm">
              {t('transactions.view_all')}
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <div
              key={transaction.id}
              className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <div className="flex items-center space-x-4">
                {/* Transaction Type Icon */}
                <div className={`p-2 rounded-full ${
                  transaction.type === 'income' 
                    ? 'bg-green-100' 
                    : 'bg-red-100'
                }`}>
                  {transaction.type === 'income' ? (
                    <ArrowDownIcon className="h-4 w-4 text-green-600" />
                  ) : (
                    <ArrowUpIcon className="h-4 w-4 text-red-600" />
                  )}
                </div>

                {/* Transaction Details */}
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium text-gray-900">
                      {transaction.description}
                    </h4>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                      {getStatusIcon(transaction.status)}
                      <span className="ml-1">{getStatusText(transaction.status)}</span>
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                    <span>{transaction.category}</span>
                    <span>•</span>
                    <span>{transaction.paymentMethod}</span>
                    {transaction.member && (
                      <>
                        <span>•</span>
                        <span>{transaction.member.name}</span>
                      </>
                    )}
                  </div>
                  
                  <p className="text-xs text-gray-400 mt-1">
                    {new Date(transaction.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>

              {/* Amount and Actions */}
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <p className={`font-semibold ${
                    transaction.type === 'income' 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    {transaction.type === 'income' ? '+' : '-'}₹ {transaction.amount.toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500">
                    {transaction.id}
                  </p>
                </div>
                
                <Button variant="ghost" size="sm">
                  <EyeIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-green-600">
                +₹ {transactions
                  .filter(t => t.type === 'income' && t.status === 'completed')
                  .reduce((sum, t) => sum + t.amount, 0)
                  .toLocaleString()}
              </p>
              <p className="text-sm text-gray-500">{t('transactions.total_income')}</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-red-600">
                -₹ {transactions
                  .filter(t => t.type === 'expense' && t.status === 'completed')
                  .reduce((sum, t) => sum + t.amount, 0)
                  .toLocaleString()}
              </p>
              <p className="text-sm text-gray-500">{t('transactions.total_expenses')}</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-600">
                ₹ {(
                  transactions.filter(t => t.type === 'income' && t.status === 'completed').reduce((sum, t) => sum + t.amount, 0) -
                  transactions.filter(t => t.type === 'expense' && t.status === 'completed').reduce((sum, t) => sum + t.amount, 0)
                ).toLocaleString()}
              </p>
              <p className="text-sm text-gray-500">{t('transactions.net_balance')}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentTransactions;
