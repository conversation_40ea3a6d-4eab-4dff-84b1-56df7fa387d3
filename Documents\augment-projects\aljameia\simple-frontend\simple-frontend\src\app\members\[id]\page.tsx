'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  UserIcon,
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  CalendarIcon,
  IdentificationIcon,
  BriefcaseIcon,
  AcademicCapIcon,
  HeartIcon,
  DocumentTextIcon,
  CreditCardIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';
import type { Member, MemberStatus, MembershipType } from '@/types/member';

export default function MemberDetailsPage() {
  const { user } = useAuth();
  const params = useParams();
  const memberId = params.id as string;
  const [member, setMember] = useState<Member | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const mockMember: Member = {
    id: '1',
    membershipNumber: 'AJ2024001',
    personalInfo: {
      firstName: 'أحمد',
      lastName: 'الراشد',
      fullNameArabic: 'أحمد محمد الراشد',
      fullNameEnglish: 'Ahmed Mohammed Al-Rashid',
      dateOfBirth: '1985-03-15',
      gender: 'male',
      nationality: 'سعودي',
      nationalId: '1234567890',
      maritalStatus: 'married',
      profession: 'مهندس برمجيات',
      employer: 'شركة التقنية المتقدمة',
      education: 'بكالوريوس هندسة حاسوب',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
    },
    contactInfo: {
      email: '<EMAIL>',
      phone: '+966501234567',
      alternativePhone: '+966507654321',
      whatsapp: '+966501234567',
      address: {
        street: 'شارع الملك فهد، حي العليا',
        city: 'الرياض',
        state: 'الرياض',
        postalCode: '12345',
        country: 'السعودية'
      },
      socialMedia: {
        twitter: '@ahmed_rashid',
        linkedin: 'ahmed-al-rashid',
        instagram: 'ahmed.rashid'
      }
    },
    membershipInfo: {
      type: 'premium',
      category: 'individual',
      joinDate: '2024-01-15',
      renewalDate: '2025-01-15',
      expiryDate: '2025-01-15',
      isActive: true,
      benefits: ['خصم 20%', 'دعوات خاصة', 'استشارات مجانية'],
      restrictions: [],
      referredBy: 'محمد العتيبي'
    },
    emergencyContact: {
      name: 'فاطمة الراشد',
      relationship: 'زوجة',
      phone: '+966507654321',
      email: '<EMAIL>',
      address: 'نفس عنوان العضو'
    },
    documents: [
      {
        id: '1',
        type: 'national_id',
        name: 'صورة الهوية الوطنية',
        url: '/documents/national_id_1.pdf',
        uploadDate: '2024-01-15',
        isVerified: true,
        verifiedBy: 'admin',
        verificationDate: '2024-01-16',
        size: 1024000,
        mimeType: 'application/pdf'
      },
      {
        id: '2',
        type: 'photo',
        name: 'الصورة الشخصية',
        url: '/documents/photo_1.jpg',
        uploadDate: '2024-01-15',
        isVerified: true,
        verifiedBy: 'admin',
        verificationDate: '2024-01-16',
        size: 512000,
        mimeType: 'image/jpeg'
      }
    ],
    payments: [
      {
        id: '1',
        type: 'membership_fee',
        amount: 500,
        currency: 'SAR',
        date: '2024-01-15',
        status: 'completed',
        method: 'bank_transfer',
        reference: 'PAY-2024-001',
        description: 'رسوم العضوية السنوية 2024',
        receiptUrl: '/receipts/receipt_1.pdf'
      },
      {
        id: '2',
        type: 'event_fee',
        amount: 150,
        currency: 'SAR',
        date: '2024-03-10',
        dueDate: '2024-03-15',
        status: 'pending',
        method: 'online',
        reference: 'PAY-2024-045',
        description: 'رسوم حضور المؤتمر السنوي'
      }
    ],
    activities: [
      {
        id: '1',
        type: 'meeting',
        title: 'الاجتماع الشهري',
        description: 'اجتماع شهري لمناقشة أنشطة الجمعية',
        date: '2024-06-15',
        location: 'قاعة المؤتمرات الرئيسية',
        status: 'completed',
        attendanceStatus: 'attended',
        rating: 5
      },
      {
        id: '2',
        type: 'workshop',
        title: 'ورشة تطوير المهارات',
        description: 'ورشة عمل لتطوير المهارات المهنية',
        date: '2024-07-20',
        location: 'قاعة التدريب',
        status: 'upcoming',
        attendanceStatus: 'registered'
      }
    ],
    status: 'active',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-06-18T15:30:00Z',
    createdBy: 'admin',
    lastModifiedBy: 'admin'
  };

  useEffect(() => {
    const loadMember = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // في التطبيق الحقيقي، سيتم جلب البيانات من API باستخدام memberId
      if (memberId === '1') {
        setMember(mockMember);
      }
      
      setIsLoading(false);
    };

    loadMember();
  }, [memberId]);

  const getStatusIcon = (status: MemberStatus) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'inactive':
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
      case 'suspended':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'expired':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: MemberStatus) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'suspended': return 'موقوف';
      case 'pending': return 'في الانتظار';
      case 'expired': return 'منتهي الصلاحية';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getMembershipTypeText = (type: MembershipType) => {
    switch (type) {
      case 'basic': return 'أساسية';
      case 'premium': return 'مميزة';
      case 'vip': return 'VIP';
      case 'honorary': return 'فخرية';
      case 'student': return 'طلابية';
      case 'corporate': return 'مؤسسية';
      default: return type;
    }
  };

  const getMembershipTypeColor = (type: MembershipType) => {
    switch (type) {
      case 'basic': return 'bg-gray-100 text-gray-800';
      case 'premium': return 'bg-blue-100 text-blue-800';
      case 'vip': return 'bg-purple-100 text-purple-800';
      case 'honorary': return 'bg-yellow-100 text-yellow-800';
      case 'student': return 'bg-green-100 text-green-800';
      case 'corporate': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  const tabs = [
    { id: 'overview', label: 'نظرة عامة', icon: UserIcon },
    { id: 'documents', label: 'الوثائق', icon: DocumentTextIcon },
    { id: 'payments', label: 'المدفوعات', icon: CreditCardIcon },
    { id: 'activities', label: 'الأنشطة', icon: CalendarIcon }
  ];

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['members']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل بيانات العضو...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!member) {
    return (
      <ProtectedRoute requiredPermissions={['members']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <UserCircleIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">العضو غير موجود</h2>
            <p className="text-gray-600 mb-4">لم يتم العثور على العضو المطلوب</p>
            <Link href="/members">
              <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                العودة لقائمة الأعضاء
              </button>
            </Link>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['members']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">الجمعية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/members">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    العودة للأعضاء
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Member Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {member.personalInfo.avatar ? (
                    <img
                      className="h-20 w-20 rounded-full"
                      src={member.personalInfo.avatar}
                      alt={member.personalInfo.fullNameArabic}
                    />
                  ) : (
                    <UserCircleIcon className="h-20 w-20 text-gray-400" />
                  )}
                </div>
                <div className="mr-6">
                  <h1 className="text-2xl font-bold text-gray-900">
                    {member.personalInfo.fullNameArabic}
                  </h1>
                  {member.personalInfo.fullNameEnglish && (
                    <p className="text-lg text-gray-600">
                      {member.personalInfo.fullNameEnglish}
                    </p>
                  )}
                  <div className="flex items-center mt-2 space-x-4 space-x-reverse">
                    <div className="flex items-center">
                      <IdentificationIcon className="h-4 w-4 text-gray-400 ml-1" />
                      <span className="text-sm text-gray-600">{member.membershipNumber}</span>
                    </div>
                    <div className="flex items-center">
                      {getStatusIcon(member.status)}
                      <span className="mr-1 text-sm text-gray-600">
                        {getStatusText(member.status)}
                      </span>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getMembershipTypeColor(member.membershipInfo.type)}`}>
                      {getMembershipTypeText(member.membershipInfo.type)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <Link href={`/members/${member.id}/edit`}>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                    <PencilIcon className="h-4 w-4 ml-2" />
                    تعديل
                  </button>
                </Link>
                <button className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center">
                  <TrashIcon className="h-4 w-4 ml-2" />
                  حذف
                </button>
              </div>
            </div>
          </motion.div>

          {/* Quick Info Cards */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
              <UserIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="text-lg font-semibold text-gray-900">المعلومات الشخصية</h3>
              <p className="text-sm text-gray-600 mt-1">
                {calculateAge(member.personalInfo.dateOfBirth)} سنة • {member.personalInfo.profession}
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
              <CalendarIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="text-lg font-semibold text-gray-900">مدة العضوية</h3>
              <p className="text-sm text-gray-600 mt-1">
                منذ {new Date(member.membershipInfo.joinDate).toLocaleDateString('ar-SA')}
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
              <PhoneIcon className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="text-lg font-semibold text-gray-900">الاتصال</h3>
              <p className="text-sm text-gray-600 mt-1">
                {member.contactInfo.phone}
              </p>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات مفصلة</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">معلومات الاتصال</h4>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <EnvelopeIcon className="h-4 w-4 text-gray-400 ml-2" />
                    <a href={`mailto:${member.contactInfo.email}`} className="text-blue-600 hover:text-blue-800">
                      {member.contactInfo.email}
                    </a>
                  </div>
                  <div className="flex items-center">
                    <PhoneIcon className="h-4 w-4 text-gray-400 ml-2" />
                    <a href={`tel:${member.contactInfo.phone}`} className="text-blue-600 hover:text-blue-800">
                      {member.contactInfo.phone}
                    </a>
                  </div>
                  <div className="flex items-start">
                    <MapPinIcon className="h-4 w-4 text-gray-400 ml-2 mt-1" />
                    <span className="text-gray-700">
                      {member.contactInfo.address.city}, {member.contactInfo.address.country}
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">معلومات العضوية</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">نوع العضوية:</span>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getMembershipTypeColor(member.membershipInfo.type)}`}>
                      {getMembershipTypeText(member.membershipInfo.type)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">الحالة:</span>
                    <div className="flex items-center">
                      {getStatusIcon(member.status)}
                      <span className="mr-1 text-sm">
                        {getStatusText(member.status)}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">تاريخ الانتهاء:</span>
                    <span className="text-gray-900">
                      {new Date(member.membershipInfo.expiryDate).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
