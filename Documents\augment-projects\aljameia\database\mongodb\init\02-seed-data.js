// ==============================================
// MongoDB Sample Data Seeding Script
// This script inserts sample data for development and testing
// ==============================================

// Switch to the aljameia database
db = db.getSiblingDB('aljameia');

print('🌱 Starting sample data seeding...');

// ==============================================
// Sample Users
// ==============================================

// Admin user
const adminUser = {
  email: '<EMAIL>',
  name: 'مدير النظام',
  password: '$2b$12$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5u', // hashed: admin123
  role: 'admin',
  phone: '+966501234567',
  isVerified: true,
  isActive: true,
  avatar: null,
  dateOfBirth: new Date('1985-01-15'),
  nationalId: '1234567890',
  address: {
    street: 'شارع الملك فهد',
    city: 'الرياض',
    state: 'الرياض',
    country: 'المملكة العربية السعودية',
    postalCode: '12345'
  },
  preferences: {
    language: 'ar',
    currency: 'SAR',
    notifications: {
      email: true,
      sms: true,
      push: true
    },
    theme: 'light'
  },
  twoFactorAuth: {
    enabled: false
  },
  loginAttempts: 0,
  lastLogin: new Date(),
  createdAt: new Date(),
  updatedAt: new Date()
};

const adminResult = db.users.insertOne(adminUser);
print('✅ Created admin user');

// Moderator user
const moderatorUser = {
  email: '<EMAIL>',
  name: 'أحمد المشرف',
  password: '$2b$12$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5u', // hashed: moderator123
  role: 'moderator',
  phone: '+966502345678',
  isVerified: true,
  isActive: true,
  preferences: {
    language: 'ar',
    currency: 'SAR',
    notifications: {
      email: true,
      sms: true,
      push: true
    },
    theme: 'light'
  },
  twoFactorAuth: {
    enabled: false
  },
  loginAttempts: 0,
  createdAt: new Date(),
  updatedAt: new Date()
};

const moderatorResult = db.users.insertOne(moderatorUser);
print('✅ Created moderator user');

// Sample regular users
const sampleUsers = [
  {
    email: '<EMAIL>',
    name: 'محمد علي',
    password: '$2b$12$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5u', // hashed: user123
    role: 'user',
    phone: '+966503456789',
    isVerified: true,
    isActive: true,
    preferences: {
      language: 'ar',
      currency: 'SAR',
      notifications: { email: true, sms: true, push: true },
      theme: 'light'
    },
    twoFactorAuth: { enabled: false },
    loginAttempts: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    email: '<EMAIL>',
    name: 'فاطمة حسن',
    password: '$2b$12$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5u',
    role: 'user',
    phone: '+966504567890',
    isVerified: true,
    isActive: true,
    preferences: {
      language: 'ar',
      currency: 'SAR',
      notifications: { email: true, sms: false, push: true },
      theme: 'dark'
    },
    twoFactorAuth: { enabled: false },
    loginAttempts: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    email: '<EMAIL>',
    name: 'عمر سالم',
    password: '$2b$12$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5u',
    role: 'user',
    phone: '+966505678901',
    isVerified: true,
    isActive: true,
    preferences: {
      language: 'ar',
      currency: 'SAR',
      notifications: { email: true, sms: true, push: false },
      theme: 'light'
    },
    twoFactorAuth: { enabled: false },
    loginAttempts: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const usersResult = db.users.insertMany(sampleUsers);
print(`✅ Created ${usersResult.insertedIds.length} sample users`);

// ==============================================
// Sample Jamiyas
// ==============================================

const sampleJamiyas = [
  {
    name: 'جمعية الأصدقاء',
    description: 'جمعية مالية للأصدقاء والزملاء في العمل',
    totalAmount: 60000,
    monthlyAmount: 2500,
    duration: 24,
    maxMembers: 24,
    currentMembers: 3,
    status: 'active',
    startDate: new Date(),
    endDate: new Date(Date.now() + 24 * 30 * 24 * 60 * 60 * 1000), // 24 months
    createdBy: adminResult.insertedId,
    rules: {
      latePaymentFee: 50,
      gracePeriodDays: 3,
      earlyWithdrawalPenalty: 0.05,
      minimumCreditScore: 600
    },
    paymentSchedule: {
      dayOfMonth: 1,
      reminderDays: 3
    },
    categories: ['savings', 'investment'],
    tags: ['أصدقاء', 'ادخار'],
    isPrivate: false,
    inviteCode: 'FRIENDS24',
    statistics: {
      totalCollected: 7500,
      totalPaid: 0,
      successfulPayments: 3,
      failedPayments: 0,
      averagePaymentTime: 2.5
    },
    settings: {
      autoApproveMembers: false,
      requireDocuments: true,
      allowEarlyWithdrawal: true,
      enableNotifications: true
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'جمعية العائلة',
    description: 'جمعية مالية لأفراد العائلة الكريمة',
    totalAmount: 120000,
    monthlyAmount: 5000,
    duration: 24,
    maxMembers: 24,
    currentMembers: 1,
    status: 'active',
    startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // starts in 7 days
    endDate: new Date(Date.now() + (24 * 30 + 7) * 24 * 60 * 60 * 1000),
    createdBy: moderatorResult.insertedId,
    rules: {
      latePaymentFee: 100,
      gracePeriodDays: 5,
      earlyWithdrawalPenalty: 0.03,
      minimumCreditScore: 650
    },
    paymentSchedule: {
      dayOfMonth: 15,
      reminderDays: 5
    },
    categories: ['emergency', 'housing'],
    tags: ['عائلة', 'طوارئ'],
    isPrivate: true,
    inviteCode: 'FAMILY24',
    statistics: {
      totalCollected: 0,
      totalPaid: 0,
      successfulPayments: 0,
      failedPayments: 0,
      averagePaymentTime: 0
    },
    settings: {
      autoApproveMembers: true,
      requireDocuments: false,
      allowEarlyWithdrawal: false,
      enableNotifications: true
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'جمعية الاستثمار',
    description: 'جمعية مالية للاستثمار في الأسهم والعقارات',
    totalAmount: 240000,
    monthlyAmount: 10000,
    duration: 24,
    maxMembers: 24,
    currentMembers: 0,
    status: 'draft',
    startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // starts in 30 days
    endDate: new Date(Date.now() + (24 * 30 + 30) * 24 * 60 * 60 * 1000),
    createdBy: adminResult.insertedId,
    rules: {
      latePaymentFee: 200,
      gracePeriodDays: 2,
      earlyWithdrawalPenalty: 0.1,
      minimumCreditScore: 700
    },
    paymentSchedule: {
      dayOfMonth: 5,
      reminderDays: 2
    },
    categories: ['investment', 'business'],
    tags: ['استثمار', 'أسهم', 'عقارات'],
    isPrivate: false,
    statistics: {
      totalCollected: 0,
      totalPaid: 0,
      successfulPayments: 0,
      failedPayments: 0,
      averagePaymentTime: 0
    },
    settings: {
      autoApproveMembers: false,
      requireDocuments: true,
      allowEarlyWithdrawal: false,
      enableNotifications: true
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const jamiyasResult = db.jamiyas.insertMany(sampleJamiyas);
print(`✅ Created ${jamiyasResult.insertedIds.length} sample jamiyas`);

// ==============================================
// Sample Subscriptions
// ==============================================

const jamiyaIds = Object.values(jamiyasResult.insertedIds);
const userIds = Object.values(usersResult.insertedIds);

const sampleSubscriptions = [
  {
    userId: userIds[0],
    jamiyaId: jamiyaIds[0],
    shares: 1,
    monthlyAmount: 2500,
    totalAmount: 60000,
    status: 'active',
    joinedAt: new Date(),
    nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    position: 1,
    settings: {
      autoPayment: false,
      reminderDays: 3,
      preferredPaymentMethod: 'mada'
    },
    statistics: {
      totalPaid: 2500,
      paymentsCount: 1,
      missedPayments: 0,
      latePayments: 0,
      averagePaymentDelay: 0
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    userId: userIds[1],
    jamiyaId: jamiyaIds[0],
    shares: 1,
    monthlyAmount: 2500,
    totalAmount: 60000,
    status: 'active',
    joinedAt: new Date(),
    nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    position: 2,
    settings: {
      autoPayment: true,
      reminderDays: 5,
      preferredPaymentMethod: 'stc_pay'
    },
    statistics: {
      totalPaid: 2500,
      paymentsCount: 1,
      missedPayments: 0,
      latePayments: 0,
      averagePaymentDelay: 0
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    userId: userIds[2],
    jamiyaId: jamiyaIds[0],
    shares: 1,
    monthlyAmount: 2500,
    totalAmount: 60000,
    status: 'active',
    joinedAt: new Date(),
    nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    position: 3,
    settings: {
      autoPayment: false,
      reminderDays: 3,
      preferredPaymentMethod: 'bank_transfer'
    },
    statistics: {
      totalPaid: 2500,
      paymentsCount: 1,
      missedPayments: 0,
      latePayments: 0,
      averagePaymentDelay: 0
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const subscriptionsResult = db.subscriptions.insertMany(sampleSubscriptions);
print(`✅ Created ${subscriptionsResult.insertedIds.length} sample subscriptions`);

// ==============================================
// Sample Payments
// ==============================================

const subscriptionIds = Object.values(subscriptionsResult.insertedIds);

const samplePayments = [
  {
    userId: userIds[0],
    subscriptionId: subscriptionIds[0],
    amount: 2500,
    currency: 'SAR',
    method: 'mada',
    status: 'completed',
    transactionId: 'TXN_001_' + Date.now(),
    description: 'دفعة شهرية - جمعية الأصدقاء',
    metadata: {
      jamiyaId: jamiyaIds[0],
      jamiyaName: 'جمعية الأصدقاء',
      paymentMonth: 'يناير',
      paymentYear: 2024,
      isLatePayment: false,
      lateFee: 0
    },
    paymentDetails: {
      cardLast4: '1234',
      cardBrand: 'mada'
    },
    fees: {
      processingFee: 10,
      lateFee: 0,
      totalFees: 10
    },
    attempts: [{
      attemptNumber: 1,
      attemptDate: new Date(),
      status: 'completed'
    }],
    completedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    userId: userIds[1],
    subscriptionId: subscriptionIds[1],
    amount: 2500,
    currency: 'SAR',
    method: 'stc_pay',
    status: 'completed',
    transactionId: 'TXN_002_' + Date.now(),
    description: 'دفعة شهرية - جمعية الأصدقاء',
    metadata: {
      jamiyaId: jamiyaIds[0],
      jamiyaName: 'جمعية الأصدقاء',
      paymentMonth: 'يناير',
      paymentYear: 2024,
      isLatePayment: false,
      lateFee: 0
    },
    fees: {
      processingFee: 37.5,
      lateFee: 0,
      totalFees: 37.5
    },
    attempts: [{
      attemptNumber: 1,
      attemptDate: new Date(),
      status: 'completed'
    }],
    completedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    userId: userIds[2],
    subscriptionId: subscriptionIds[2],
    amount: 2500,
    currency: 'SAR',
    method: 'bank_transfer',
    status: 'completed',
    transactionId: 'TXN_003_' + Date.now(),
    description: 'دفعة شهرية - جمعية الأصدقاء',
    metadata: {
      jamiyaId: jamiyaIds[0],
      jamiyaName: 'جمعية الأصدقاء',
      paymentMonth: 'يناير',
      paymentYear: 2024,
      isLatePayment: false,
      lateFee: 0
    },
    paymentDetails: {
      bankName: 'البنك الأهلي السعودي',
      referenceNumber: 'REF123456789'
    },
    fees: {
      processingFee: 5,
      lateFee: 0,
      totalFees: 5
    },
    attempts: [{
      attemptNumber: 1,
      attemptDate: new Date(),
      status: 'completed'
    }],
    completedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const paymentsResult = db.payments.insertMany(samplePayments);
print(`✅ Created ${paymentsResult.insertedIds.length} sample payments`);

// ==============================================
// Sample Notifications
// ==============================================

const sampleNotifications = [
  {
    userId: userIds[0],
    type: 'payment',
    title: 'تم استلام الدفعة بنجاح',
    message: 'تم استلام دفعتك الشهرية لجمعية الأصدقاء بمبلغ 2500 ريال',
    isRead: false,
    data: {
      paymentId: Object.values(paymentsResult.insertedIds)[0],
      amount: 2500,
      jamiyaName: 'جمعية الأصدقاء'
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    userId: userIds[1],
    type: 'jamiya',
    title: 'مرحباً بك في الجمعية',
    message: 'تم قبولك في جمعية الأصدقاء. موعد الدفعة القادمة هو 1 فبراير',
    isRead: true,
    data: {
      jamiyaId: jamiyaIds[0],
      jamiyaName: 'جمعية الأصدقاء'
    },
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    updatedAt: new Date()
  },
  {
    userId: adminResult.insertedId,
    type: 'system',
    title: 'تقرير يومي',
    message: 'تم معالجة 3 مدفوعات اليوم بإجمالي 7500 ريال',
    isRead: false,
    data: {
      totalPayments: 3,
      totalAmount: 7500,
      date: new Date().toISOString().split('T')[0]
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const notificationsResult = db.notifications.insertMany(sampleNotifications);
print(`✅ Created ${notificationsResult.insertedIds.length} sample notifications`);

print('🎉 Sample data seeding completed successfully!');
print(`📊 Created:
  - ${1 + 1 + usersResult.insertedIds.length} users (admin, moderator, ${usersResult.insertedIds.length} regular)
  - ${jamiyasResult.insertedIds.length} jamiyas
  - ${subscriptionsResult.insertedIds.length} subscriptions
  - ${paymentsResult.insertedIds.length} payments
  - ${notificationsResult.insertedIds.length} notifications`);

print('🔐 Login credentials:');
print('  Admin: <EMAIL> / admin123');
print('  Moderator: <EMAIL> / moderator123');
print('  Users: <EMAIL> / user123 (and others)');
