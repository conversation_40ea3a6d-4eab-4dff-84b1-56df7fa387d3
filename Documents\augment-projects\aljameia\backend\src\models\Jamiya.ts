import mongoose, { Document, Schema } from 'mongoose';

// Jamiya interface
export interface IJamiya extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  totalAmount: number;
  monthlyAmount: number;
  duration: number; // in months
  maxMembers: number;
  currentMembers: number;
  status: 'draft' | 'active' | 'full' | 'completed' | 'cancelled';
  startDate: Date;
  endDate: Date;
  createdBy: mongoose.Types.ObjectId;
  rules: {
    latePaymentFee: number;
    gracePeriodDays: number;
    earlyWithdrawalPenalty: number;
    minimumCreditScore?: number;
  };
  paymentSchedule: {
    dayOfMonth: number; // 1-28
    reminderDays: number; // days before payment due
  };
  categories: string[];
  tags: string[];
  isPrivate: boolean;
  inviteCode?: string;
  documents: {
    name: string;
    url: string;
    type: string;
    uploadedAt: Date;
  }[];
  statistics: {
    totalCollected: number;
    totalPaid: number;
    successfulPayments: number;
    failedPayments: number;
    averagePaymentTime: number;
  };
  settings: {
    autoApproveMembers: boolean;
    requireDocuments: boolean;
    allowEarlyWithdrawal: boolean;
    enableNotifications: boolean;
  };
  createdAt: Date;
  updatedAt: Date;

  // Methods
  calculateMonthlyAmount(): number;
  isEligibleForJoin(userId: mongoose.Types.ObjectId): Promise<boolean>;
  addMember(userId: mongoose.Types.ObjectId): Promise<void>;
  removeMember(userId: mongoose.Types.ObjectId): Promise<void>;
  updateStatistics(): Promise<void>;
  generateInviteCode(): string;
}

// Jamiya schema
const jamiyaSchema = new Schema<IJamiya>({
  name: {
    type: String,
    required: [true, 'Jamiya name is required'],
    trim: true,
    minlength: [3, 'Name must be at least 3 characters'],
    maxlength: [100, 'Name cannot exceed 100 characters'],
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters'],
  },
  totalAmount: {
    type: Number,
    required: [true, 'Total amount is required'],
    min: [1000, 'Total amount must be at least 1000'],
    max: [10000000, 'Total amount cannot exceed 10,000,000'],
  },
  monthlyAmount: {
    type: Number,
    required: [true, 'Monthly amount is required'],
    min: [100, 'Monthly amount must be at least 100'],
  },
  duration: {
    type: Number,
    required: [true, 'Duration is required'],
    min: [6, 'Duration must be at least 6 months'],
    max: [60, 'Duration cannot exceed 60 months'],
  },
  maxMembers: {
    type: Number,
    required: [true, 'Maximum members is required'],
    min: [5, 'Must have at least 5 members'],
    max: [100, 'Cannot exceed 100 members'],
  },
  currentMembers: {
    type: Number,
    default: 0,
    min: [0, 'Current members cannot be negative'],
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'full', 'completed', 'cancelled'],
    default: 'draft',
  },
  startDate: {
    type: Date,
    required: [true, 'Start date is required'],
    validate: {
      validator: function(value: Date) {
        return value >= new Date();
      },
      message: 'Start date cannot be in the past',
    },
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required'],
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Creator is required'],
  },
  rules: {
    latePaymentFee: {
      type: Number,
      default: 50,
      min: [0, 'Late payment fee cannot be negative'],
    },
    gracePeriodDays: {
      type: Number,
      default: 3,
      min: [0, 'Grace period cannot be negative'],
      max: [30, 'Grace period cannot exceed 30 days'],
    },
    earlyWithdrawalPenalty: {
      type: Number,
      default: 0.05, // 5%
      min: [0, 'Penalty cannot be negative'],
      max: [0.5, 'Penalty cannot exceed 50%'],
    },
    minimumCreditScore: {
      type: Number,
      min: [300, 'Credit score cannot be below 300'],
      max: [850, 'Credit score cannot exceed 850'],
    },
  },
  paymentSchedule: {
    dayOfMonth: {
      type: Number,
      required: [true, 'Payment day is required'],
      min: [1, 'Payment day must be between 1-28'],
      max: [28, 'Payment day must be between 1-28'],
    },
    reminderDays: {
      type: Number,
      default: 3,
      min: [0, 'Reminder days cannot be negative'],
      max: [15, 'Reminder days cannot exceed 15'],
    },
  },
  categories: [{
    type: String,
    trim: true,
    enum: ['savings', 'investment', 'emergency', 'education', 'housing', 'business', 'travel', 'other'],
  }],
  tags: [{
    type: String,
    trim: true,
    maxlength: [20, 'Tag cannot exceed 20 characters'],
  }],
  isPrivate: {
    type: Boolean,
    default: false,
  },
  inviteCode: {
    type: String,
    unique: true,
    sparse: true,
    match: [/^[A-Z0-9]{8}$/, 'Invite code must be 8 alphanumeric characters'],
  },
  documents: [{
    name: {
      type: String,
      required: true,
      trim: true,
    },
    url: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
      enum: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
    },
    uploadedAt: {
      type: Date,
      default: Date.now,
    },
  }],
  statistics: {
    totalCollected: { type: Number, default: 0 },
    totalPaid: { type: Number, default: 0 },
    successfulPayments: { type: Number, default: 0 },
    failedPayments: { type: Number, default: 0 },
    averagePaymentTime: { type: Number, default: 0 },
  },
  settings: {
    autoApproveMembers: { type: Boolean, default: false },
    requireDocuments: { type: Boolean, default: false },
    allowEarlyWithdrawal: { type: Boolean, default: true },
    enableNotifications: { type: Boolean, default: true },
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
jamiyaSchema.index({ name: 1 });
jamiyaSchema.index({ status: 1 });
jamiyaSchema.index({ createdBy: 1 });
jamiyaSchema.index({ startDate: 1 });
jamiyaSchema.index({ endDate: 1 });
jamiyaSchema.index({ totalAmount: 1 });
jamiyaSchema.index({ monthlyAmount: 1 });
jamiyaSchema.index({ categories: 1 });
jamiyaSchema.index({ tags: 1 });
jamiyaSchema.index({ inviteCode: 1 }, { unique: true, sparse: true });
jamiyaSchema.index({ createdAt: 1 });

// Virtual for completion percentage
jamiyaSchema.virtual('completionPercentage').get(function() {
  if (this.maxMembers === 0) return 0;
  return Math.round((this.currentMembers / this.maxMembers) * 100);
});

// Virtual for remaining spots
jamiyaSchema.virtual('remainingSpots').get(function() {
  return Math.max(0, this.maxMembers - this.currentMembers);
});

// Virtual for is full
jamiyaSchema.virtual('isFull').get(function() {
  return this.currentMembers >= this.maxMembers;
});

// Pre-save middleware to calculate end date
jamiyaSchema.pre('save', function(next) {
  if (this.isModified('startDate') || this.isModified('duration')) {
    const endDate = new Date(this.startDate);
    endDate.setMonth(endDate.getMonth() + this.duration);
    this.endDate = endDate;
  }
  
  // Validate monthly amount vs total amount
  if (this.isModified('monthlyAmount') || this.isModified('totalAmount') || this.isModified('maxMembers')) {
    const calculatedMonthly = this.totalAmount / this.maxMembers;
    if (Math.abs(this.monthlyAmount - calculatedMonthly) > 1) {
      return next(new Error('Monthly amount must equal total amount divided by max members'));
    }
  }
  
  next();
});

// Instance method to calculate monthly amount
jamiyaSchema.methods.calculateMonthlyAmount = function(): number {
  return Math.round(this.totalAmount / this.maxMembers);
};

// Instance method to check eligibility
jamiyaSchema.methods.isEligibleForJoin = async function(userId: mongoose.Types.ObjectId): Promise<boolean> {
  // Check if jamiya is active and not full
  if (this.status !== 'active' || this.isFull) {
    return false;
  }
  
  // Check if user is already a member
  const Subscription = mongoose.model('Subscription');
  const existingSubscription = await Subscription.findOne({
    userId,
    jamiyaId: this._id,
    status: { $in: ['active', 'pending'] }
  });
  
  return !existingSubscription;
};

// Instance method to add member
jamiyaSchema.methods.addMember = async function(userId: mongoose.Types.ObjectId): Promise<void> {
  if (this.currentMembers >= this.maxMembers) {
    throw new Error('Jamiya is full');
  }
  
  this.currentMembers += 1;
  
  // Update status to full if max members reached
  if (this.currentMembers >= this.maxMembers) {
    this.status = 'full';
  }
  
  await this.save();
};

// Instance method to remove member
jamiyaSchema.methods.removeMember = async function(userId: mongoose.Types.ObjectId): Promise<void> {
  if (this.currentMembers > 0) {
    this.currentMembers -= 1;
    
    // Update status back to active if was full
    if (this.status === 'full') {
      this.status = 'active';
    }
    
    await this.save();
  }
};

// Instance method to update statistics
jamiyaSchema.methods.updateStatistics = async function(): Promise<void> {
  const Payment = mongoose.model('Payment');
  
  const stats = await Payment.aggregate([
    {
      $lookup: {
        from: 'subscriptions',
        localField: 'subscriptionId',
        foreignField: '_id',
        as: 'subscription'
      }
    },
    {
      $unwind: '$subscription'
    },
    {
      $match: {
        'subscription.jamiyaId': this._id
      }
    },
    {
      $group: {
        _id: null,
        totalCollected: {
          $sum: {
            $cond: [{ $eq: ['$status', 'completed'] }, '$amount', 0]
          }
        },
        successfulPayments: {
          $sum: {
            $cond: [{ $eq: ['$status', 'completed'] }, 1, 0]
          }
        },
        failedPayments: {
          $sum: {
            $cond: [{ $eq: ['$status', 'failed'] }, 1, 0]
          }
        }
      }
    }
  ]);
  
  if (stats.length > 0) {
    this.statistics = {
      ...this.statistics,
      totalCollected: stats[0].totalCollected || 0,
      successfulPayments: stats[0].successfulPayments || 0,
      failedPayments: stats[0].failedPayments || 0,
    };
    
    await this.save();
  }
};

// Instance method to generate invite code
jamiyaSchema.methods.generateInviteCode = function(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  this.inviteCode = result;
  return result;
};

// Static method to find active jamiyas
jamiyaSchema.statics.findActive = function() {
  return this.find({ status: 'active' });
};

// Static method to find by invite code
jamiyaSchema.statics.findByInviteCode = function(code: string) {
  return this.findOne({ inviteCode: code, status: 'active' });
};

// Create and export the model
export const Jamiya = mongoose.model<IJamiya>('Jamiya', jamiyaSchema);
export default Jamiya;
