$com.aljameia.app.AlJameiaApplication(com.aljameia.app.data.dao.TransactionDao!com.aljameia.app.data.dao.UserDao/com.aljameia.app.data.database.AlJameiaDatabase9com.aljameia.app.data.database.AlJameiaDatabase.Companion)com.aljameia.app.data.database.Converters'com.aljameia.app.data.entities.Document+com.aljameia.app.data.entities.DocumentType$com.aljameia.app.data.entities.Event(com.aljameia.app.data.entities.EventType*com.aljameia.app.data.entities.Transaction.com.aljameia.app.data.entities.TransactionType0com.aljameia.app.data.entities.TransactionStatus,com.aljameia.app.data.entities.PaymentMethod#com.aljameia.app.data.entities.User/com.aljameia.app.data.repository.UserRepositorycom.aljameia.app.di.AppModule'com.aljameia.app.receivers.BootReceiver*com.aljameia.app.receivers.NetworkReceiver$com.aljameia.app.services.FCMService%com.aljameia.app.services.SyncService%com.aljameia.app.ui.auth.AuthActivity/com.aljameia.app.ui.dashboard.DashboardActivity6com.aljameia.app.ui.dashboard.FinancialSummaryCardData-com.aljameia.app.ui.dashboard.QuickActionData1com.aljameia.app.ui.dashboard.TransactionItemData4com.aljameia.app.ui.documents.DocumentViewerActivity%com.aljameia.app.ui.main.MainActivity&com.aljameia.app.ui.main.MainViewModel$com.aljameia.app.ui.main.MainUiState,com.aljameia.app.ui.main.MainUiState.Loading,com.aljameia.app.ui.main.MainUiState.Success*com.aljameia.app.ui.main.MainUiState.Error+com.aljameia.app.ui.main.SimpleMainActivity+com.aljameia.app.ui.profile.ProfileActivity-com.aljameia.app.ui.settings.SettingsActivity(com.aljameia.app.ui.settings.SettingItem)com.aljameia.app.ui.splash.SplashActivity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                