import { authService } from '../../services/authService';
import { httpClient, tokenManager, userManager } from '../../services/api';

// Mock dependencies
jest.mock('../../services/api');
jest.mock('@react-native-async-storage/async-storage');

const mockHttpClient = httpClient as jest.Mocked<typeof httpClient>;
const mockTokenManager = tokenManager as jest.Mocked<typeof tokenManager>;
const mockUserManager = userManager as jest.Mocked<typeof userManager>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    const mockCredentials = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const mockAuthResponse = {
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        isVerified: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
    };

    it('should login successfully with valid credentials', async () => {
      mockHttpClient.post.mockResolvedValue({
        success: true,
        data: mockAuthResponse,
      });

      const result = await authService.login(mockCredentials);

      expect(mockHttpClient.post).toHaveBeenCalledWith('/auth/login', mockCredentials);
      expect(mockTokenManager.setTokens).toHaveBeenCalledWith(
        mockAuthResponse.accessToken,
        mockAuthResponse.refreshToken
      );
      expect(mockUserManager.setUserData).toHaveBeenCalledWith(mockAuthResponse.user);
      expect(result).toEqual(mockAuthResponse);
    });

    it('should throw error on login failure', async () => {
      mockHttpClient.post.mockResolvedValue({
        success: false,
        message: 'Invalid credentials',
      });

      await expect(authService.login(mockCredentials)).rejects.toThrow('Invalid credentials');
    });

    it('should handle network errors', async () => {
      mockHttpClient.post.mockRejectedValue(new Error('Network error'));

      await expect(authService.login(mockCredentials)).rejects.toThrow('Network error');
    });
  });

  describe('register', () => {
    const mockUserData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      phone: '0501234567',
    };

    const mockAuthResponse = {
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        phone: '0501234567',
        role: 'user',
        isVerified: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
    };

    it('should register successfully with valid data', async () => {
      mockHttpClient.post.mockResolvedValue({
        success: true,
        data: mockAuthResponse,
      });

      const result = await authService.register(mockUserData);

      expect(mockHttpClient.post).toHaveBeenCalledWith('/auth/register', mockUserData);
      expect(mockTokenManager.setTokens).toHaveBeenCalledWith(
        mockAuthResponse.accessToken,
        mockAuthResponse.refreshToken
      );
      expect(mockUserManager.setUserData).toHaveBeenCalledWith(mockAuthResponse.user);
      expect(result).toEqual(mockAuthResponse);
    });

    it('should throw error on registration failure', async () => {
      mockHttpClient.post.mockResolvedValue({
        success: false,
        message: 'Email already exists',
      });

      await expect(authService.register(mockUserData)).rejects.toThrow('Email already exists');
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      mockHttpClient.post.mockResolvedValue({ success: true });

      await authService.logout();

      expect(mockHttpClient.post).toHaveBeenCalledWith('/auth/logout');
      expect(mockTokenManager.clearTokens).toHaveBeenCalled();
      expect(mockUserManager.clearUserData).toHaveBeenCalled();
    });

    it('should clear local data even if server call fails', async () => {
      mockHttpClient.post.mockRejectedValue(new Error('Server error'));

      await authService.logout();

      expect(mockTokenManager.clearTokens).toHaveBeenCalled();
      expect(mockUserManager.clearUserData).toHaveBeenCalled();
    });
  });

  describe('getCurrentUser', () => {
    const mockUser = {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'user',
      isVerified: true,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    };

    it('should get current user successfully', async () => {
      mockHttpClient.get.mockResolvedValue({
        success: true,
        data: mockUser,
      });

      const result = await authService.getCurrentUser();

      expect(mockHttpClient.get).toHaveBeenCalledWith('/auth/me');
      expect(mockUserManager.setUserData).toHaveBeenCalledWith(mockUser);
      expect(result).toEqual(mockUser);
    });

    it('should throw error if user not found', async () => {
      mockHttpClient.get.mockResolvedValue({
        success: false,
        message: 'User not found',
      });

      await expect(authService.getCurrentUser()).rejects.toThrow('User not found');
    });
  });

  describe('updateProfile', () => {
    const mockUserData = {
      name: 'Updated Name',
      phone: '0509876543',
    };

    const mockUpdatedUser = {
      id: '1',
      name: 'Updated Name',
      email: '<EMAIL>',
      phone: '0509876543',
      role: 'user',
      isVerified: true,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-02',
    };

    it('should update profile successfully', async () => {
      mockHttpClient.put.mockResolvedValue({
        success: true,
        data: mockUpdatedUser,
      });

      const result = await authService.updateProfile(mockUserData);

      expect(mockHttpClient.put).toHaveBeenCalledWith('/auth/profile', mockUserData);
      expect(mockUserManager.setUserData).toHaveBeenCalledWith(mockUpdatedUser);
      expect(result).toEqual(mockUpdatedUser);
    });

    it('should throw error on update failure', async () => {
      mockHttpClient.put.mockResolvedValue({
        success: false,
        message: 'Update failed',
      });

      await expect(authService.updateProfile(mockUserData)).rejects.toThrow('Update failed');
    });
  });

  describe('changePassword', () => {
    const mockPasswordData = {
      currentPassword: 'oldpassword',
      newPassword: 'newpassword123',
    };

    it('should change password successfully', async () => {
      mockHttpClient.put.mockResolvedValue({ success: true });

      await authService.changePassword(mockPasswordData);

      expect(mockHttpClient.put).toHaveBeenCalledWith('/auth/change-password', mockPasswordData);
    });

    it('should throw error on password change failure', async () => {
      mockHttpClient.put.mockResolvedValue({
        success: false,
        message: 'Current password is incorrect',
      });

      await expect(authService.changePassword(mockPasswordData)).rejects.toThrow(
        'Current password is incorrect'
      );
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when token is valid', async () => {
      mockTokenManager.getAccessToken.mockResolvedValue('valid-token');
      mockHttpClient.get.mockResolvedValue({ success: true });

      const result = await authService.isAuthenticated();

      expect(result).toBe(true);
      expect(mockHttpClient.get).toHaveBeenCalledWith('/auth/verify-token');
    });

    it('should return false when no token exists', async () => {
      mockTokenManager.getAccessToken.mockResolvedValue(null);

      const result = await authService.isAuthenticated();

      expect(result).toBe(false);
      expect(mockHttpClient.get).not.toHaveBeenCalled();
    });

    it('should return false when token verification fails', async () => {
      mockTokenManager.getAccessToken.mockResolvedValue('invalid-token');
      mockHttpClient.get.mockResolvedValue({ success: false });

      const result = await authService.isAuthenticated();

      expect(result).toBe(false);
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      mockTokenManager.refreshAccessToken.mockResolvedValue('new-access-token');

      const result = await authService.refreshToken();

      expect(result).toBe('new-access-token');
    });

    it('should logout on refresh failure', async () => {
      mockTokenManager.refreshAccessToken.mockRejectedValue(new Error('Refresh failed'));
      const logoutSpy = jest.spyOn(authService, 'logout').mockResolvedValue();

      const result = await authService.refreshToken();

      expect(result).toBeNull();
      expect(logoutSpy).toHaveBeenCalled();
    });
  });
});
