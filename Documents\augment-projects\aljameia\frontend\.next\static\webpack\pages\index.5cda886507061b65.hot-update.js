"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/store/AuthContext.tsx":
/*!***********************************!*\
  !*** ./src/store/AuthContext.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   withAuth: function() { return /* binding */ withAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Initial state\nconst initialState = {\n    user: null,\n    member: null,\n    isAuthenticated: false,\n    isLoading: true,\n    error: null,\n    requiresTwoFactor: false,\n    tempToken: null\n};\n// Reducer\nfunction authReducer(state, action) {\n    switch(action.type){\n        case \"AUTH_START\":\n            return {\n                ...state,\n                isLoading: true,\n                error: null\n            };\n        case \"AUTH_SUCCESS\":\n            return {\n                ...state,\n                user: action.payload.user,\n                member: action.payload.member || null,\n                isAuthenticated: true,\n                isLoading: false,\n                error: null,\n                requiresTwoFactor: false,\n                tempToken: null\n            };\n        case \"AUTH_FAILURE\":\n            return {\n                ...state,\n                user: null,\n                member: null,\n                isAuthenticated: false,\n                isLoading: false,\n                error: action.payload,\n                requiresTwoFactor: false,\n                tempToken: null\n            };\n        case \"AUTH_LOGOUT\":\n            return {\n                ...initialState,\n                isLoading: false\n            };\n        case \"AUTH_CLEAR_ERROR\":\n            return {\n                ...state,\n                error: null\n            };\n        case \"AUTH_REQUIRE_2FA\":\n            return {\n                ...state,\n                isLoading: false,\n                requiresTwoFactor: true,\n                tempToken: action.payload.tempToken,\n                error: null\n            };\n        case \"AUTH_UPDATE_USER\":\n            return {\n                ...state,\n                user: action.payload\n            };\n        case \"AUTH_UPDATE_MEMBER\":\n            return {\n                ...state,\n                member: action.payload\n            };\n        default:\n            return state;\n    }\n}\n// Context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [state, dispatch] = useReducer(authReducer, initialState);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check authentication status on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    // Auto-refresh token\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (state.isAuthenticated) {\n            const interval = setInterval(()=>{\n                refreshToken().catch(()=>{\n                // Silent fail - will be handled by the next API call\n                });\n            }, 14 * 60 * 1000); // Refresh every 14 minutes\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        state.isAuthenticated\n    ]);\n    // Check authentication\n    const checkAuth = async ()=>{\n        try {\n            dispatch({\n                type: \"AUTH_START\"\n            });\n            const token = localStorage.getItem(\"accessToken\");\n            if (!token) {\n                dispatch({\n                    type: \"AUTH_LOGOUT\"\n                });\n                return;\n            }\n            const response = await authService.getProfile();\n            dispatch({\n                type: \"AUTH_SUCCESS\",\n                payload: {\n                    user: response.user,\n                    member: response.member\n                }\n            });\n        } catch (error) {\n            logger.error(\"Auth check failed:\", error);\n            localStorage.removeItem(\"accessToken\");\n            localStorage.removeItem(\"refreshToken\");\n            dispatch({\n                type: \"AUTH_LOGOUT\"\n            });\n        }\n    };\n    // Login\n    const login = async (email, password, twoFactorCode)=>{\n        try {\n            dispatch({\n                type: \"AUTH_START\"\n            });\n            const response = await authService.login({\n                email,\n                password,\n                twoFactorCode\n            });\n            if (response.requiresTwoFactor) {\n                dispatch({\n                    type: \"AUTH_REQUIRE_2FA\",\n                    payload: {\n                        tempToken: response.tempToken\n                    }\n                });\n                return;\n            }\n            // Store tokens\n            localStorage.setItem(\"accessToken\", response.accessToken);\n            localStorage.setItem(\"refreshToken\", response.refreshToken);\n            dispatch({\n                type: \"AUTH_SUCCESS\",\n                payload: {\n                    user: response.user,\n                    member: response.member\n                }\n            });\n            toast.success(\"تم تسجيل الدخول بنجاح\");\n            // Redirect to dashboard\n            const redirectTo = router.query.redirect || \"/dashboard\";\n            router.push(redirectTo);\n        } catch (error) {\n            var _error_response_data_error, _error_response_data, _error_response;\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_error = _error_response_data.error) === null || _error_response_data_error === void 0 ? void 0 : _error_response_data_error.message) || \"فشل في تسجيل الدخول\";\n            dispatch({\n                type: \"AUTH_FAILURE\",\n                payload: errorMessage\n            });\n            toast.error(errorMessage);\n            throw error;\n        }\n    };\n    // Register\n    const register = async (data)=>{\n        try {\n            dispatch({\n                type: \"AUTH_START\"\n            });\n            await authService.register(data);\n            toast.success(\"تم التسجيل بنجاح. يرجى التحقق من بريدك الإلكتروني\");\n            router.push(\"/auth/verify-email\");\n        } catch (error) {\n            var _error_response_data_error, _error_response_data, _error_response;\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_error = _error_response_data.error) === null || _error_response_data_error === void 0 ? void 0 : _error_response_data_error.message) || \"فشل في التسجيل\";\n            dispatch({\n                type: \"AUTH_FAILURE\",\n                payload: errorMessage\n            });\n            toast.error(errorMessage);\n            throw error;\n        }\n    };\n    // Logout\n    const logout = async ()=>{\n        try {\n            await authService.logout();\n        } catch (error) {\n            logger.error(\"Logout error:\", error);\n        } finally{\n            // Clear local storage\n            localStorage.removeItem(\"accessToken\");\n            localStorage.removeItem(\"refreshToken\");\n            dispatch({\n                type: \"AUTH_LOGOUT\"\n            });\n            toast.success(\"تم تسجيل الخروج بنجاح\");\n            router.push(\"/\");\n        }\n    };\n    // Refresh token\n    const refreshToken = async ()=>{\n        try {\n            const refreshToken = localStorage.getItem(\"refreshToken\");\n            if (!refreshToken) {\n                throw new Error(\"No refresh token available\");\n            }\n            const response = await authService.refreshToken(refreshToken);\n            localStorage.setItem(\"accessToken\", response.accessToken);\n            localStorage.setItem(\"refreshToken\", response.refreshToken);\n        } catch (error) {\n            logger.error(\"Token refresh failed:\", error);\n            // Clear tokens and logout\n            localStorage.removeItem(\"accessToken\");\n            localStorage.removeItem(\"refreshToken\");\n            dispatch({\n                type: \"AUTH_LOGOUT\"\n            });\n            // Redirect to login if not already there\n            if (router.pathname !== \"/auth/login\") {\n                router.push(\"/auth/login\");\n            }\n            throw error;\n        }\n    };\n    // Update profile\n    const updateProfile = async (data)=>{\n        try {\n            const updatedUser = await authService.updateProfile(data);\n            dispatch({\n                type: \"AUTH_UPDATE_USER\",\n                payload: updatedUser\n            });\n            toast.success(\"تم تحديث الملف الشخصي بنجاح\");\n        } catch (error) {\n            var _error_response_data_error, _error_response_data, _error_response;\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_error = _error_response_data.error) === null || _error_response_data_error === void 0 ? void 0 : _error_response_data_error.message) || \"فشل في تحديث الملف الشخصي\";\n            toast.error(errorMessage);\n            throw error;\n        }\n    };\n    // Clear error\n    const clearError = ()=>{\n        dispatch({\n            type: \"AUTH_CLEAR_ERROR\"\n        });\n    };\n    const value = {\n        ...state,\n        login,\n        register,\n        logout,\n        refreshToken,\n        updateProfile,\n        clearError,\n        checkAuth\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\store\\\\AuthContext.tsx\",\n        lineNumber: 321,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"CWXkBfVANc7h/TpEjdlj6Zdi6Y0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Hook to use auth context\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// HOC for protected routes\nfunction withAuth(WrappedComponent) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _s = $RefreshSig$();\n    const { requireAuth = true, requireRole = [] } = options;\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { isAuthenticated, isLoading, user } = useAuth();\n        const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!isLoading) {\n                if (requireAuth && !isAuthenticated) {\n                    router.push(\"/auth/login?redirect=\".concat(encodeURIComponent(router.asPath)));\n                    return;\n                }\n                if (requireRole.length > 0 && user && !requireRole.includes(user.role)) {\n                    router.push(\"/unauthorized\");\n                    return;\n                }\n            }\n        }, [\n            isAuthenticated,\n            isLoading,\n            user,\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\store\\\\AuthContext.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\store\\\\AuthContext.tsx\",\n                lineNumber: 363,\n                columnNumber: 9\n            }, this);\n        }\n        if (requireAuth && !isAuthenticated) {\n            return null;\n        }\n        if (requireRole.length > 0 && user && !requireRole.includes(user.role)) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\store\\\\AuthContext.tsx\",\n            lineNumber: 377,\n            columnNumber: 12\n        }, this);\n    }, \"UGkbzpeJOLBfWXlF0P6hHCfoRRA=\", false, function() {\n        return [\n            useAuth,\n            next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n        ];\n    });\n}\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/store/AuthContext.tsx\n"));

/***/ })

});