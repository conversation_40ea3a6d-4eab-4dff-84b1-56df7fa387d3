import { httpClient, ApiResponse, PaginatedResponse } from './api';

// Payment Types
export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_transfer' | 'digital_wallet' | 'qr_code';
  name: string;
  description: string;
  fees: number;
  processingTime: string;
  available: boolean;
  popular?: boolean;
}

export interface Payment {
  id: string;
  amount: number;
  currency: string;
  method: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  reference: string;
  description: string;
  jamiyaId?: string;
  jamiyaName?: string;
  createdAt: string;
  completedAt?: string;
  fees: number;
  metadata?: Record<string, any>;
}

export interface CreatePaymentRequest {
  amount: number;
  currency: string;
  method: string;
  description: string;
  jamiyaId?: string;
  metadata?: Record<string, any>;
}

export interface PaymentStats {
  totalPaid: number;
  totalPending: number;
  totalFees: number;
  paymentCount: number;
  lastPaymentDate?: string;
}

export interface PaymentSummary {
  baseAmount: number;
  fees: number;
  total: number;
  method: PaymentMethod;
}

// Payment Service Class
class PaymentService {
  /**
   * Get available payment methods
   */
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      const response = await httpClient.get<PaymentMethod[]>('/payments/methods');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get payment methods');
    } catch (error) {
      console.error('Get payment methods error:', error);
      throw error;
    }
  }

  /**
   * Calculate payment summary
   */
  async calculatePaymentSummary(amount: number, methodId: string): Promise<PaymentSummary> {
    try {
      const response = await httpClient.post<PaymentSummary>('/payments/calculate', {
        amount,
        methodId,
      });
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to calculate payment');
    } catch (error) {
      console.error('Calculate payment error:', error);
      throw error;
    }
  }

  /**
   * Create new payment
   */
  async createPayment(paymentData: CreatePaymentRequest): Promise<Payment> {
    try {
      const response = await httpClient.post<Payment>('/payments', paymentData);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to create payment');
    } catch (error) {
      console.error('Create payment error:', error);
      throw error;
    }
  }

  /**
   * Get payment by ID
   */
  async getPayment(id: string): Promise<Payment> {
    try {
      const response = await httpClient.get<Payment>(`/payments/${id}`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get payment');
    } catch (error) {
      console.error('Get payment error:', error);
      throw error;
    }
  }

  /**
   * Get user's payments with pagination
   */
  async getPayments(params?: {
    page?: number;
    limit?: number;
    status?: string;
    jamiyaId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<PaginatedResponse<Payment>> {
    try {
      const response = await httpClient.get<PaginatedResponse<Payment>>('/payments', params);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get payments');
    } catch (error) {
      console.error('Get payments error:', error);
      throw error;
    }
  }

  /**
   * Get payment statistics
   */
  async getPaymentStats(): Promise<PaymentStats> {
    try {
      const response = await httpClient.get<PaymentStats>('/payments/stats');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get payment stats');
    } catch (error) {
      console.error('Get payment stats error:', error);
      throw error;
    }
  }

  /**
   * Cancel payment
   */
  async cancelPayment(id: string): Promise<Payment> {
    try {
      const response = await httpClient.post<Payment>(`/payments/${id}/cancel`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to cancel payment');
    } catch (error) {
      console.error('Cancel payment error:', error);
      throw error;
    }
  }

  /**
   * Retry failed payment
   */
  async retryPayment(id: string): Promise<Payment> {
    try {
      const response = await httpClient.post<Payment>(`/payments/${id}/retry`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to retry payment');
    } catch (error) {
      console.error('Retry payment error:', error);
      throw error;
    }
  }

  /**
   * Get pending payments for user
   */
  async getPendingPayments(): Promise<Payment[]> {
    try {
      const response = await httpClient.get<Payment[]>('/payments/pending');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get pending payments');
    } catch (error) {
      console.error('Get pending payments error:', error);
      throw error;
    }
  }

  /**
   * Pay jamiya contribution
   */
  async payContribution(jamiyaId: string, amount: number, method: string): Promise<Payment> {
    try {
      const paymentData: CreatePaymentRequest = {
        amount,
        currency: 'SAR',
        method,
        description: `مساهمة جمعية - ${new Date().toLocaleDateString('ar-SA')}`,
        jamiyaId,
        metadata: {
          type: 'contribution',
          jamiyaId,
        },
      };

      return await this.createPayment(paymentData);
    } catch (error) {
      console.error('Pay contribution error:', error);
      throw error;
    }
  }

  /**
   * Get payment receipt
   */
  async getPaymentReceipt(id: string): Promise<Blob> {
    try {
      const response = await fetch(`${httpClient.baseURL}/payments/${id}/receipt`, {
        method: 'GET',
        headers: await httpClient.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to get payment receipt');
      }

      return await response.blob();
    } catch (error) {
      console.error('Get payment receipt error:', error);
      throw error;
    }
  }

  /**
   * Verify payment status
   */
  async verifyPayment(id: string): Promise<Payment> {
    try {
      const response = await httpClient.post<Payment>(`/payments/${id}/verify`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to verify payment');
    } catch (error) {
      console.error('Verify payment error:', error);
      throw error;
    }
  }

  /**
   * Get payment methods for specific amount
   */
  async getAvailableMethodsForAmount(amount: number): Promise<PaymentMethod[]> {
    try {
      const response = await httpClient.get<PaymentMethod[]>('/payments/methods/available', {
        amount,
      });
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to get available methods');
    } catch (error) {
      console.error('Get available methods error:', error);
      throw error;
    }
  }

  /**
   * Process QR payment
   */
  async processQRPayment(qrData: string): Promise<Payment> {
    try {
      const response = await httpClient.post<Payment>('/payments/qr', { qrData });
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to process QR payment');
    } catch (error) {
      console.error('Process QR payment error:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const paymentService = new PaymentService();
export default paymentService;
