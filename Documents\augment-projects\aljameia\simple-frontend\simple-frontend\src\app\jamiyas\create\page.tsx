'use client';

import { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  CheckIcon,
  InformationCircleIcon,
  CurrencyDollarIcon,
  UsersIcon,
  CalendarIcon,
  CogIcon,
  DocumentCheckIcon,
  ExclamationTriangleIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';
import type { CreateJamiyaForm, JamiyaType, JamiyaRules, JamiyaSettings } from '@/types/jamiya';

export default function CreateJamiyaPage() {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState<CreateJamiyaForm>({
    name: '',
    description: '',
    type: 'simple',
    totalMembers: 12,
    monthlyAmount: 5000,
    currency: 'SAR',
    startDate: '',
    rules: {
      allowEarlyWithdrawal: false,
      allowLateJoining: false,
      requireGuarantor: true,
      lateFee: 50,
      earlyWithdrawalDiscount: 0,
      lateWithdrawalBonus: 0,
      allowBidding: false,
      minimumBid: 0,
      biddingDeadline: 48,
      gracePeriod: 3,
      maxMissedPayments: 2,
      autoSuspension: true,
      advanceNotice: 7,
      withdrawalPenalty: 100
    },
    settings: {
      enableEmailNotifications: true,
      enableSmsNotifications: true,
      enablePushNotifications: true,
      paymentReminderDays: [7, 3, 1],
      cycleReminderDays: [7, 1],
      autoProcessPayments: false,
      autoDistributeFunds: false,
      autoSuspendDefaulters: true,
      showMemberDetails: true,
      showPaymentHistory: false,
      allowMemberCommunication: true,
      requireTwoFactorAuth: true,
      requirePaymentVerification: true,
      logAllActivities: true
    },
    agenda: [],
    speakers: []
  });

  const totalSteps = 5;

  const steps = [
    {
      number: 1,
      title: 'المعلومات الأساسية',
      description: 'اسم الجمعية ووصفها',
      icon: InformationCircleIcon
    },
    {
      number: 2,
      title: 'النوع والقواعد',
      description: 'نوع الجمعية والقواعد المالية',
      icon: CurrencyDollarIcon
    },
    {
      number: 3,
      title: 'التفاصيل المالية',
      description: 'المبلغ والأعضاء والمدة',
      icon: UsersIcon
    },
    {
      number: 4,
      title: 'الإعدادات',
      description: 'الإشعارات والأمان',
      icon: CogIcon
    },
    {
      number: 5,
      title: 'المراجعة',
      description: 'مراجعة وتأكيد البيانات',
      icon: DocumentCheckIcon
    }
  ];

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // محاكاة إرسال البيانات
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // في التطبيق الحقيقي، سيتم إرسال البيانات إلى API
    console.log('Creating Jamiya:', formData);
    
    setIsSubmitting(false);
    
    // إعادة توجيه إلى صفحة الجمعيات
    window.location.href = '/jamiyas';
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateRules = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      rules: {
        ...prev.rules,
        [field]: value
      }
    }));
  };

  const updateSettings = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        [field]: value
      }
    }));
  };

  const getJamiyaTypeText = (type: JamiyaType) => {
    switch (type) {
      case 'simple': return 'جمعية بسيطة';
      case 'auction': return 'جمعية بالمزايدة';
      case 'discount': return 'جمعية بالخصم';
      case 'flexible': return 'جمعية مرنة';
      default: return type;
    }
  };

  const getJamiyaTypeDescription = (type: JamiyaType) => {
    switch (type) {
      case 'simple': 
        return 'مبلغ ثابت شهرياً، دورات محددة مسبقاً أو بالقرعة، بدون فوائد أو أرباح';
      case 'auction': 
        return 'الأعضاء يزايدون على الدور، من يدفع أعلى مبلغ إضافي يحصل على الدور';
      case 'discount': 
        return 'من يأخذ دوره مبكراً يدفع خصم، من يأخذ دوره متأخراً يحصل على زيادة';
      case 'flexible': 
        return 'مبالغ متغيرة حسب الظروف والاتفاق بين الأعضاء';
      default: 
        return '';
    }
  };

  const isStepValid = (step: number) => {
    switch (step) {
      case 1:
        return formData.name.trim() !== '' && formData.description.trim() !== '';
      case 2:
        return formData.type !== '';
      case 3:
        return formData.totalMembers > 0 && formData.monthlyAmount > 0 && formData.startDate !== '';
      case 4:
        return true; // الإعدادات اختيارية
      case 5:
        return true; // مراجعة فقط
      default:
        return false;
    }
  };

  return (
    <ProtectedRoute requiredPermissions={['jamiya_management']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">منصة الجمعيات المالية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/jamiyas">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    إدارة الجمعيات
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <h1 className="text-2xl font-bold text-gray-900 mb-2">إنشاء جمعية مالية جديدة</h1>
            <p className="text-gray-600">اتبع الخطوات التالية لإنشاء جمعية مالية جديدة</p>
          </motion.div>

          {/* Progress Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.number} className="flex items-center">
                  <div className="flex items-center">
                    <div className={`
                      flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                      ${currentStep >= step.number 
                        ? 'bg-blue-600 border-blue-600 text-white' 
                        : 'border-gray-300 text-gray-500'
                      }
                    `}>
                      {currentStep > step.number ? (
                        <CheckIcon className="h-5 w-5" />
                      ) : (
                        <step.icon className="h-5 w-5" />
                      )}
                    </div>
                    <div className="mr-3 hidden sm:block">
                      <div className={`text-sm font-medium ${
                        currentStep >= step.number ? 'text-blue-600' : 'text-gray-500'
                      }`}>
                        {step.title}
                      </div>
                      <div className="text-xs text-gray-500">{step.description}</div>
                    </div>
                  </div>
                  
                  {index < steps.length - 1 && (
                    <div className={`
                      flex-1 h-0.5 mx-4 transition-colors
                      ${currentStep > step.number ? 'bg-blue-600' : 'bg-gray-300'}
                    `} />
                  )}
                </div>
              ))}
            </div>
          </motion.div>

          {/* Form Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
          >
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم الجمعية *
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => updateFormData('name', e.target.value)}
                        placeholder="مثال: جمعية الأصدقاء"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        وصف الجمعية *
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => updateFormData('description', e.target.value)}
                        placeholder="وصف مختصر عن الجمعية وأهدافها..."
                        rows={4}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Type and Rules */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">نوع الجمعية والقواعد</h3>

                  <div className="space-y-6">
                    {/* Jamiya Type Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        نوع الجمعية *
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {(['simple', 'auction', 'discount', 'flexible'] as JamiyaType[]).map((type) => (
                          <div
                            key={type}
                            className={`
                              border-2 rounded-lg p-4 cursor-pointer transition-colors
                              ${formData.type === type
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                              }
                            `}
                            onClick={() => updateFormData('type', type)}
                          >
                            <div className="flex items-center mb-2">
                              <input
                                type="radio"
                                name="jamiyaType"
                                value={type}
                                checked={formData.type === type}
                                onChange={() => updateFormData('type', type)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                              />
                              <label className="mr-3 text-sm font-medium text-gray-900">
                                {getJamiyaTypeText(type)}
                              </label>
                            </div>
                            <p className="text-sm text-gray-600">
                              {getJamiyaTypeDescription(type)}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Basic Rules */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">القواعد الأساسية</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              السماح بالانسحاب المبكر
                            </label>
                            <p className="text-xs text-gray-500">
                              هل يمكن للأعضاء الانسحاب قبل انتهاء الجمعية؟
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.rules.allowEarlyWithdrawal}
                            onChange={(e) => updateRules('allowEarlyWithdrawal', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              السماح بالانضمام المتأخر
                            </label>
                            <p className="text-xs text-gray-500">
                              هل يمكن انضمام أعضاء جدد بعد بداية الجمعية؟
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.rules.allowLateJoining}
                            onChange={(e) => updateRules('allowLateJoining', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              طلب كفيل للأعضاء الجدد
                            </label>
                            <p className="text-xs text-gray-500">
                              هل يجب على الأعضاء الجدد تقديم كفيل؟
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.rules.requireGuarantor}
                            onChange={(e) => updateRules('requireGuarantor', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Financial Rules */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">القواعد المالية</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            غرامة التأخير (ريال)
                          </label>
                          <input
                            type="number"
                            value={formData.rules.lateFee}
                            onChange={(e) => updateRules('lateFee', parseInt(e.target.value) || 0)}
                            min="0"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            فترة السماح (أيام)
                          </label>
                          <input
                            type="number"
                            value={formData.rules.gracePeriod}
                            onChange={(e) => updateRules('gracePeriod', parseInt(e.target.value) || 0)}
                            min="0"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            الحد الأقصى للمدفوعات المفقودة
                          </label>
                          <input
                            type="number"
                            value={formData.rules.maxMissedPayments}
                            onChange={(e) => updateRules('maxMissedPayments', parseInt(e.target.value) || 0)}
                            min="0"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            غرامة الانسحاب (ريال)
                          </label>
                          <input
                            type="number"
                            value={formData.rules.withdrawalPenalty}
                            onChange={(e) => updateRules('withdrawalPenalty', parseInt(e.target.value) || 0)}
                            min="0"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Financial Details */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">التفاصيل المالية</h3>

                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          عدد الأعضاء *
                        </label>
                        <input
                          type="number"
                          value={formData.totalMembers}
                          onChange={(e) => updateFormData('totalMembers', parseInt(e.target.value) || 0)}
                          min="2"
                          max="50"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          required
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          عدد الأعضاء المشاركين في الجمعية (2-50 عضو)
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          المبلغ الشهري *
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            value={formData.monthlyAmount}
                            onChange={(e) => updateFormData('monthlyAmount', parseInt(e.target.value) || 0)}
                            min="100"
                            className="w-full px-4 py-2 pr-16 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 sm:text-sm">ريال</span>
                          </div>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          المبلغ الذي يساهم به كل عضو شهرياً
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          تاريخ البداية *
                        </label>
                        <input
                          type="date"
                          value={formData.startDate}
                          onChange={(e) => updateFormData('startDate', e.target.value)}
                          min={new Date().toISOString().split('T')[0]}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          العملة
                        </label>
                        <select
                          value={formData.currency}
                          onChange={(e) => updateFormData('currency', e.target.value)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="SAR">ريال سعودي (SAR)</option>
                          <option value="USD">دولار أمريكي (USD)</option>
                          <option value="EUR">يورو (EUR)</option>
                          <option value="AED">درهم إماراتي (AED)</option>
                        </select>
                      </div>
                    </div>

                    {/* Financial Summary */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="text-md font-medium text-blue-900 mb-3">ملخص مالي</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-blue-700 font-medium">المبلغ الشهري الإجمالي:</span>
                          <div className="text-lg font-bold text-blue-900">
                            {(formData.totalMembers * formData.monthlyAmount).toLocaleString()} {formData.currency}
                          </div>
                        </div>
                        <div>
                          <span className="text-blue-700 font-medium">مدة الجمعية:</span>
                          <div className="text-lg font-bold text-blue-900">
                            {formData.totalMembers} شهر
                          </div>
                        </div>
                        <div>
                          <span className="text-blue-700 font-medium">إجمالي المبلغ:</span>
                          <div className="text-lg font-bold text-blue-900">
                            {(formData.totalMembers * formData.totalMembers * formData.monthlyAmount).toLocaleString()} {formData.currency}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Payment Schedule Preview */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">جدول الدورات المتوقع</h4>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 text-sm">
                          {Array.from({ length: Math.min(formData.totalMembers, 6) }, (_, i) => {
                            const cycleDate = new Date(formData.startDate);
                            cycleDate.setMonth(cycleDate.getMonth() + i);
                            return (
                              <div key={i} className="flex justify-between py-1">
                                <span>الدورة {i + 1}:</span>
                                <span className="font-medium">
                                  {cycleDate.toLocaleDateString('ar-SA', { month: 'long', year: 'numeric' })}
                                </span>
                              </div>
                            );
                          })}
                          {formData.totalMembers > 6 && (
                            <div className="text-gray-500 text-center col-span-full">
                              ... و {formData.totalMembers - 6} دورة أخرى
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Settings */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">الإعدادات والتفضيلات</h3>

                  <div className="space-y-6">
                    {/* Notification Settings */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">إعدادات الإشعارات</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              إشعارات البريد الإلكتروني
                            </label>
                            <p className="text-xs text-gray-500">
                              إرسال تذكيرات ومعلومات عبر البريد الإلكتروني
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.settings.enableEmailNotifications}
                            onChange={(e) => updateSettings('enableEmailNotifications', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              إشعارات الرسائل النصية
                            </label>
                            <p className="text-xs text-gray-500">
                              إرسال تذكيرات عاجلة عبر الرسائل النصية
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.settings.enableSmsNotifications}
                            onChange={(e) => updateSettings('enableSmsNotifications', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              الإشعارات الفورية
                            </label>
                            <p className="text-xs text-gray-500">
                              إشعارات فورية عبر التطبيق والمتصفح
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.settings.enablePushNotifications}
                            onChange={(e) => updateSettings('enablePushNotifications', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Security Settings */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">إعدادات الأمان</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              المصادقة الثنائية
                            </label>
                            <p className="text-xs text-gray-500">
                              طلب رمز تأكيد إضافي للعمليات المهمة
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.settings.requireTwoFactorAuth}
                            onChange={(e) => updateSettings('requireTwoFactorAuth', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              تأكيد المدفوعات
                            </label>
                            <p className="text-xs text-gray-500">
                              طلب تأكيد إضافي لجميع المدفوعات
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.settings.requirePaymentVerification}
                            onChange={(e) => updateSettings('requirePaymentVerification', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              تسجيل جميع الأنشطة
                            </label>
                            <p className="text-xs text-gray-500">
                              الاحتفاظ بسجل مفصل لجميع العمليات
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.settings.logAllActivities}
                            onChange={(e) => updateSettings('logAllActivities', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Privacy Settings */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-3">إعدادات الخصوصية</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              عرض تفاصيل الأعضاء
                            </label>
                            <p className="text-xs text-gray-500">
                              السماح للأعضاء برؤية معلومات بعضهم البعض
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.settings.showMemberDetails}
                            onChange={(e) => updateSettings('showMemberDetails', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              عرض تاريخ المدفوعات
                            </label>
                            <p className="text-xs text-gray-500">
                              السماح للأعضاء برؤية تاريخ مدفوعات الآخرين
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.settings.showPaymentHistory}
                            onChange={(e) => updateSettings('showPaymentHistory', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              السماح بالتواصل بين الأعضاء
                            </label>
                            <p className="text-xs text-gray-500">
                              تمكين الأعضاء من التواصل مع بعضهم البعض
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            checked={formData.settings.allowMemberCommunication}
                            onChange={(e) => updateSettings('allowMemberCommunication', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 5: Review */}
            {currentStep === 5 && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">مراجعة وتأكيد البيانات</h3>

                  <div className="space-y-6">
                    {/* Basic Information Review */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-md font-medium text-gray-900 mb-3">المعلومات الأساسية</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">اسم الجمعية:</span>
                          <div className="font-medium">{formData.name}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">النوع:</span>
                          <div className="font-medium">{getJamiyaTypeText(formData.type)}</div>
                        </div>
                        <div className="md:col-span-2">
                          <span className="text-gray-600">الوصف:</span>
                          <div className="font-medium">{formData.description}</div>
                        </div>
                      </div>
                    </div>

                    {/* Financial Details Review */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-md font-medium text-gray-900 mb-3">التفاصيل المالية</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">عدد الأعضاء:</span>
                          <div className="font-medium">{formData.totalMembers} عضو</div>
                        </div>
                        <div>
                          <span className="text-gray-600">المبلغ الشهري:</span>
                          <div className="font-medium">{formData.monthlyAmount.toLocaleString()} {formData.currency}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">تاريخ البداية:</span>
                          <div className="font-medium">
                            {new Date(formData.startDate).toLocaleDateString('ar-SA')}
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-600">المبلغ الشهري الإجمالي:</span>
                          <div className="font-medium text-green-600">
                            {(formData.totalMembers * formData.monthlyAmount).toLocaleString()} {formData.currency}
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-600">مدة الجمعية:</span>
                          <div className="font-medium">{formData.totalMembers} شهر</div>
                        </div>
                        <div>
                          <span className="text-gray-600">إجمالي المبلغ:</span>
                          <div className="font-medium text-blue-600">
                            {(formData.totalMembers * formData.totalMembers * formData.monthlyAmount).toLocaleString()} {formData.currency}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Rules Summary */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-md font-medium text-gray-900 mb-3">ملخص القواعد</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">الانسحاب المبكر:</span>
                          <span className={formData.rules.allowEarlyWithdrawal ? 'text-green-600' : 'text-red-600'}>
                            {formData.rules.allowEarlyWithdrawal ? 'مسموح' : 'غير مسموح'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">الانضمام المتأخر:</span>
                          <span className={formData.rules.allowLateJoining ? 'text-green-600' : 'text-red-600'}>
                            {formData.rules.allowLateJoining ? 'مسموح' : 'غير مسموح'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">طلب كفيل:</span>
                          <span className={formData.rules.requireGuarantor ? 'text-orange-600' : 'text-green-600'}>
                            {formData.rules.requireGuarantor ? 'مطلوب' : 'غير مطلوب'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">غرامة التأخير:</span>
                          <span className="font-medium">{formData.rules.lateFee} {formData.currency}</span>
                        </div>
                      </div>
                    </div>

                    {/* Warning */}
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex">
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 ml-2" />
                        <div>
                          <h4 className="text-sm font-medium text-yellow-800">تنبيه مهم</h4>
                          <p className="text-sm text-yellow-700 mt-1">
                            بعد إنشاء الجمعية، لن تتمكن من تعديل المعلومات الأساسية مثل عدد الأعضاء والمبلغ الشهري.
                            تأكد من صحة جميع البيانات قبل المتابعة.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between pt-6 border-t border-gray-200">
              <button
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowRightIcon className="h-4 w-4 ml-2" />
                السابق
              </button>

              <div className="text-sm text-gray-500">
                الخطوة {currentStep} من {totalSteps}
              </div>

              {currentStep < totalSteps ? (
                <button
                  onClick={handleNext}
                  disabled={!isStepValid(currentStep)}
                  className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  التالي
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="flex items-center px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                      جاري الإنشاء...
                    </>
                  ) : (
                    <>
                      <CheckIcon className="h-4 w-4 ml-2" />
                      إنشاء الجمعية
                    </>
                  )}
                </button>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
