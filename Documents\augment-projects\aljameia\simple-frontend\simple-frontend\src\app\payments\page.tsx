'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import {
  CreditCardIcon,
  BanknotesIcon,
  DevicePhoneMobileIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowPathIcon,
  QrCodeIcon,
  BuildingLibraryIcon
} from '@heroicons/react/24/outline';

interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_transfer' | 'digital_wallet' | 'qr_code';
  name: string;
  description: string;
  icon: any;
  fees: number;
  processingTime: string;
  available: boolean;
  popular?: boolean;
}

interface PaymentTransaction {
  id: string;
  amount: number;
  currency: string;
  method: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  reference: string;
  description: string;
  createdAt: string;
  completedAt?: string;
  fees: number;
}

export default function PaymentsPage() {
  const { user } = useAuth();
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [amount, setAmount] = useState<number>(0);
  const [description, setDescription] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [transactions, setTransactions] = useState<PaymentTransaction[]>([]);
  const [showSuccess, setShowSuccess] = useState(false);

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'mada',
      type: 'card',
      name: 'مدى',
      description: 'بطاقة مدى السعودية',
      icon: CreditCardIcon,
      fees: 0,
      processingTime: 'فوري',
      available: true,
      popular: true
    },
    {
      id: 'visa',
      type: 'card',
      name: 'فيزا',
      description: 'بطاقة فيزا الائتمانية',
      icon: CreditCardIcon,
      fees: 2.5,
      processingTime: 'فوري',
      available: true
    },
    {
      id: 'mastercard',
      type: 'card',
      name: 'ماستركارد',
      description: 'بطاقة ماستركارد الائتمانية',
      icon: CreditCardIcon,
      fees: 2.5,
      processingTime: 'فوري',
      available: true
    },
    {
      id: 'bank_transfer',
      type: 'bank_transfer',
      name: 'تحويل بنكي',
      description: 'تحويل مباشر من البنك',
      icon: BuildingLibraryIcon,
      fees: 0,
      processingTime: '1-3 أيام عمل',
      available: true
    },
    {
      id: 'stc_pay',
      type: 'digital_wallet',
      name: 'STC Pay',
      description: 'محفظة STC الرقمية',
      icon: DevicePhoneMobileIcon,
      fees: 0,
      processingTime: 'فوري',
      available: true,
      popular: true
    },
    {
      id: 'apple_pay',
      type: 'digital_wallet',
      name: 'Apple Pay',
      description: 'محفظة آبل الرقمية',
      icon: DevicePhoneMobileIcon,
      fees: 0,
      processingTime: 'فوري',
      available: true
    },
    {
      id: 'qr_code',
      type: 'qr_code',
      name: 'رمز QR',
      description: 'الدفع عبر رمز QR',
      icon: QrCodeIcon,
      fees: 0,
      processingTime: 'فوري',
      available: true
    }
  ];

  useEffect(() => {
    // Load mock transactions
    setTransactions([
      {
        id: 'TXN001',
        amount: 5000,
        currency: 'SAR',
        method: 'مدى',
        status: 'completed',
        reference: 'REF123456789',
        description: 'دفعة جمعية الأصدقاء - يونيو 2024',
        createdAt: '2024-06-18T10:30:00Z',
        completedAt: '2024-06-18T10:31:00Z',
        fees: 0
      },
      {
        id: 'TXN002',
        amount: 3000,
        currency: 'SAR',
        method: 'STC Pay',
        status: 'completed',
        reference: 'REF987654321',
        description: 'دفعة جمعية العائلة - يونيو 2024',
        createdAt: '2024-06-17T14:20:00Z',
        completedAt: '2024-06-17T14:20:30Z',
        fees: 0
      },
      {
        id: 'TXN003',
        amount: 2500,
        currency: 'SAR',
        method: 'تحويل بنكي',
        status: 'processing',
        reference: 'REF456789123',
        description: 'دفعة جمعية الأحياء - يونيو 2024',
        createdAt: '2024-06-16T09:15:00Z',
        fees: 0
      }
    ]);
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'processing':
        return <ClockIcon className="h-5 w-5 text-yellow-600" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'processing':
        return 'قيد المعالجة';
      case 'pending':
        return 'معلقة';
      case 'failed':
        return 'فشلت';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'processing':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'pending':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'failed':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'cancelled':
        return 'bg-gray-50 text-gray-700 border-gray-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  const calculateTotal = () => {
    const selectedMethodData = paymentMethods.find(m => m.id === selectedMethod);
    const fees = selectedMethodData ? (amount * selectedMethodData.fees / 100) : 0;
    return amount + fees;
  };

  const handlePayment = async () => {
    if (!selectedMethod || amount <= 0) return;

    setIsProcessing(true);

    // Simulate payment processing
    setTimeout(() => {
      const newTransaction: PaymentTransaction = {
        id: `TXN${Date.now()}`,
        amount,
        currency: 'SAR',
        method: paymentMethods.find(m => m.id === selectedMethod)?.name || '',
        status: 'completed',
        reference: `REF${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
        description,
        createdAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
        fees: calculateTotal() - amount
      };

      setTransactions(prev => [newTransaction, ...prev]);
      setIsProcessing(false);
      setShowSuccess(true);
      
      // Reset form
      setAmount(0);
      setDescription('');
      setSelectedMethod('');

      // Hide success message after 3 seconds
      setTimeout(() => setShowSuccess(false), 3000);
    }, 3000);
  };

  return (
    <ProtectedRoute requiredPermissions={['payments']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">نظام الدفع الإلكتروني</h1>
                <p className="text-gray-600 mt-1">
                  ادفع مساهماتك بسهولة وأمان عبر طرق الدفع المتعددة
                </p>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <ShieldCheckIcon className="h-8 w-8 text-green-600" />
                <span className="text-sm text-green-600 font-medium">آمن ومشفر</span>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Success Message */}
          {showSuccess && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6"
            >
              <div className="flex items-center">
                <CheckCircleIcon className="h-6 w-6 text-green-600 ml-3" />
                <div>
                  <h3 className="text-green-800 font-medium">تم الدفع بنجاح!</h3>
                  <p className="text-green-700 text-sm mt-1">
                    تم معالجة دفعتك وستظهر في سجل المعاملات
                  </p>
                </div>
              </div>
            </motion.div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Payment Form */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <div className="flex items-center mb-6">
                <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CreditCardIcon className="h-6 w-6 text-blue-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mr-3">إجراء دفعة جديدة</h2>
              </div>

              <div className="space-y-6">
                {/* Amount */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    المبلغ (ريال سعودي) *
                  </label>
                  <input
                    type="number"
                    value={amount || ''}
                    onChange={(e) => setAmount(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0"
                    min="1"
                    max="100000"
                  />
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    وصف الدفعة *
                  </label>
                  <input
                    type="text"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="مثال: دفعة جمعية الأصدقاء - يونيو 2024"
                  />
                </div>

                {/* Payment Methods */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-4">
                    طريقة الدفع *
                  </label>
                  <div className="grid grid-cols-1 gap-3">
                    {paymentMethods.map((method) => (
                      <div
                        key={method.id}
                        onClick={() => method.available && setSelectedMethod(method.id)}
                        className={`
                          relative p-4 rounded-lg border-2 cursor-pointer transition-all
                          ${!method.available 
                            ? 'opacity-50 cursor-not-allowed bg-gray-50' 
                            : selectedMethod === method.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 bg-white hover:border-gray-300'
                          }
                        `}
                      >
                        {method.popular && (
                          <span className="absolute top-2 left-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            الأكثر استخداماً
                          </span>
                        )}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <method.icon className="h-6 w-6 text-gray-600 ml-3" />
                            <div>
                              <h4 className="font-medium text-gray-900">{method.name}</h4>
                              <p className="text-sm text-gray-600">{method.description}</p>
                            </div>
                          </div>
                          <div className="text-left">
                            <p className="text-sm text-gray-600">
                              {method.fees > 0 ? `${method.fees}% رسوم` : 'بدون رسوم'}
                            </p>
                            <p className="text-xs text-gray-500">{method.processingTime}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Payment Summary */}
                {amount > 0 && selectedMethod && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-3">ملخص الدفعة</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">المبلغ الأساسي:</span>
                        <span className="font-medium">{formatCurrency(amount)}</span>
                      </div>
                      {calculateTotal() - amount > 0 && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">الرسوم:</span>
                          <span className="font-medium">{formatCurrency(calculateTotal() - amount)}</span>
                        </div>
                      )}
                      <div className="flex justify-between border-t border-gray-200 pt-2">
                        <span className="font-medium text-gray-900">المجموع:</span>
                        <span className="font-bold text-blue-600">{formatCurrency(calculateTotal())}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Pay Button */}
                <button
                  onClick={handlePayment}
                  disabled={!selectedMethod || amount <= 0 || !description || isProcessing}
                  className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      <CreditCardIcon className="h-5 w-5 ml-2" />
                      ادفع الآن
                    </>
                  )}
                </button>
              </div>
            </motion.div>

            {/* Transaction History */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <BanknotesIcon className="h-6 w-6 text-green-600" />
                  </div>
                  <h2 className="text-xl font-semibold text-gray-900 mr-3">سجل المعاملات</h2>
                </div>
                <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                  عرض الكل
                </button>
              </div>

              <div className="space-y-4">
                {transactions.length > 0 ? (
                  transactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          {getStatusIcon(transaction.status)}
                          <span className="mr-2 font-medium text-gray-900">
                            {formatCurrency(transaction.amount)}
                          </span>
                          <span className={`
                            px-2 py-1 rounded-full text-xs font-medium border
                            ${getStatusColor(transaction.status)}
                          `}>
                            {getStatusText(transaction.status)}
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">
                          {new Date(transaction.createdAt).toLocaleDateString('ar-SA')}
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 mb-2">{transaction.description}</p>

                      <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
                        <div>
                          <span>طريقة الدفع: </span>
                          <span className="font-medium">{transaction.method}</span>
                        </div>
                        <div>
                          <span>المرجع: </span>
                          <span className="font-medium">{transaction.reference}</span>
                        </div>
                        {transaction.fees > 0 && (
                          <div>
                            <span>الرسوم: </span>
                            <span className="font-medium">{formatCurrency(transaction.fees)}</span>
                          </div>
                        )}
                        {transaction.completedAt && (
                          <div>
                            <span>تم في: </span>
                            <span className="font-medium">
                              {new Date(transaction.completedAt).toLocaleTimeString('ar-SA')}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <BanknotesIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد معاملات</h3>
                    <p className="text-gray-600">ستظهر معاملاتك هنا بعد إجراء أول دفعة</p>
                  </div>
                )}
              </div>
            </motion.div>
          </div>

          {/* Security Notice */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6"
          >
            <div className="flex items-start">
              <ShieldCheckIcon className="h-6 w-6 text-blue-600 ml-3 mt-1" />
              <div>
                <h3 className="font-medium text-blue-900 mb-2">الأمان والحماية</h3>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• جميع المعاملات مشفرة بتقنية SSL 256-bit</p>
                  <p>• نحن لا نحتفظ ببيانات بطاقتك الائتمانية</p>
                  <p>• جميع المدفوعات تتم عبر بوابات دفع معتمدة من البنك المركزي السعودي</p>
                  <p>• يمكنك إلغاء أي معاملة خلال 24 ساعة من إجرائها</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Payment Methods Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">طرق الدفع المتاحة</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {paymentMethods.filter(m => m.available).map((method) => (
                <div key={method.id} className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <method.icon className="h-8 w-8 text-gray-600 ml-3" />
                  <div>
                    <h4 className="font-medium text-gray-900">{method.name}</h4>
                    <p className="text-xs text-gray-600">
                      {method.fees > 0 ? `رسوم ${method.fees}%` : 'بدون رسوم'} • {method.processingTime}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
