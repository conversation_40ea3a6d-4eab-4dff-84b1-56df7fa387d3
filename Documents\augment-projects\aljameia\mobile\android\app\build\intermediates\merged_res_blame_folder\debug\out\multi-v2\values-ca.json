{"logs": [{"outputFile": "com.aljameia.app-mergeDebugResources-104:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\21cc0a6be925d3c2a0c747bb469e8733\\transformed\\appcompat-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,447,552,659,742,848,974,1058,1137,1228,1321,1414,1509,1607,1700,1793,1887,1978,2069,2150,2261,2369,2467,2577,2682,2790,2950,19509", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "442,547,654,737,843,969,1053,1132,1223,1316,1409,1504,1602,1695,1788,1882,1973,2064,2145,2256,2364,2462,2572,2677,2785,2945,3044,19586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1c0b3a48f4d4c74d31971bddddc3d282\\transformed\\play-services-base-18.1.0\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5264,5369,5521,5648,5757,5907,6034,6157,6400,6571,6680,6839,6970,7134,7292,7357,7425", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "5364,5516,5643,5752,5902,6029,6152,6260,6566,6675,6834,6965,7129,7287,7352,7420,7507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d2a56f6da0e2e4ab4da1b326f38c566a\\transformed\\material-1.11.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1096,1193,1273,1338,1433,1497,1569,1631,1707,1770,1827,1948,2006,2067,2124,2204,2341,2428,2512,2651,2729,2808,2960,3049,3125,3182,3238,3304,3382,3463,3551,3639,3717,3794,3868,3947,4057,4147,4239,4331,4432,4506,4588,4689,4739,4822,4888,4980,5067,5129,5193,5256,5329,5452,5565,5669,5777,5838,5898", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,151,88,75,56,55,65,77,80,87,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85", "endOffsets": "269,350,430,518,621,713,814,942,1026,1091,1188,1268,1333,1428,1492,1564,1626,1702,1765,1822,1943,2001,2062,2119,2199,2336,2423,2507,2646,2724,2803,2955,3044,3120,3177,3233,3299,3377,3458,3546,3634,3712,3789,3863,3942,4052,4142,4234,4326,4427,4501,4583,4684,4734,4817,4883,4975,5062,5124,5188,5251,5324,5447,5560,5664,5772,5833,5893,5979"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3512,3593,3673,3761,3864,4687,4788,4916,11829,11977,13739,13988,14053,14148,14212,14284,14346,14422,14485,14542,14663,14721,14782,14839,14919,15056,15143,15227,15366,15444,15523,15675,15764,15840,15897,15953,16019,16097,16178,16266,16354,16432,16509,16583,16662,16772,16862,16954,17046,17147,17221,17303,17404,17454,17537,17603,17695,17782,17844,17908,17971,18044,18167,18280,18384,18492,18553,19164", "endLines": "5,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,151,88,75,56,55,65,77,80,87,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85", "endOffsets": "319,3588,3668,3756,3859,3951,4783,4911,4995,11889,12069,13814,14048,14143,14207,14279,14341,14417,14480,14537,14658,14716,14777,14834,14914,15051,15138,15222,15361,15439,15518,15670,15759,15835,15892,15948,16014,16092,16173,16261,16349,16427,16504,16578,16657,16767,16857,16949,17041,17142,17216,17298,17399,17449,17532,17598,17690,17777,17839,17903,17966,18039,18162,18275,18379,18487,18548,18608,19245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c514f303830aa824ccabc1313adde6e2\\transformed\\material3-1.1.2\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,402,518,601,695,807,945,1056,1211,1291,1385,1479,1576,1689,1814,1913,2050,2183,2330,2492,2621,2734,2852,2978,3073,3166,3283,3424,3526,3635,3745,3879,4020,4125,4227,4299,4382,4464,4546,4653,4729,4809,4906,5010,5105,5205,5288,5397,5493,5595,5710,5786,5899", "endColumns": "116,114,114,115,82,93,111,137,110,154,79,93,93,96,112,124,98,136,132,146,161,128,112,117,125,94,92,116,140,101,108,109,133,140,104,101,71,82,81,81,106,75,79,96,103,94,99,82,108,95,101,114,75,112,102", "endOffsets": "167,282,397,513,596,690,802,940,1051,1206,1286,1380,1474,1571,1684,1809,1908,2045,2178,2325,2487,2616,2729,2847,2973,3068,3161,3278,3419,3521,3630,3740,3874,4015,4120,4222,4294,4377,4459,4541,4648,4724,4804,4901,5005,5100,5200,5283,5392,5488,5590,5705,5781,5894,5997"}, "to": {"startLines": "33,34,35,36,54,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,109,112,195,198,200,204,205,206,207,208,209,210,211,212,213,214,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3049,3166,3281,3396,5181,7742,7836,7948,8086,8197,8352,8432,8526,8620,8717,8830,8955,9054,9191,9324,9471,9633,9762,9875,9993,10119,10214,10307,10424,10565,10667,10776,10886,11020,11161,11266,11668,11894,19427,19667,19850,20230,20306,20386,20483,20587,20682,20782,20865,20974,21070,21172,21287,21363,21476", "endColumns": "116,114,114,115,82,93,111,137,110,154,79,93,93,96,112,124,98,136,132,146,161,128,112,117,125,94,92,116,140,101,108,109,133,140,104,101,71,82,81,81,106,75,79,96,103,94,99,82,108,95,101,114,75,112,102", "endOffsets": "3161,3276,3391,3507,5259,7831,7943,8081,8192,8347,8427,8521,8615,8712,8825,8950,9049,9186,9319,9466,9628,9757,9870,9988,10114,10209,10302,10419,10560,10662,10771,10881,11015,11156,11261,11363,11735,11972,19504,19744,19952,20301,20381,20478,20582,20677,20777,20860,20969,21065,21167,21282,21358,21471,21574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f1a3dac0af0a85f165dc53a6762b4a81\\transformed\\play-services-basement-18.2.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6265", "endColumns": "134", "endOffsets": "6395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\523b9fad2b83d89c2b74860689d8fb91\\transformed\\biometric-1.1.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,265,380,532,677,804,937,1084,1188,1328,1480", "endColumns": "116,92,114,151,144,126,132,146,103,139,151,126", "endOffsets": "167,260,375,527,672,799,932,1079,1183,1323,1475,1602"}, "to": {"startLines": "73,107,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7512,11472,12397,12512,12664,12809,12936,13069,13216,13320,13460,13612", "endColumns": "116,92,114,151,144,126,132,146,103,139,151,126", "endOffsets": "7624,11560,12507,12659,12804,12931,13064,13211,13315,13455,13607,13734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\cdc55fe2502a063bcee343c082439e6f\\transformed\\navigation-ui-2.7.6\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "18613,18724", "endColumns": "110,121", "endOffsets": "18719,18841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5081b8eacd451e5b41f2b566f41c579e\\transformed\\core-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "42,43,44,45,46,47,48,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3956,4052,4154,4253,4350,4456,4561,19749", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "4047,4149,4248,4345,4451,4556,4682,19845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b3e74a7425b14daa5ccd7c3c1ea8df2a\\transformed\\browser-1.4.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "74,114,115,116", "startColumns": "4,4,4,4", "startOffsets": "7629,12074,12177,12288", "endColumns": "112,102,110,108", "endOffsets": "7737,12172,12283,12392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\403ef3e018096a306df17d01c38df724\\transformed\\zxing-android-embedded-4.3.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,167,323", "endColumns": "58,52,155,112", "endOffsets": "109,162,318,431"}, "to": {"startLines": "218,219,220,221", "startColumns": "4,4,4,4", "startOffsets": "21579,21638,21691,21847", "endColumns": "58,52,155,112", "endOffsets": "21633,21686,21842,21955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\85fae80ff7869a8da6251422548fb462\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1000,1069,1155,1246,1322,1404,1475", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,995,1064,1150,1241,1317,1399,1470,1590"}, "to": {"startLines": "52,53,106,108,110,128,129,188,189,190,191,193,194,197,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5000,5097,11368,11565,11740,13819,13897,18846,18937,19023,19095,19250,19336,19591,19957,20039,20110", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "5092,5176,11467,11663,11824,13892,13983,18932,19018,19090,19159,19331,19422,19662,20034,20105,20225"}}]}]}