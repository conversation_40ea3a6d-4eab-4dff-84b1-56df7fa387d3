'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import {
  ChartBarIcon,
  UsersIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  EyeIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  BanknotesIcon,
  UserGroupIcon,
  CreditCardIcon,
  ShieldCheckIcon,
  BellIcon,
  DocumentTextIcon,
  CalculatorIcon
} from '@heroicons/react/24/outline';
import type { Jamiya, JamiyaStatistics } from '@/types/jamiya';

export default function Dashboard() {
  const [isLoaded, setIsLoaded] = useState(false);
  const { user, logout, isLoading } = useAuth();

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">غير مصرح</h2>
          <p className="text-gray-600 mb-4">يجب تسجيل الدخول للوصول إلى هذه الصفحة</p>
          <Link href="/auth/login">
            <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
              تسجيل الدخول
            </button>
          </Link>
        </div>
      </div>
    );
  }

  const stats = [
    {
      title: 'إجمالي الأعضاء',
      value: '2,547',
      change: '+12%',
      changeType: 'increase' as const,
      icon: UsersIcon,
      color: 'blue'
    },
    {
      title: 'الإيرادات الشهرية',
      value: '124,500 ريال',
      change: '+8.2%',
      changeType: 'increase' as const,
      icon: CreditCardIcon,
      color: 'green'
    },
    {
      title: 'الاشتراكات النشطة',
      value: '156',
      change: '+18',
      changeType: 'increase' as const,
      icon: CurrencyDollarIcon,
      color: 'indigo'
    },
    {
      title: 'الفعاليات النشطة',
      value: '8',
      change: '+2',
      changeType: 'increase' as const,
      icon: CalendarIcon,
      color: 'purple'
    }
  ];

  const transactions = [
    {
      id: 'TXN-001',
      type: 'income' as const,
      description: 'تجديد العضوية السنوية',
      amount: 2500,
      status: 'completed' as const,
      member: 'أحمد الراشد',
      date: '2024-06-18'
    },
    {
      id: 'TXN-002',
      type: 'expense' as const,
      description: 'مستلزمات مكتبية وطباعة',
      amount: 850,
      status: 'completed' as const,
      date: '2024-06-17'
    },
    {
      id: 'TXN-003',
      type: 'income' as const,
      description: 'رسوم تسجيل المؤتمر السنوي',
      amount: 1200,
      status: 'pending' as const,
      member: 'فاطمة الزهراء',
      date: '2024-06-17'
    },
    {
      id: 'TXN-004',
      type: 'income' as const,
      description: 'تبرع خيري',
      amount: 5000,
      status: 'completed' as const,
      member: 'محمد السعود',
      date: '2024-06-16'
    }
  ];

  const quickActions = [
    {
      title: 'إدارة الأعضاء',
      description: 'عرض وإدارة جميع أعضاء الجمعية',
      icon: UsersIcon,
      color: 'blue',
      href: '/members'
    },
    {
      title: 'النظام المالي',
      description: 'إدارة المالية والمحاسبة والتقارير',
      icon: CreditCardIcon,
      color: 'green',
      href: '/finance'
    },
    {
      title: 'إدارة الوثائق',
      description: 'تنظيم وإدارة جميع وثائق الجمعية',
      icon: DocumentTextIcon,
      color: 'purple',
      href: '/documents'
    },
    {
      title: 'إدارة الفعاليات',
      description: 'تنظيم وإدارة جميع فعاليات الجمعية',
      icon: CalendarIcon,
      color: 'orange',
      href: '/events'
    },
    {
      title: 'الاشتراكات الشهرية',
      description: 'إدارة اشتراكاتك في الأسهم الشهرية للجمعيات',
      icon: CurrencyDollarIcon,
      color: 'indigo',
      href: '/subscriptions'
    },
    {
      title: 'التقارير والتحليلات',
      description: 'تقارير شاملة ومفصلة لاستثماراتك وعوائدك',
      icon: ChartBarIcon,
      color: 'blue',
      href: '/reports'
    },
    {
      title: 'حاسبة الاستثمار',
      description: 'احسب العوائد المتوقعة لاستثماراتك المستقبلية',
      icon: CalculatorIcon,
      color: 'green',
      href: '/calculator'
    },
    {
      title: 'الإشعارات والتنبيهات',
      description: 'إدارة إشعاراتك وتخصيص التنبيهات',
      icon: BellIcon,
      color: 'yellow',
      href: '/notifications'
    },
    {
      title: 'نظام الدفع الإلكتروني',
      description: 'ادفع مساهماتك بسهولة وأمان عبر طرق الدفع المتعددة',
      icon: CreditCardIcon,
      color: 'purple',
      href: '/payments'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'failed':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'معلقة';
      case 'failed':
        return 'فاشلة';
      default:
        return status;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center">
              <Link href="/">
                <div className="flex items-center cursor-pointer">
                  <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">ج</span>
                  </div>
                  <span className="mr-3 text-xl font-bold text-gray-900">الجمعية</span>
                </div>
              </Link>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="text-sm text-gray-600">
                مرحباً، {user.name}
              </div>
              <button
                onClick={logout}
                className="text-sm text-red-600 hover:text-red-800 px-3 py-1 rounded-md hover:bg-red-50 transition-colors"
              >
                تسجيل الخروج
              </button>
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                {user.avatar ? (
                  <img src={user.avatar} alt={user.name} className="h-8 w-8 rounded-full" />
                ) : (
                  <span className="text-blue-600 font-semibold text-sm">
                    {user.name.charAt(0)}
                  </span>
                )}
              </div>
            </div>
          </div>
        </nav>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            مرحباً بعودتك، {user.name}!
          </h1>
          <p className="text-gray-600">
            إليك ما يحدث في جمعيتك اليوم.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {stats.map((stat, index) => (
            <div key={index} className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                  <div className="flex items-center mt-2">
                    {stat.changeType === 'increase' ? (
                      <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 ml-1" />
                    ) : (
                      <ArrowTrendingDownIcon className="h-4 w-4 text-red-500 ml-1" />
                    )}
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                    <span className="text-sm text-gray-500 mr-1">
                      من الشهر الماضي
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg ${
                  stat.color === 'blue' ? 'bg-blue-100' :
                  stat.color === 'green' ? 'bg-green-100' :
                  stat.color === 'indigo' ? 'bg-indigo-100' :
                  stat.color === 'purple' ? 'bg-purple-100' :
                  stat.color === 'orange' ? 'bg-orange-100' :
                  'bg-gray-100'
                }`}>
                  <stat.icon className={`h-6 w-6 ${
                    stat.color === 'blue' ? 'text-blue-600' :
                    stat.color === 'green' ? 'text-green-600' :
                    stat.color === 'indigo' ? 'text-indigo-600' :
                    stat.color === 'purple' ? 'text-purple-600' :
                    stat.color === 'orange' ? 'text-orange-600' :
                    'text-gray-600'
                  }`} />
                </div>
              </div>
            </div>
          ))}
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Transactions */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-lg border border-gray-200"
            >
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">المعاملات الحديثة</h3>
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    عرض الكل
                  </button>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                    >
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <div className={`p-2 rounded-full ${
                          transaction.type === 'income' 
                            ? 'bg-green-100' 
                            : 'bg-red-100'
                        }`}>
                          {transaction.type === 'income' ? (
                            <ArrowDownIcon className="h-4 w-4 text-green-600" />
                          ) : (
                            <ArrowUpIcon className="h-4 w-4 text-red-600" />
                          )}
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <h4 className="font-medium text-gray-900">
                              {transaction.description}
                            </h4>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              transaction.status === 'completed' ? 'text-green-600 bg-green-50' :
                              transaction.status === 'pending' ? 'text-yellow-600 bg-yellow-50' :
                              'text-red-600 bg-red-50'
                            }`}>
                              {getStatusIcon(transaction.status)}
                              <span className="mr-1">{getStatusText(transaction.status)}</span>
                            </span>
                          </div>
                          
                          <div className="flex items-center space-x-4 space-x-reverse mt-1 text-sm text-gray-500">
                            {transaction.member && (
                              <>
                                <span>{transaction.member}</span>
                                <span>•</span>
                              </>
                            )}
                            <span>{transaction.date}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 space-x-reverse">
                        <div className="text-left">
                          <p className={`font-semibold ${
                            transaction.type === 'income' 
                              ? 'text-green-600' 
                              : 'text-red-600'
                          }`}>
                            {transaction.type === 'income' ? '+' : '-'}{transaction.amount.toLocaleString()} ريال
                          </p>
                          <p className="text-xs text-gray-500">
                            {transaction.id}
                          </p>
                        </div>
                        
                        <button className="p-2 text-gray-400 hover:text-gray-600">
                          <EyeIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>

          {/* Quick Actions */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white rounded-lg border border-gray-200"
            >
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <PlusIcon className="h-5 w-5 ml-2" />
                  إجراءات سريعة
                </h3>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {quickActions.map((action, index) => (
                    <Link key={index} href={action.href}>
                      <div className={`
                        p-4 rounded-lg border transition-all duration-200 cursor-pointer
                        hover:shadow-sm border-gray-200 ${
                          action.color === 'blue' ? 'hover:border-blue-200' :
                          action.color === 'green' ? 'hover:border-green-200' :
                          action.color === 'purple' ? 'hover:border-purple-200' :
                          action.color === 'orange' ? 'hover:border-orange-200' :
                          action.color === 'indigo' ? 'hover:border-indigo-200' :
                          'hover:border-gray-300'
                        }
                      `}>
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <div className={`
                            p-2 rounded-lg ${
                              action.color === 'blue' ? 'bg-blue-100' :
                              action.color === 'green' ? 'bg-green-100' :
                              action.color === 'purple' ? 'bg-purple-100' :
                              action.color === 'orange' ? 'bg-orange-100' :
                              action.color === 'indigo' ? 'bg-indigo-100' :
                              'bg-gray-100'
                            }
                          `}>
                            <action.icon className={`h-5 w-5 ${
                              action.color === 'blue' ? 'text-blue-600' :
                              action.color === 'green' ? 'text-green-600' :
                              action.color === 'purple' ? 'text-purple-600' :
                              action.color === 'orange' ? 'text-orange-600' :
                              action.color === 'indigo' ? 'text-indigo-600' :
                              'text-gray-600'
                            }`} />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900">
                              {action.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">
                              {action.description}
                            </p>
                          </div>
                          <div className="text-gray-400">
                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
