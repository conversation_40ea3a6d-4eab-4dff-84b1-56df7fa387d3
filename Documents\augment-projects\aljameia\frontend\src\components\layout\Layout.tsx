import { ReactNode } from 'react';
import { useTranslation } from 'next-i18next';
import Header from './Header';
import Footer from './Footer';
import Sidebar from './Sidebar';
import { useAuth } from '@/hooks/useAuth';

interface LayoutProps {
  children: ReactNode;
  showSidebar?: boolean;
  className?: string;
}

const Layout = ({ children, showSidebar = false, className = '' }: LayoutProps) => {
  const { t } = useTranslation('common');
  const { user, isAuthenticated } = useAuth();

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      <Header />
      
      <div className="flex">
        {showSidebar && isAuthenticated && (
          <Sidebar />
        )}
        
        <main className={`flex-1 ${showSidebar && isAuthenticated ? 'ml-64' : ''}`}>
          {children}
        </main>
      </div>
      
      <Footer />
    </div>
  );
};

export default Layout;
