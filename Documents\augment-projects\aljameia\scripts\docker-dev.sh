#!/bin/bash

# ==============================================
# Docker Development Script for Aljameia
# ==============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose is not installed. Please install it and try again."
        exit 1
    fi
    log_success "docker-compose is available"
}

# Create .env file if it doesn't exist
setup_env() {
    if [ ! -f .env ]; then
        log_info "Creating .env file from .env.example"
        cp .env.example .env
        log_warning "Please update the .env file with your configuration"
    else
        log_success ".env file exists"
    fi
}

# Build and start development environment
start_dev() {
    log_info "Starting development environment..."
    
    # Build images
    log_info "Building Docker images..."
    docker-compose build
    
    # Start services
    log_info "Starting services..."
    docker-compose up -d mongodb redis
    
    # Wait for database to be ready
    log_info "Waiting for MongoDB to be ready..."
    sleep 10
    
    # Start backend
    log_info "Starting backend service..."
    docker-compose up -d backend
    
    # Wait for backend to be ready
    log_info "Waiting for backend to be ready..."
    sleep 5
    
    # Start frontend services
    log_info "Starting frontend services..."
    docker-compose up -d admin-dashboard nginx
    
    log_success "Development environment started successfully!"
    log_info "Services available at:"
    echo "  - API: http://localhost:3000"
    echo "  - Admin Dashboard: http://localhost:3001"
    echo "  - Nginx: http://localhost:80"
    echo "  - MongoDB: localhost:27017"
    echo "  - Redis: localhost:6379"
}

# Stop development environment
stop_dev() {
    log_info "Stopping development environment..."
    docker-compose down
    log_success "Development environment stopped"
}

# Restart development environment
restart_dev() {
    log_info "Restarting development environment..."
    stop_dev
    start_dev
}

# Show logs
show_logs() {
    local service=${1:-""}
    if [ -z "$service" ]; then
        log_info "Showing logs for all services..."
        docker-compose logs -f
    else
        log_info "Showing logs for $service..."
        docker-compose logs -f "$service"
    fi
}

# Clean up Docker resources
cleanup() {
    log_info "Cleaning up Docker resources..."
    
    # Stop and remove containers
    docker-compose down -v
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    # Remove unused networks
    docker network prune -f
    
    log_success "Cleanup completed"
}

# Run database migrations
migrate() {
    log_info "Running database migrations..."
    docker-compose exec backend npm run migrate
    log_success "Migrations completed"
}

# Seed database with sample data
seed() {
    log_info "Seeding database with sample data..."
    docker-compose exec backend npm run seed
    log_success "Database seeded"
}

# Run tests
test() {
    log_info "Running tests..."
    docker-compose exec backend npm test
}

# Access backend shell
shell() {
    log_info "Accessing backend shell..."
    docker-compose exec backend /bin/sh
}

# Monitor services
monitor() {
    log_info "Monitoring services..."
    watch -n 2 'docker-compose ps'
}

# Backup database
backup() {
    local backup_name="aljameia_backup_$(date +%Y%m%d_%H%M%S)"
    log_info "Creating database backup: $backup_name"
    
    docker-compose exec mongodb mongodump \
        --host localhost:27017 \
        --db aljameia \
        --username admin \
        --password password123 \
        --authenticationDatabase admin \
        --out /data/backups/$backup_name
    
    log_success "Backup created: $backup_name"
}

# Restore database
restore() {
    local backup_path=$1
    if [ -z "$backup_path" ]; then
        log_error "Please provide backup path"
        exit 1
    fi
    
    log_info "Restoring database from: $backup_path"
    
    docker-compose exec mongodb mongorestore \
        --host localhost:27017 \
        --db aljameia \
        --username admin \
        --password password123 \
        --authenticationDatabase admin \
        --drop \
        $backup_path
    
    log_success "Database restored"
}

# Show help
show_help() {
    echo "Aljameia Docker Development Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start       Start development environment"
    echo "  stop        Stop development environment"
    echo "  restart     Restart development environment"
    echo "  logs [svc]  Show logs (optionally for specific service)"
    echo "  cleanup     Clean up Docker resources"
    echo "  migrate     Run database migrations"
    echo "  seed        Seed database with sample data"
    echo "  test        Run tests"
    echo "  shell       Access backend shell"
    echo "  monitor     Monitor services status"
    echo "  backup      Create database backup"
    echo "  restore     Restore database from backup"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs backend"
    echo "  $0 restore /data/backups/backup_name"
}

# Main script
main() {
    local command=${1:-"help"}
    
    # Check prerequisites
    check_docker
    check_docker_compose
    setup_env
    
    case $command in
        "start")
            start_dev
            ;;
        "stop")
            stop_dev
            ;;
        "restart")
            restart_dev
            ;;
        "logs")
            show_logs $2
            ;;
        "cleanup")
            cleanup
            ;;
        "migrate")
            migrate
            ;;
        "seed")
            seed
            ;;
        "test")
            test
            ;;
        "shell")
            shell
            ;;
        "monitor")
            monitor
            ;;
        "backup")
            backup
            ;;
        "restore")
            restore $2
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
