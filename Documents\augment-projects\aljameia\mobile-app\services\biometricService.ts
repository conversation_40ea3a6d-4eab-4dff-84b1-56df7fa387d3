import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Biometric Types
export interface BiometricSettings {
  enabled: boolean;
  type: 'fingerprint' | 'face' | 'iris' | 'none';
  fallbackToPassword: boolean;
  autoLockTimeout: number; // in minutes
}

export interface BiometricCapabilities {
  isAvailable: boolean;
  supportedTypes: LocalAuthentication.AuthenticationType[];
  isEnrolled: boolean;
  securityLevel: LocalAuthentication.SecurityLevel;
}

// Storage Keys
const STORAGE_KEYS = {
  BIOMETRIC_SETTINGS: 'biometric_settings',
  BIOMETRIC_CREDENTIALS: 'biometric_credentials',
  LAST_AUTH_TIME: 'last_auth_time',
  AUTO_LOCK_ENABLED: 'auto_lock_enabled',
};

// Default Settings
const DEFAULT_SETTINGS: BiometricSettings = {
  enabled: false,
  type: 'none',
  fallbackToPassword: true,
  autoLockTimeout: 5, // 5 minutes
};

// Biometric Service Class
class BiometricService {
  private settings: BiometricSettings = DEFAULT_SETTINGS;
  private lastAuthTime: number = 0;

  /**
   * Initialize biometric service
   */
  async initialize(): Promise<void> {
    try {
      await this.loadSettings();
      await this.updateLastAuthTime();
    } catch (error) {
      console.error('Failed to initialize biometric service:', error);
    }
  }

  /**
   * Check biometric capabilities
   */
  async getCapabilities(): Promise<BiometricCapabilities> {
    try {
      const isAvailable = await LocalAuthentication.hasHardwareAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const securityLevel = await LocalAuthentication.getEnrolledLevelAsync();

      return {
        isAvailable,
        supportedTypes,
        isEnrolled,
        securityLevel,
      };
    } catch (error) {
      console.error('Failed to get biometric capabilities:', error);
      return {
        isAvailable: false,
        supportedTypes: [],
        isEnrolled: false,
        securityLevel: LocalAuthentication.SecurityLevel.NONE,
      };
    }
  }

  /**
   * Authenticate with biometrics
   */
  async authenticate(reason?: string): Promise<{
    success: boolean;
    error?: string;
    warning?: string;
  }> {
    try {
      const capabilities = await this.getCapabilities();
      
      if (!capabilities.isAvailable) {
        return {
          success: false,
          error: 'المصادقة البيومترية غير متوفرة على هذا الجهاز',
        };
      }

      if (!capabilities.isEnrolled) {
        return {
          success: false,
          error: 'لم يتم تسجيل بيانات بيومترية على هذا الجهاز',
        };
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: reason || 'تأكيد الهوية',
        cancelLabel: 'إلغاء',
        fallbackLabel: 'استخدام كلمة المرور',
        disableDeviceFallback: !this.settings.fallbackToPassword,
      });

      if (result.success) {
        await this.updateLastAuthTime();
        return { success: true };
      } else {
        let error = 'فشل في المصادقة';
        
        if (result.error === 'user_cancel') {
          error = 'تم إلغاء المصادقة';
        } else if (result.error === 'user_fallback') {
          error = 'تم اختيار كلمة المرور';
        } else if (result.error === 'biometric_unknown_error') {
          error = 'خطأ غير معروف في المصادقة البيومترية';
        } else if (result.error === 'invalid_context') {
          error = 'سياق مصادقة غير صالح';
        }

        return {
          success: false,
          error,
          warning: result.warning,
        };
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return {
        success: false,
        error: 'حدث خطأ أثناء المصادقة البيومترية',
      };
    }
  }

  /**
   * Enable biometric authentication
   */
  async enableBiometric(): Promise<boolean> {
    try {
      const capabilities = await this.getCapabilities();
      
      if (!capabilities.isAvailable || !capabilities.isEnrolled) {
        return false;
      }

      // Test authentication first
      const authResult = await this.authenticate('تفعيل المصادقة البيومترية');
      
      if (authResult.success) {
        // Determine biometric type
        let type: BiometricSettings['type'] = 'none';
        
        if (capabilities.supportedTypes.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
          type = 'fingerprint';
        } else if (capabilities.supportedTypes.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)) {
          type = 'face';
        } else if (capabilities.supportedTypes.includes(LocalAuthentication.AuthenticationType.IRIS)) {
          type = 'iris';
        }

        // Update settings
        await this.updateSettings({
          enabled: true,
          type,
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error('Failed to enable biometric:', error);
      return false;
    }
  }

  /**
   * Disable biometric authentication
   */
  async disableBiometric(): Promise<void> {
    try {
      await this.updateSettings({
        enabled: false,
        type: 'none',
      });

      // Clear stored biometric credentials
      await SecureStore.deleteItemAsync(STORAGE_KEYS.BIOMETRIC_CREDENTIALS);
    } catch (error) {
      console.error('Failed to disable biometric:', error);
    }
  }

  /**
   * Check if biometric is enabled
   */
  isBiometricEnabled(): boolean {
    return this.settings.enabled;
  }

  /**
   * Get biometric type
   */
  getBiometricType(): BiometricSettings['type'] {
    return this.settings.type;
  }

  /**
   * Get biometric type display name
   */
  getBiometricTypeName(): string {
    switch (this.settings.type) {
      case 'fingerprint':
        return 'بصمة الإصبع';
      case 'face':
        return 'التعرف على الوجه';
      case 'iris':
        return 'مسح القزحية';
      default:
        return 'غير محدد';
    }
  }

  /**
   * Check if auto-lock should be triggered
   */
  shouldAutoLock(): boolean {
    if (!this.settings.enabled || this.settings.autoLockTimeout === 0) {
      return false;
    }

    const timeSinceLastAuth = Date.now() - this.lastAuthTime;
    const timeoutMs = this.settings.autoLockTimeout * 60 * 1000;
    
    return timeSinceLastAuth > timeoutMs;
  }

  /**
   * Update last authentication time
   */
  async updateLastAuthTime(): Promise<void> {
    try {
      this.lastAuthTime = Date.now();
      await SecureStore.setItemAsync(
        STORAGE_KEYS.LAST_AUTH_TIME,
        this.lastAuthTime.toString()
      );
    } catch (error) {
      console.error('Failed to update last auth time:', error);
    }
  }

  /**
   * Get biometric settings
   */
  getSettings(): BiometricSettings {
    return { ...this.settings };
  }

  /**
   * Update biometric settings
   */
  async updateSettings(newSettings: Partial<BiometricSettings>): Promise<void> {
    try {
      this.settings = { ...this.settings, ...newSettings };
      await SecureStore.setItemAsync(
        STORAGE_KEYS.BIOMETRIC_SETTINGS,
        JSON.stringify(this.settings)
      );
    } catch (error) {
      console.error('Failed to update biometric settings:', error);
    }
  }

  /**
   * Load settings from secure storage
   */
  private async loadSettings(): Promise<void> {
    try {
      const stored = await SecureStore.getItemAsync(STORAGE_KEYS.BIOMETRIC_SETTINGS);
      if (stored) {
        this.settings = { ...DEFAULT_SETTINGS, ...JSON.parse(stored) };
      }

      const lastAuthStored = await SecureStore.getItemAsync(STORAGE_KEYS.LAST_AUTH_TIME);
      if (lastAuthStored) {
        this.lastAuthTime = parseInt(lastAuthStored, 10);
      }
    } catch (error) {
      console.error('Failed to load biometric settings:', error);
      this.settings = DEFAULT_SETTINGS;
    }
  }

  /**
   * Store credentials securely (for quick access)
   */
  async storeCredentials(credentials: { email: string; token: string }): Promise<void> {
    try {
      if (this.settings.enabled) {
        await SecureStore.setItemAsync(
          STORAGE_KEYS.BIOMETRIC_CREDENTIALS,
          JSON.stringify(credentials)
        );
      }
    } catch (error) {
      console.error('Failed to store biometric credentials:', error);
    }
  }

  /**
   * Retrieve stored credentials
   */
  async getStoredCredentials(): Promise<{ email: string; token: string } | null> {
    try {
      if (!this.settings.enabled) {
        return null;
      }

      const stored = await SecureStore.getItemAsync(STORAGE_KEYS.BIOMETRIC_CREDENTIALS);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Failed to get stored credentials:', error);
      return null;
    }
  }

  /**
   * Clear all biometric data
   */
  async clearAllData(): Promise<void> {
    try {
      await Promise.all([
        SecureStore.deleteItemAsync(STORAGE_KEYS.BIOMETRIC_SETTINGS),
        SecureStore.deleteItemAsync(STORAGE_KEYS.BIOMETRIC_CREDENTIALS),
        SecureStore.deleteItemAsync(STORAGE_KEYS.LAST_AUTH_TIME),
      ]);
      
      this.settings = DEFAULT_SETTINGS;
      this.lastAuthTime = 0;
    } catch (error) {
      console.error('Failed to clear biometric data:', error);
    }
  }
}

// Export singleton instance
export const biometricService = new BiometricService();
export default biometricService;
