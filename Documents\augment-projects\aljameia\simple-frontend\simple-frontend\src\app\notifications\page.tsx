'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import {
  BellIcon,
  EnvelopeIcon,
  DevicePhoneMobileIcon,
  CogIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

interface NotificationSetting {
  id: string;
  type: 'payment_reminder' | 'payment_due' | 'payment_overdue' | 'cycle_complete' | 'new_opportunity';
  title: string;
  description: string;
  email: boolean;
  sms: boolean;
  push: boolean;
  daysBeforeDue?: number;
}

interface Notification {
  id: string;
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionUrl?: string;
}

export default function NotificationsPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('notifications');
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [settings, setSettings] = useState<NotificationSetting[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Load mock data
    setNotifications([
      {
        id: '1',
        type: 'warning',
        title: 'تذكير دفعة مستحقة',
        message: 'دفعة جمعية الأصدقاء مستحقة خلال 3 أيام - 5,000 ريال',
        timestamp: '2024-06-18T10:30:00Z',
        read: false,
        actionUrl: '/subscriptions/1'
      },
      {
        id: '2',
        type: 'success',
        title: 'تم استلام الدفعة',
        message: 'تم استلام دفعة جمعية العائلة بنجاح - 3,000 ريال',
        timestamp: '2024-06-17T14:20:00Z',
        read: true
      },
      {
        id: '3',
        type: 'info',
        title: 'فرصة استثمار جديدة',
        message: 'جمعية المهنيين الجديدة متاحة للانضمام - عائد متوقع 20%',
        timestamp: '2024-06-16T09:15:00Z',
        read: false,
        actionUrl: '/jamiyas/4'
      },
      {
        id: '4',
        type: 'error',
        title: 'دفعة متأخرة',
        message: 'دفعة جمعية الأحياء متأخرة بـ 5 أيام - يرجى الدفع فوراً',
        timestamp: '2024-06-15T16:45:00Z',
        read: false,
        actionUrl: '/subscriptions/payments'
      }
    ]);

    setSettings([
      {
        id: '1',
        type: 'payment_reminder',
        title: 'تذكير الدفعات',
        description: 'تذكير قبل موعد استحقاق الدفعة',
        email: true,
        sms: true,
        push: true,
        daysBeforeDue: 3
      },
      {
        id: '2',
        type: 'payment_due',
        title: 'دفعة مستحقة',
        description: 'إشعار عند حلول موعد الدفعة',
        email: true,
        sms: false,
        push: true
      },
      {
        id: '3',
        type: 'payment_overdue',
        title: 'دفعة متأخرة',
        description: 'تنبيه عند تأخر الدفعة',
        email: true,
        sms: true,
        push: true
      },
      {
        id: '4',
        type: 'cycle_complete',
        title: 'اكتمال الدورة',
        description: 'إشعار عند اكتمال دورة الجمعية',
        email: true,
        sms: false,
        push: true
      },
      {
        id: '5',
        type: 'new_opportunity',
        title: 'فرص استثمار جديدة',
        description: 'إشعار بالجمعيات الجديدة المتاحة',
        email: false,
        sms: false,
        push: true
      }
    ]);
  }, []);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="h-6 w-6 text-green-600" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />;
      case 'error':
        return <XCircleIcon className="h-6 w-6 text-red-600" />;
      default:
        return <InformationCircleIcon className="h-6 w-6 text-blue-600" />;
    }
  };

  const getNotificationBgColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-blue-50 border-blue-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'منذ قليل';
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `منذ ${diffInDays} يوم`;
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const updateSetting = (settingId: string, field: string, value: boolean | number) => {
    setSettings(prev => 
      prev.map(setting => 
        setting.id === settingId ? { ...setting, [field]: value } : setting
      )
    );
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <ProtectedRoute requiredPermissions={['notifications']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">الإشعارات والتنبيهات</h1>
                <p className="text-gray-600 mt-1">
                  إدارة إشعاراتك وتخصيص التنبيهات حسب احتياجاتك
                </p>
              </div>
              <div className="flex items-center space-x-4 space-x-reverse">
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <CheckCircleIcon className="h-5 w-5 ml-2" />
                    تحديد الكل كمقروء
                  </button>
                )}
                <div className="relative">
                  <BellIcon className="h-8 w-8 text-gray-600" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {unreadCount}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Tabs */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 space-x-reverse px-6">
                <button
                  onClick={() => setActiveTab('notifications')}
                  className={`
                    py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${activeTab === 'notifications'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <div className="flex items-center">
                    <BellIcon className="h-5 w-5 ml-2" />
                    الإشعارات
                    {unreadCount > 0 && (
                      <span className="mr-2 bg-red-100 text-red-600 text-xs rounded-full px-2 py-1">
                        {unreadCount}
                      </span>
                    )}
                  </div>
                </button>
                <button
                  onClick={() => setActiveTab('settings')}
                  className={`
                    py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${activeTab === 'settings'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <div className="flex items-center">
                    <CogIcon className="h-5 w-5 ml-2" />
                    إعدادات التنبيهات
                  </div>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
