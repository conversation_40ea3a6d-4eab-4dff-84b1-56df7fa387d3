package com.aljameia.app.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
// import dagger.hilt.android.AndroidEntryPoint

// @AndroidEntryPoint
class NetworkReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == ConnectivityManager.CONNECTIVITY_ACTION) {
            // Handle network connectivity changes
            // TODO: Implement network state handling
        }
    }
}
