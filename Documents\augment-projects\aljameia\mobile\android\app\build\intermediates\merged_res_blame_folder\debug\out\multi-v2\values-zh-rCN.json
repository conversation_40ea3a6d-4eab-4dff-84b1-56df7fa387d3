{"logs": [{"outputFile": "com.aljameia.app-mergeDebugResources-104:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\cdc55fe2502a063bcee343c082439e6f\\transformed\\navigation-ui-2.7.6\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "15756,15857", "endColumns": "100,102", "endOffsets": "15852,15955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\85fae80ff7869a8da6251422548fb462\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,343,434,511,585,662,740,815,880,945,1018,1093,1161,1234,1300", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "177,253,338,429,506,580,657,735,810,875,940,1013,1088,1156,1229,1295,1411"}, "to": {"startLines": "52,53,106,108,110,128,129,188,189,190,191,193,194,197,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4528,4605,9763,9929,10089,11730,11804,15960,16038,16113,16178,16312,16385,16618,16953,17026,17092", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "4600,4676,9843,10015,10161,11799,11876,16033,16108,16173,16238,16380,16455,16681,17021,17087,17203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b3e74a7425b14daa5ccd7c3c1ea8df2a\\transformed\\browser-1.4.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "74,114,115,116", "startColumns": "4,4,4,4", "startOffsets": "6665,10376,10468,10569", "endColumns": "82,91,100,92", "endOffsets": "6743,10463,10564,10657"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f1a3dac0af0a85f165dc53a6762b4a81\\transformed\\play-services-basement-18.2.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "5615", "endColumns": "98", "endOffsets": "5709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\21cc0a6be925d3c2a0c747bb469e8733\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,16539", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,16613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d2a56f6da0e2e4ab4da1b326f38c566a\\transformed\\material-1.11.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,922,999,1058,1117,1195,1256,1313,1369,1428,1486,1540,1625,1681,1739,1793,1858,1950,2024,2100,2222,2284,2346,2445,2524,2598,2648,2699,2765,2829,2898,2976,3047,3108,3179,3246,3306,3392,3471,3538,3621,3706,3780,3845,3921,3969,4042,4106,4182,4260,4322,4386,4449,4514,4594,4670,4748,4824,4878,4933", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "242,306,368,438,508,585,676,782,855,917,994,1053,1112,1190,1251,1308,1364,1423,1481,1535,1620,1676,1734,1788,1853,1945,2019,2095,2217,2279,2341,2440,2519,2593,2643,2694,2760,2824,2893,2971,3042,3103,3174,3241,3301,3387,3466,3533,3616,3701,3775,3840,3916,3964,4037,4101,4177,4255,4317,4381,4444,4509,4589,4665,4743,4819,4873,4928,4997"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3251,3315,3377,3447,3517,4258,4349,4455,10166,10299,11671,11881,11940,12018,12079,12136,12192,12251,12309,12363,12448,12504,12562,12616,12681,12773,12847,12923,13045,13107,13169,13268,13347,13421,13471,13522,13588,13652,13721,13799,13870,13931,14002,14069,14129,14215,14294,14361,14444,14529,14603,14668,14744,14792,14865,14929,15005,15083,15145,15209,15272,15337,15417,15493,15571,15647,15701,16243", "endLines": "5,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "292,3310,3372,3442,3512,3589,4344,4450,4523,10223,10371,11725,11935,12013,12074,12131,12187,12246,12304,12358,12443,12499,12557,12611,12676,12768,12842,12918,13040,13102,13164,13263,13342,13416,13466,13517,13583,13647,13716,13794,13865,13926,13997,14064,14124,14210,14289,14356,14439,14524,14598,14663,14739,14787,14860,14924,15000,15078,15140,15204,15267,15332,15412,15488,15566,15642,15696,15751,16307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\403ef3e018096a306df17d01c38df724\\transformed\\zxing-android-embedded-4.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,104,151,238", "endColumns": "48,46,86,68", "endOffsets": "99,146,233,302"}, "to": {"startLines": "218,219,220,221", "startColumns": "4,4,4,4", "startOffsets": "18434,18483,18530,18617", "endColumns": "48,46,86,68", "endOffsets": "18478,18525,18612,18681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c514f303830aa824ccabc1313adde6e2\\transformed\\material3-1.1.2\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,607,707,811,912,1023,1101,1193,1273,1358,1460,1570,1667,1771,1873,1977,2089,2192,2289,2390,2493,2574,2665,2766,2871,2957,3051,3145,3248,3357,3453,3539,3608,3679,3758,3836,3924,4000,4077,4171,4261,4350,4441,4520,4612,4704,4796,4900,4976,5064", "endColumns": "99,98,99,97,71,82,99,103,100,110,77,91,79,84,101,109,96,103,101,103,111,102,96,100,102,80,90,100,104,85,93,93,102,108,95,85,68,70,78,77,87,75,76,93,89,88,90,78,91,91,91,103,75,87,85", "endOffsets": "150,249,349,447,519,602,702,806,907,1018,1096,1188,1268,1353,1455,1565,1662,1766,1868,1972,2084,2187,2284,2385,2488,2569,2660,2761,2866,2952,3046,3140,3243,3352,3448,3534,3603,3674,3753,3831,3919,3995,4072,4166,4256,4345,4436,4515,4607,4699,4791,4895,4971,5059,5145"}, "to": {"startLines": "33,34,35,36,54,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,109,112,195,198,200,204,205,206,207,208,209,210,211,212,213,214,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,2954,3053,3153,4681,6748,6831,6931,7035,7136,7247,7325,7417,7497,7582,7684,7794,7891,7995,8097,8201,8313,8416,8513,8614,8717,8798,8889,8990,9095,9181,9275,9369,9472,9581,9677,10020,10228,16460,16686,16865,17208,17284,17361,17455,17545,17634,17725,17804,17896,17988,18080,18184,18260,18348", "endColumns": "99,98,99,97,71,82,99,103,100,110,77,91,79,84,101,109,96,103,101,103,111,102,96,100,102,80,90,100,104,85,93,93,102,108,95,85,68,70,78,77,87,75,76,93,89,88,90,78,91,91,91,103,75,87,85", "endOffsets": "2949,3048,3148,3246,4748,6826,6926,7030,7131,7242,7320,7412,7492,7577,7679,7789,7886,7990,8092,8196,8308,8411,8508,8609,8712,8793,8884,8985,9090,9176,9270,9364,9467,9576,9672,9758,10084,10294,16534,16759,16948,17279,17356,17450,17540,17629,17720,17799,17891,17983,18075,18179,18255,18343,18429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1c0b3a48f4d4c74d31971bddddc3d282\\transformed\\play-services-base-18.1.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4753,4854,4983,5098,5200,5305,5421,5523,5714,5822,5923,6053,6168,6272,6380,6436,6493", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "4849,4978,5093,5195,5300,5416,5518,5610,5817,5918,6048,6163,6267,6375,6431,6488,6562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\523b9fad2b83d89c2b74860689d8fb91\\transformed\\biometric-1.1.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,234,333,436,538,636,737,838,928,1040,1146", "endColumns": "97,80,98,102,101,97,100,100,89,111,105,96", "endOffsets": "148,229,328,431,533,631,732,833,923,1035,1141,1238"}, "to": {"startLines": "73,107,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6567,9848,10662,10761,10864,10966,11064,11165,11266,11356,11468,11574", "endColumns": "97,80,98,102,101,97,100,100,89,111,105,96", "endOffsets": "6660,9924,10756,10859,10961,11059,11160,11261,11351,11463,11569,11666"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5081b8eacd451e5b41f2b566f41c579e\\transformed\\core-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "42,43,44,45,46,47,48,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3594,3686,3787,3881,3975,4068,4162,16764", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3681,3782,3876,3970,4063,4157,4253,16860"}}]}]}