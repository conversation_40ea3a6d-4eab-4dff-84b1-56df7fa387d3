"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"index,follow\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"googlebot\",\n                        content: \"index,follow\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1d4ed8\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#1d4ed8\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://api.aljameia.com\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap\",\n                        rel: \"stylesheet\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"application-name\",\n                        content: \"Al-Jameia Financial Association\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"Al-Jameia\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-config\",\n                        content: \"/browserconfig.xml\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-tap-highlight\",\n                        content: \"no\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Content-Type-Options\",\n                        content: \"nosniff\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Frame-Options\",\n                        content: \"DENY\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-XSS-Protection\",\n                        content: \"1; mode=block\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"Referrer-Policy\",\n                        content: \"strict-origin-when-cross-origin\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.googleapis.com\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.gstatic.com\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//api.aljameia.com\",\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"3d638a474d4b4ce4\",\n                        children: \".jsx-3d638a474d4b4ce4:root{--color-primary-50:#eff6ff;--color-primary-100:#dbeafe;--color-primary-200:#bfdbfe;--color-primary-300:#93c5fd;--color-primary-400:#60a5fa;--color-primary-500:#3b82f6;--color-primary-600:#2563eb;--color-primary-700:#1d4ed8;--color-primary-800:#1e40af;--color-primary-900:#1e3a8a;--color-gray-50:#f9fafb;--color-gray-100:#f3f4f6;--color-gray-200:#e5e7eb;--color-gray-300:#d1d5db;--color-gray-400:#9ca3af;--color-gray-500:#6b7280;--color-gray-600:#4b5563;--color-gray-700:#374151;--color-gray-800:#1f2937;--color-gray-900:#111827;--color-success:#10b981;--color-warning:#f59e0b;--color-error:#ef4444;--color-info:#3b82f6;--font-arabic:'Noto Sans Arabic', 'Cairo', 'Amiri', sans-serif;--font-english:'Inter', 'Roboto', 'Helvetica Neue', sans-serif;--font-display:'Amiri', 'Georgia', serif;--space-1:0.25rem;--space-2:0.5rem;--space-3:0.75rem;--space-4:1rem;--space-5:1.25rem;--space-6:1.5rem;--space-8:2rem;--space-10:2.5rem;--space-12:3rem;--space-16:4rem;--space-20:5rem;--space-24:6rem;--shadow-sm:0 1px 2px 0 rgba(0, 0, 0, 0.05);--shadow-md:0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);--shadow-lg:0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);--shadow-xl:0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);--radius-sm:0.125rem;--radius-md:0.375rem;--radius-lg:0.5rem;--radius-xl:0.75rem;--radius-2xl:1rem;--radius-full:9999px;--transition-fast:150ms ease-in-out;--transition-normal:250ms ease-in-out;--transition-slow:350ms ease-in-out;--z-dropdown:1000;--z-sticky:1020;--z-fixed:1030;--z-modal-backdrop:1040;--z-modal:1050;--z-popover:1060;--z-tooltip:1070;--z-toast:1080}@media(prefers-color-scheme:dark){.jsx-3d638a474d4b4ce4:root{--color-gray-50:#111827;--color-gray-100:#1f2937;--color-gray-200:#374151;--color-gray-300:#4b5563;--color-gray-400:#6b7280;--color-gray-500:#9ca3af;--color-gray-600:#d1d5db;--color-gray-700:#e5e7eb;--color-gray-800:#f3f4f6;--color-gray-900:#f9fafb}}*.jsx-3d638a474d4b4ce4{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}html.jsx-3d638a474d4b4ce4{scroll-behavior:smooth;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}body.jsx-3d638a474d4b4ce4{margin:0;padding:0;font-family:var(--font-english);line-height:1.6;color:var(--color-gray-900);background-color:var(--color-gray-50);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility}[dir=\\\"rtl\\\"].jsx-3d638a474d4b4ce4 body.jsx-3d638a474d4b4ce4{font-family:var(--font-arabic)}*.jsx-3d638a474d4b4ce4:focus{outline:2px solid var(--color-primary-500);outline-offset:2px}.loading.jsx-3d638a474d4b4ce4{opacity:.6;pointer-events:none}@media(prefers-reduced-motion:reduce){*.jsx-3d638a474d4b4ce4{-webkit-animation-duration:.01ms!important;-moz-animation-duration:.01ms!important;-o-animation-duration:.01ms!important;animation-duration:.01ms!important;-webkit-animation-iteration-count:1!important;-moz-animation-iteration-count:1!important;-o-animation-iteration-count:1!important;animation-iteration-count:1!important;-webkit-transition-duration:.01ms!important;-moz-transition-duration:.01ms!important;-o-transition-duration:.01ms!important;transition-duration:.01ms!important}}\"\n                    }, void 0, false, void 0, this),\n                    process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                                async: true,\n                                src: `https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID}`,\n                                className: \"jsx-3d638a474d4b4ce4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                                dangerouslySetInnerHTML: {\n                                    __html: `\n                  window.dataLayer = window.dataLayer || [];\n                  function gtag(){dataLayer.push(arguments);}\n                  gtag('js', new Date());\n                  gtag('config', '${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID}', {\n                    page_path: window.location.pathname,\n                    anonymize_ip: true,\n                    cookie_flags: 'SameSite=Strict;Secure'\n                  });\n                `\n                                },\n                                className: \"jsx-3d638a474d4b4ce4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n                window.SENTRY_DSN = '${\"your_sentry_dsn\"}';\n              `\n                        },\n                        className: \"jsx-3d638a474d4b4ce4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noscript\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"fixed\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                bottom: 0,\n                                backgroundColor: \"#f9fafb\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                flexDirection: \"column\",\n                                textAlign: \"center\",\n                                padding: \"2rem\",\n                                zIndex: 9999\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        color: \"#1f2937\",\n                                        marginBottom: \"1rem\"\n                                    },\n                                    children: \"JavaScript Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: \"#6b7280\",\n                                        maxWidth: \"400px\"\n                                    },\n                                    children: \"This application requires JavaScript to function properly. Please enable JavaScript in your browser settings and reload the page.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              if ('serviceWorker' in navigator && window.location.protocol === 'https:') {\n                navigator.serviceWorker.register('/sw.js')\n                  .then(function(registration) {\n                    console.log('SW registered: ', registration);\n                  })\n                  .catch(function(registrationError) {\n                    console.log('SW registration failed: ', registrationError);\n                  });\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./src/pages/_document.tsx")));
module.exports = __webpack_exports__;

})();