# ==============================================
# Aljameia Platform Production Environment
# ==============================================

# Environment
NODE_ENV=production
COMPOSE_PROJECT_NAME=aljameia-prod

# ==============================================
# Database Configuration
# ==============================================

# MongoDB
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=CHANGE_THIS_SECURE_PASSWORD_IN_PRODUCTION
MONGO_DATABASE=aljameia

# Redis
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD_IN_PRODUCTION

# ==============================================
# Application Ports
# ==============================================

BACKEND_PORT=3000
FRONTEND_PORT=3001
ADMIN_PORT=3002
HTTP_PORT=80
HTTPS_PORT=443

# ==============================================
# Security & Authentication (CHANGE ALL IN PRODUCTION!)
# ==============================================

# JWT Secrets (MUST BE CHANGED!)
JWT_SECRET=CHANGE_THIS_TO_A_VERY_SECURE_JWT_SECRET_MINIMUM_32_CHARACTERS
JWT_REFRESH_SECRET=CHANGE_THIS_TO_A_VERY_SECURE_REFRESH_SECRET_MINIMUM_32_CHARACTERS

# Encryption Key (32 characters)
ENCRYPTION_KEY=CHANGE_THIS_32_CHARACTER_ENCRYPTION_KEY

# ==============================================
# Application URLs (CHANGE TO YOUR DOMAIN)
# ==============================================

API_BASE_URL=https://api.aljameia.com
FRONTEND_URL=https://aljameia.com
ADMIN_URL=https://admin.aljameia.com

# Next.js Frontend URLs
NEXT_PUBLIC_API_URL=https://api.aljameia.com/api/v1
NEXT_PUBLIC_WS_URL=wss://api.aljameia.com

# ==============================================
# Payment Gateway Configuration
# ==============================================

# Saudi Payment Gateways
PAYMENT_GATEWAY_URL=https://api.payment-gateway.com
PAYMENT_GATEWAY_KEY=YOUR_PRODUCTION_PAYMENT_GATEWAY_KEY
PAYMENT_GATEWAY_SECRET=YOUR_PRODUCTION_PAYMENT_GATEWAY_SECRET

# Stripe (International)
STRIPE_SECRET_KEY=sk_live_YOUR_STRIPE_SECRET_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_WEBHOOK_SECRET
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_STRIPE_PUBLISHABLE_KEY

# Mada/STC Pay Configuration
MADA_MERCHANT_ID=YOUR_MADA_MERCHANT_ID
MADA_SECRET_KEY=YOUR_MADA_SECRET_KEY
STC_PAY_MERCHANT_ID=YOUR_STC_PAY_MERCHANT_ID
STC_PAY_SECRET_KEY=YOUR_STC_PAY_SECRET_KEY

# ==============================================
# Email Configuration
# ==============================================

# SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=YOUR_EMAIL_PASSWORD

# SendGrid (Alternative)
SENDGRID_API_KEY=YOUR_SENDGRID_API_KEY
SENDGRID_FROM_EMAIL=<EMAIL>

# ==============================================
# SMS & Phone Configuration
# ==============================================

# Twilio
TWILIO_ACCOUNT_SID=YOUR_TWILIO_ACCOUNT_SID
TWILIO_AUTH_TOKEN=YOUR_TWILIO_AUTH_TOKEN
TWILIO_PHONE_NUMBER=+966XXXXXXXXX

# Saudi SMS Provider
SMS_PROVIDER_URL=https://api.sms-provider.com
SMS_PROVIDER_KEY=YOUR_SMS_PROVIDER_KEY
SMS_PROVIDER_SENDER=ALJAMEIA

# ==============================================
# Push Notifications
# ==============================================

# Firebase Cloud Messaging
FCM_SERVER_KEY=YOUR_FCM_SERVER_KEY
FCM_PROJECT_ID=YOUR_FIREBASE_PROJECT_ID

# Expo Push Notifications
EXPO_ACCESS_TOKEN=YOUR_EXPO_ACCESS_TOKEN

# ==============================================
# File Storage
# ==============================================

# AWS S3
AWS_ACCESS_KEY_ID=YOUR_AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY=YOUR_AWS_SECRET_ACCESS_KEY
AWS_REGION=us-east-1
AWS_S3_BUCKET=aljameia-files-prod

# ==============================================
# External APIs
# ==============================================

# Google Services
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=YOUR_GOOGLE_MAPS_API_KEY
GOOGLE_CLIENT_ID=YOUR_GOOGLE_OAUTH_CLIENT_ID
GOOGLE_CLIENT_SECRET=YOUR_GOOGLE_OAUTH_CLIENT_SECRET

# Apple Services
APPLE_CLIENT_ID=YOUR_APPLE_CLIENT_ID
APPLE_TEAM_ID=YOUR_APPLE_TEAM_ID
APPLE_KEY_ID=YOUR_APPLE_KEY_ID
APPLE_PRIVATE_KEY=YOUR_APPLE_PRIVATE_KEY

# ==============================================
# Monitoring & Analytics
# ==============================================

# Grafana
GRAFANA_PASSWORD=CHANGE_THIS_GRAFANA_PASSWORD

# Sentry (Error Tracking)
SENTRY_DSN=YOUR_SENTRY_DSN
SENTRY_AUTH_TOKEN=YOUR_SENTRY_AUTH_TOKEN

# Google Analytics
NEXT_PUBLIC_GA_TRACKING_ID=GA-XXXXXXXXX

# ==============================================
# Production Settings
# ==============================================

# SSL Certificates
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/private.key

# Domain Configuration
DOMAIN=aljameia.com
API_DOMAIN=api.aljameia.com
ADMIN_DOMAIN=admin.aljameia.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=CHANGE_THIS_SESSION_SECRET_KEY
SESSION_TIMEOUT=3600000

# ==============================================
# Backup & Recovery
# ==============================================

# Database Backup
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=aljameia-backups-prod

# ==============================================
# Compliance & Security
# ==============================================

# PCI DSS Compliance
PCI_COMPLIANCE_MODE=true
AUDIT_LOG_ENABLED=true

# Data Encryption
DATA_ENCRYPTION_ENABLED=true
FIELD_LEVEL_ENCRYPTION=true

# GDPR Compliance
GDPR_COMPLIANCE_MODE=true
DATA_RETENTION_DAYS=2555

# ==============================================
# Feature Flags
# ==============================================

# Features
ENABLE_BIOMETRIC_AUTH=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=true
ENABLE_REAL_TIME_UPDATES=true
ENABLE_ANALYTICS=true
ENABLE_AUDIT_LOGS=true

# Payment Features
ENABLE_MADA_PAYMENTS=true
ENABLE_STC_PAY=true
ENABLE_APPLE_PAY=true
ENABLE_BANK_TRANSFER=true

# ==============================================
# Performance
# ==============================================

# Cache Settings
CACHE_TTL=3600
REDIS_CACHE_TTL=1800

# Database Connection Pool
DB_POOL_SIZE=20
DB_CONNECTION_TIMEOUT=30000

# API Rate Limiting
API_RATE_LIMIT=1000
API_BURST_LIMIT=100

# ==============================================
# Docker Configuration
# ==============================================

# Docker Registry
DOCKER_REGISTRY=aljameia
VERSION=latest

# Resource Limits
MONGODB_MEMORY_LIMIT=2G
REDIS_MEMORY_LIMIT=512M
BACKEND_MEMORY_LIMIT=1G
FRONTEND_MEMORY_LIMIT=512M

# ==============================================
# Health Checks
# ==============================================

HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# ==============================================
# Logging
# ==============================================

LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_SIZE=100m
LOG_MAX_FILES=10

# ==============================================
# Security Headers
# ==============================================

# CORS
CORS_ORIGIN=https://aljameia.com,https://admin.aljameia.com
CORS_CREDENTIALS=true

# CSP
CSP_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'

# HSTS
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true
