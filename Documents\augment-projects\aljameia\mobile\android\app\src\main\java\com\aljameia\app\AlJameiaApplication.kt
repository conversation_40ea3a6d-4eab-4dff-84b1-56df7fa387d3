package com.aljameia.app

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Build
import android.util.Log

class AlJameiaApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        Log.d("AlJameia", "Application onCreate() called")

        try {
            // Create notification channels
            createNotificationChannels()
            Log.d("AlJameia", "Application initialized successfully")
        } catch (e: Exception) {
            Log.e("AlJameia", "Error in Application onCreate", e)
        }
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(NotificationManager::class.java)
            
            // Default notification channel
            val defaultChannel = NotificationChannel(
                "default_channel",
                "Default Notifications",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Default notifications for Al Jameia app"
            }
            
            // Payment notifications channel
            val paymentChannel = NotificationChannel(
                "payment_channel",
                "Payment Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Payment and transaction notifications"
            }
            
            // Sync notifications channel
            val syncChannel = NotificationChannel(
                "sync_channel",
                "Sync Notifications",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Data synchronization notifications"
            }
            
            notificationManager?.createNotificationChannels(
                listOf(defaultChannel, paymentChannel, syncChannel)
            )
        }
    }
}
