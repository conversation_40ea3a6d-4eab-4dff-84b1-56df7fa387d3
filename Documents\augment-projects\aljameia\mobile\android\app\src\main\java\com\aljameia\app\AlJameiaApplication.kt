package com.aljameia.app

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Build
// import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import androidx.work.WorkManager
import com.jakewharton.threetenabp.AndroidThreeTen
// import dagger.hilt.android.HiltAndroidApp
// import javax.inject.Inject

// @HiltAndroidApp
class AlJameiaApplication : Application() {

    // @Inject
    // lateinit var workerFactory: HiltWorkerFactory

    override fun onCreate() {
        super.onCreate()
        
        // Initialize ThreeTenABP for date/time handling
        AndroidThreeTen.init(this)
        
        // Create notification channels
        createNotificationChannels()
        
        // Initialize WorkManager
        WorkManager.initialize(this, Configuration.Builder().build())
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(NotificationManager::class.java)
            
            // Default notification channel
            val defaultChannel = NotificationChannel(
                "default_channel",
                "Default Notifications",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Default notifications for Al Jameia app"
            }
            
            // Payment notifications channel
            val paymentChannel = NotificationChannel(
                "payment_channel",
                "Payment Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Payment and transaction notifications"
            }
            
            // Sync notifications channel
            val syncChannel = NotificationChannel(
                "sync_channel",
                "Sync Notifications",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Data synchronization notifications"
            }
            
            notificationManager?.createNotificationChannels(
                listOf(defaultChannel, paymentChannel, syncChannel)
            )
        }
    }
}
