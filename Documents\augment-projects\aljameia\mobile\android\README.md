# Al Jameia Association Management App - Android

A comprehensive mobile application for managing association operations, built with modern Android development practices.

## 🏗️ Architecture

- **MVVM Pattern** - Clean separation of concerns
- **Jetpack Compose** - Modern declarative UI
- **Room Database** - Local data persistence
- **Repository Pattern** - Data layer abstraction
- **Material Design 3** - Latest design system

## 📱 Features

### Core Functionality
- ✅ **User Authentication** - Secure login/signup system
- ✅ **Financial Dashboard** - Comprehensive financial overview
- ✅ **Transaction Management** - Track payments, fees, and donations
- ✅ **Document Management** - PDF viewing and document storage
- ✅ **User Profile** - Personal information management
- ✅ **Settings** - Bilingual (Arabic/English) preferences

### Technical Features
- ✅ **Bilingual Support** - Arabic and English with RTL layout
- ✅ **Dark/Light Theme** - Adaptive UI themes
- ✅ **Biometric Authentication** - Fingerprint/Face recognition
- ✅ **Push Notifications** - Firebase Cloud Messaging
- ✅ **Offline Support** - Local database with sync capabilities
- ✅ **Security** - Network security configuration and encryption

## 🛠️ Technology Stack

### Core Android
- **Kotlin** 1.9.20
- **Android Gradle Plugin** 8.2.0
- **Compile SDK** 34
- **Min <PERSON>K** 24 (Android 7.0)
- **Target SDK** 34

### UI & Design
- **Jetpack Compose** 1.5.4
- **Material Design 3** 1.1.2
- **Compose Navigation** 2.7.6
- **Compose Activity** 1.8.2

### Data & Storage
- **Room Database** 2.6.1
- **DataStore Preferences** 1.0.0
- **Gson** 2.10.1

### Networking
- **Retrofit** 2.9.0
- **OkHttp** 4.12.0
- **Gson Converter** 2.9.0

### Firebase Services
- **Firebase Auth** 22.3.0
- **Firebase Firestore** 24.10.0
- **Firebase Storage** 20.3.0
- **Firebase Messaging** 23.4.0
- **Firebase Analytics** 21.5.0
- **Firebase Crashlytics** 18.6.0

### Security & Utilities
- **Biometric** 1.1.0
- **Security Crypto** 1.1.0-alpha06
- **WorkManager** 2.9.0
- **Timber** 5.0.1 (Logging)

## 📁 Project Structure

```
app/src/main/java/com/aljameia/app/
├── data/
│   ├── database/          # Room database setup
│   ├── entities/          # Data entities (User, Transaction, etc.)
│   ├── dao/              # Data Access Objects
│   └── repository/       # Repository implementations
├── domain/
│   ├── model/            # Domain models
│   └── usecase/          # Business logic use cases
├── ui/
│   ├── main/             # Main activity and dashboard
│   ├── auth/             # Authentication screens
│   ├── dashboard/        # Financial dashboard
│   ├── settings/         # App settings
│   ├── profile/          # User profile
│   └── documents/        # Document management
├── services/             # Background services
├── receivers/            # Broadcast receivers
└── utils/               # Utility classes
```

## 🚀 Getting Started

### Prerequisites
- Android Studio Hedgehog or later
- JDK 17
- Android SDK 34
- Git

### Building the Project

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd aljameia/mobile/android
   ```

2. **Open in Android Studio**
   - Open Android Studio
   - Select "Open an existing project"
   - Navigate to the android folder

3. **Build the project**
   ```bash
   ./gradlew assembleDebug
   ```

4. **Install on device**
   ```bash
   ./gradlew installDebug
   ```

## 📊 Database Schema

### Core Entities
- **User** - Member information and preferences
- **Transaction** - Financial transactions and payments
- **Document** - File management and metadata
- **Event** - Association events and registrations

### Key Features
- **Type Converters** - Handle complex data types
- **Foreign Keys** - Maintain data integrity
- **Indexes** - Optimized query performance

## 🌐 Internationalization

The app supports both Arabic and English:
- **RTL Layout** - Proper right-to-left text direction
- **Localized Strings** - All UI text in both languages
- **Cultural Adaptation** - Date formats, number formats, etc.

## 🔒 Security Features

- **Network Security Config** - Secure HTTPS communications
- **Certificate Pinning** - Prevent man-in-the-middle attacks
- **Biometric Authentication** - Secure app access
- **Data Encryption** - Local data protection
- **ProGuard Rules** - Code obfuscation for release builds

## 📱 APK Information

- **Debug APK Size**: ~42MB
- **Minimum Android Version**: 7.0 (API 24)
- **Target Android Version**: 14 (API 34)
- **Architecture Support**: ARM64, ARM, x86_64

## 🔧 Development Notes

### Build Variants
- **Debug** - Development builds with debugging enabled
- **Release** - Production builds (requires signing key)
- **Staging** - Testing builds with production-like settings

### Testing
- Unit tests with JUnit
- UI tests with Espresso
- Integration tests with Room

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Documentation**: [Internal Wiki]
- **Issue Tracking**: [Project Management System]

---

**Built with ❤️ for Al Jameia Association**
