'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  CurrencyDollarIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  CalendarIcon,
  BanknotesIcon,
  CreditCardIcon,
  PencilIcon,
  PauseIcon,
  PlayIcon,
  StopIcon
} from '@heroicons/react/24/outline';
import type { MonthlySubscription, SubscriptionPayment } from '@/types/jamiya';

interface SubscriptionDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function SubscriptionDetailPage({ params }: SubscriptionDetailPageProps) {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<MonthlySubscription | null>(null);
  const [payments, setPayments] = useState<SubscriptionPayment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptionId, setSubscriptionId] = useState<string>('');

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setSubscriptionId(resolvedParams.id);
    };
    getParams();
  }, [params]);

  useEffect(() => {
    if (!subscriptionId) return;
    
    const loadData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - في التطبيق الحقيقي سيتم جلبها من API
      const mockSubscription: MonthlySubscription = {
        id: subscriptionId,
        jamiyaId: '1',
        memberId: 'user1',
        memberName: 'أحمد محمد الراشد',
        memberEmail: '<EMAIL>',
        memberPhone: '+************',
        sharesCount: 2,
        pricePerShare: 2500,
        monthlyAmount: 5000,
        currency: 'SAR',
        startDate: '2024-01-01',
        status: 'active',
        paymentMethod: 'bank_transfer',
        bankAccount: '************************',
        autoRenewal: true,
        reminderDays: [7, 3, 1],
        totalPaid: 30000,
        totalDue: 60000,
        missedPayments: 0,
        lastPaymentDate: '2024-06-01',
        nextPaymentDate: '2024-07-01',
        createdAt: '2024-01-01T10:00:00Z',
        updatedAt: '2024-06-18T14:30:00Z'
      };

      const mockPayments: SubscriptionPayment[] = [
        {
          id: '1',
          subscriptionId: subscriptionId,
          jamiyaId: '1',
          memberId: 'user1',
          amount: 5000,
          currency: 'SAR',
          dueDate: '2024-07-01',
          status: 'pending',
          remindersSent: 0,
          createdAt: '2024-06-01T10:00:00Z',
          updatedAt: '2024-06-01T10:00:00Z'
        },
        {
          id: '2',
          subscriptionId: subscriptionId,
          jamiyaId: '1',
          memberId: 'user1',
          amount: 5000,
          currency: 'SAR',
          dueDate: '2024-06-01',
          paidDate: '2024-06-01',
          status: 'paid',
          paymentMethod: 'bank_transfer',
          reference: 'TXN-2024-001',
          remindersSent: 1,
          lastReminderDate: '2024-05-29',
          createdAt: '2024-05-01T10:00:00Z',
          updatedAt: '2024-06-01T10:00:00Z'
        },
        {
          id: '3',
          subscriptionId: subscriptionId,
          jamiyaId: '1',
          memberId: 'user1',
          amount: 5000,
          currency: 'SAR',
          dueDate: '2024-05-01',
          paidDate: '2024-05-01',
          status: 'paid',
          paymentMethod: 'bank_transfer',
          reference: 'TXN-2024-002',
          remindersSent: 0,
          createdAt: '2024-04-01T10:00:00Z',
          updatedAt: '2024-05-01T10:00:00Z'
        }
      ];

      setSubscription(mockSubscription);
      setPayments(mockPayments);
      setIsLoading(false);
    };

    loadData();
  }, [subscriptionId]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-blue-500" />;
      case 'paused':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'completed': return 'مكتمل';
      case 'paused': return 'متوقف';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقداً';
      case 'bank_transfer': return 'تحويل بنكي';
      case 'online_payment': return 'دفع إلكتروني';
      case 'auto_debit': return 'خصم تلقائي';
      case 'check': return 'شيك';
      default: return method;
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'late':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'missed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'paid': return 'مدفوع';
      case 'pending': return 'معلق';
      case 'late': return 'متأخر';
      case 'missed': return 'مفقود';
      default: return status;
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['subscription_management']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل تفاصيل الاشتراك...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!subscription) {
    return (
      <ProtectedRoute requiredPermissions={['subscription_management']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <XCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">الاشتراك غير موجود</h2>
            <p className="text-gray-600 mb-4">لم يتم العثور على الاشتراك المطلوب</p>
            <Link href="/subscriptions">
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                العودة إلى الاشتراكات
              </button>
            </Link>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['subscription_management']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">منصة الجمعيات المالية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/subscriptions">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    الاشتراكات
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Subscription Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <h1 className="text-2xl font-bold text-gray-900 ml-3">
                      اشتراك في جمعية رقم {subscription.jamiyaId}
                    </h1>
                    <div className="flex items-center">
                      {getStatusIcon(subscription.status)}
                      <span className="mr-1 text-sm text-gray-600">
                        {getStatusText(subscription.status)}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-4">
                    اشتراك في {subscription.sharesCount} سهم بقيمة {formatCurrency(subscription.monthlyAmount)} شهرياً
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <CurrencyDollarIcon className="h-4 w-4 ml-2" />
                      <span>{subscription.sharesCount} سهم × {formatCurrency(subscription.pricePerShare)}</span>
                    </div>
                    
                    <div className="flex items-center text-sm text-gray-500">
                      <CreditCardIcon className="h-4 w-4 ml-2" />
                      <span>{getPaymentMethodText(subscription.paymentMethod)}</span>
                    </div>
                    
                    <div className="flex items-center text-sm text-gray-500">
                      <CalendarIcon className="h-4 w-4 ml-2" />
                      <span>بدأ في {new Date(subscription.startDate).toLocaleDateString('ar-SA')}</span>
                    </div>
                    
                    <div className="flex items-center text-sm text-gray-500">
                      <BanknotesIcon className="h-4 w-4 ml-2" />
                      <span>{formatCurrency(subscription.totalPaid)} مدفوع</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 space-x-reverse">
                  <Link href={`/subscriptions/${subscription.id}/edit`}>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                      <PencilIcon className="h-4 w-4 ml-2" />
                      تعديل
                    </button>
                  </Link>
                  {subscription.status === 'active' ? (
                    <button className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors flex items-center">
                      <PauseIcon className="h-4 w-4 ml-2" />
                      إيقاف مؤقت
                    </button>
                  ) : subscription.status === 'paused' ? (
                    <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                      <PlayIcon className="h-4 w-4 ml-2" />
                      استئناف
                    </button>
                  ) : null}
                  <button className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center">
                    <StopIcon className="h-4 w-4 ml-2" />
                    إلغاء
                  </button>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Statistics Cards */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <BanknotesIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(subscription.totalPaid)}
                  </p>
                  <p className="text-sm text-gray-600">إجمالي المدفوع</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-blue-600">
                    {formatCurrency(subscription.totalDue)}
                  </p>
                  <p className="text-sm text-gray-600">إجمالي المستحق</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <CurrencyDollarIcon className="h-6 w-6 text-indigo-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-indigo-600">
                    {subscription.sharesCount}
                  </p>
                  <p className="text-sm text-gray-600">عدد الأسهم</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <CalendarIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-purple-600">
                    {Math.ceil((subscription.totalDue - subscription.totalPaid) / subscription.monthlyAmount)}
                  </p>
                  <p className="text-sm text-gray-600">أشهر متبقية</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <ExclamationTriangleIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-orange-600">{subscription.missedPayments}</p>
                  <p className="text-sm text-gray-600">مدفوعات مفقودة</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Next Payment */}
          {subscription.status === 'active' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">الدفعة القادمة</h3>
                <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-blue-900">
                        دفعة شهر {new Date(subscription.nextPaymentDate).toLocaleDateString('ar-SA', { month: 'long', year: 'numeric' })}
                      </h4>
                      <p className="text-sm text-blue-700">
                        تاريخ الاستحقاق: {new Date(subscription.nextPaymentDate).toLocaleDateString('ar-SA')}
                      </p>
                      <p className="text-xs text-blue-600 mt-1">
                        متبقي {Math.ceil((new Date(subscription.nextPaymentDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} يوم
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-600">
                        {formatCurrency(subscription.monthlyAmount)}
                      </div>
                      <button className="mt-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        ادفع الآن
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Payment History */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">سجل المدفوعات</h3>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      التاريخ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المبلغ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      طريقة الدفع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المرجع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {payments.map((payment) => (
                    <tr key={payment.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="font-medium">
                            {payment.paidDate
                              ? new Date(payment.paidDate).toLocaleDateString('ar-SA')
                              : new Date(payment.dueDate).toLocaleDateString('ar-SA')
                            }
                          </div>
                          <div className="text-xs text-gray-500">
                            {payment.paidDate ? 'تاريخ الدفع' : 'تاريخ الاستحقاق'}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {formatCurrency(payment.amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {payment.paymentMethod ? getPaymentMethodText(payment.paymentMethod) : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {payment.reference || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getPaymentStatusIcon(payment.status)}
                          <span className="mr-2 text-sm">
                            {getPaymentStatusText(payment.status)}
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
