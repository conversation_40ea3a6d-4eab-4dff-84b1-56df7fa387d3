'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  CurrencyDollarIcon,
  UsersIcon,
  BanknotesIcon,
  ChartBarIcon,
  CalendarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  PlusIcon,
  EyeIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  BellIcon
} from '@heroicons/react/24/outline';
import type { Jamiya, JamiyaStatistics } from '@/types/jamiya';

export default function JamiyaDashboard() {
  const { user } = useAuth();
  const [jamiyaStats, setJamiyaStats] = useState<JamiyaStatistics | null>(null);
  const [recentJamiyas, setRecentJamiyas] = useState<Jamiya[]>([]);
  const [alerts, setAlerts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadDashboardData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // إحصائيات الجمعيات المالية
      setJamiyaStats({
        totalCollected: 2450000,
        totalDistributed: 1980000,
        totalPending: 470000,
        activeMembers: 89,
        suspendedMembers: 3,
        completedMembers: 45,
        onTimePayments: 234,
        latePayments: 12,
        missedPayments: 5,
        completedCycles: 18,
        pendingCycles: 6,
        collectionRate: 94.2,
        onTimeRate: 89.7,
        completionRate: 96.8
      });

      // التنبيهات
      setAlerts([
        {
          id: '1',
          type: 'payment_due',
          severity: 'high',
          title: 'مساهمات متأخرة',
          message: '3 أعضاء لم يدفعوا مساهماتهم لهذا الشهر',
          date: '2024-06-18',
          jamiyaId: '1',
          jamiyaName: 'جمعية الأصدقاء'
        },
        {
          id: '2',
          type: 'cycle_due',
          severity: 'medium',
          title: 'دورة قادمة',
          message: 'دورة محمد السعود مستحقة خلال 3 أيام',
          date: '2024-06-18',
          jamiyaId: '2',
          jamiyaName: 'جمعية العمل'
        }
      ]);
      
      setIsLoading(false);
    };

    loadDashboardData();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'payment_due':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'cycle_due':
        return <CalendarIcon className="h-5 w-5 text-yellow-500" />;
      case 'member_joined':
        return <UserGroupIcon className="h-5 w-5 text-green-500" />;
      default:
        return <BellIcon className="h-5 w-5 text-blue-500" />;
    }
  };

  const quickActions = [
    {
      title: 'إنشاء جمعية جديدة',
      description: 'ابدأ جمعية مالية جديدة مع الأعضاء',
      icon: PlusIcon,
      color: 'blue',
      href: '/jamiyas/create'
    },
    {
      title: 'إدارة الجمعيات',
      description: 'عرض وإدارة جميع الجمعيات النشطة',
      icon: UserGroupIcon,
      color: 'green',
      href: '/jamiyas'
    },
    {
      title: 'تسجيل مساهمة',
      description: 'تسجيل مساهمة شهرية لعضو',
      icon: BanknotesIcon,
      color: 'purple',
      href: '/contributions/add'
    },
    {
      title: 'التقارير المالية',
      description: 'عرض التقارير والإحصائيات المالية',
      icon: ChartBarIcon,
      color: 'orange',
      href: '/reports'
    }
  ];

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['jamiya_management']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل بيانات الجمعيات...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['jamiya_management']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">منصة الجمعيات المالية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-semibold text-sm">
                    {user?.name?.charAt(0)}
                  </span>
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              مرحباً بعودتك، {user?.name}!
            </h1>
            <p className="text-gray-600">
              إليك نظرة عامة على جمعياتك المالية اليوم.
            </p>
          </motion.div>

          {/* Alerts */}
          {alerts.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="mb-8"
            >
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <BellIcon className="h-5 w-5 ml-2" />
                  التنبيهات المهمة
                </h3>
                <div className="space-y-3">
                  {alerts.map((alert) => (
                    <div
                      key={alert.id}
                      className={`flex items-center p-3 rounded-lg border ${
                        alert.severity === 'high' ? 'bg-red-50 border-red-200' :
                        alert.severity === 'medium' ? 'bg-yellow-50 border-yellow-200' :
                        'bg-blue-50 border-blue-200'
                      }`}
                    >
                      {getAlertIcon(alert.type)}
                      <div className="mr-3 flex-1">
                        <h4 className="font-medium text-gray-900">{alert.title}</h4>
                        <p className="text-sm text-gray-600">{alert.message}</p>
                        <p className="text-xs text-gray-500 mt-1">{alert.jamiyaName}</p>
                      </div>
                      <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        عرض التفاصيل
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {/* Financial Statistics */}
          {jamiyaStats && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
            >
              {/* Total Collected */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي المجموع</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(jamiyaStats.totalCollected)}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <BanknotesIcon className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 ml-1" />
                  <span className="text-sm font-medium text-green-600">
                    {jamiyaStats.collectionRate.toFixed(1)}%
                  </span>
                  <span className="text-sm text-gray-600 mr-2">معدل التحصيل</span>
                </div>
              </div>

              {/* Total Distributed */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي الموزع</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {formatCurrency(jamiyaStats.totalDistributed)}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <CheckCircleIcon className="h-4 w-4 text-blue-500 ml-1" />
                  <span className="text-sm font-medium text-blue-600">
                    {jamiyaStats.completedCycles}
                  </span>
                  <span className="text-sm text-gray-600 mr-2">دورة مكتملة</span>
                </div>
              </div>

              {/* Active Members */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">الأعضاء النشطون</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {jamiyaStats.activeMembers}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <UsersIcon className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <span className="text-sm text-gray-600">
                    {jamiyaStats.suspendedMembers} موقوف
                  </span>
                  <span className="text-sm text-gray-400 mx-2">•</span>
                  <span className="text-sm text-gray-600">
                    {jamiyaStats.completedMembers} مكتمل
                  </span>
                </div>
              </div>

              {/* Payment Performance */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">معدل الالتزام</p>
                    <p className="text-2xl font-bold text-orange-600">
                      {jamiyaStats.onTimeRate.toFixed(1)}%
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <ChartBarIcon className="h-6 w-6 text-orange-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <span className="text-sm text-green-600">
                    {jamiyaStats.onTimePayments} في الوقت
                  </span>
                  <span className="text-sm text-gray-400 mx-2">•</span>
                  <span className="text-sm text-red-600">
                    {jamiyaStats.latePayments} متأخر
                  </span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="mb-8"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickActions.map((action, index) => (
                <Link key={index} href={action.href}>
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer">
                    <div className={`h-12 w-12 rounded-lg flex items-center justify-center mb-4 ${
                      action.color === 'blue' ? 'bg-blue-100' :
                      action.color === 'green' ? 'bg-green-100' :
                      action.color === 'purple' ? 'bg-purple-100' :
                      'bg-orange-100'
                    }`}>
                      <action.icon className={`h-6 w-6 ${
                        action.color === 'blue' ? 'text-blue-600' :
                        action.color === 'green' ? 'text-green-600' :
                        action.color === 'purple' ? 'text-purple-600' :
                        'text-orange-600'
                      }`} />
                    </div>
                    <h4 className="font-semibold text-gray-900 mb-2">{action.title}</h4>
                    <p className="text-sm text-gray-600">{action.description}</p>
                  </div>
                </Link>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
