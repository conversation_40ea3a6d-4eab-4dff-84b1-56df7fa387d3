import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { notificationService, NotificationSettings } from '../../services/notificationService';

export default function NotificationSettingsScreen() {
  const [settings, setSettings] = useState<NotificationSettings>({
    enabled: true,
    paymentReminders: true,
    jamiyaUpdates: true,
    generalNotifications: true,
    soundEnabled: true,
    vibrationEnabled: true,
    reminderDays: 3,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const currentSettings = await notificationService.getSettings();
      setSettings(currentSettings);
    } catch (error) {
      console.error('Failed to load notification settings:', error);
      Alert.alert('خطأ', 'فشل في تحميل إعدادات الإشعارات');
    } finally {
      setIsLoading(false);
    }
  };

  const updateSetting = async (key: keyof NotificationSettings, value: any) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      
      await notificationService.updateSettings({ [key]: value });
      
      // Show confirmation for important changes
      if (key === 'enabled') {
        Alert.alert(
          'تم التحديث',
          value ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات'
        );
      }
    } catch (error) {
      console.error('Failed to update setting:', error);
      Alert.alert('خطأ', 'فشل في تحديث الإعدادات');
      // Revert the change
      setSettings(settings);
    }
  };

  const handleReminderDaysChange = (days: number) => {
    updateSetting('reminderDays', days);
  };

  const testNotification = async () => {
    try {
      await notificationService.scheduleNotification({
        id: 'test_notification',
        title: 'إشعار تجريبي',
        body: 'هذا إشعار تجريبي للتأكد من عمل النظام',
        type: 'general',
        priority: 'normal',
      });
      
      Alert.alert('تم', 'تم إرسال إشعار تجريبي');
    } catch (error) {
      console.error('Failed to send test notification:', error);
      Alert.alert('خطأ', 'فشل في إرسال الإشعار التجريبي');
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>جاري التحميل...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#3B82F6" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>إعدادات الإشعارات</Text>
      </View>

      {/* Main Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>الإعدادات العامة</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Ionicons name="notifications-outline" size={24} color="#3B82F6" />
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>تفعيل الإشعارات</Text>
              <Text style={styles.settingDescription}>
                تفعيل أو إيقاف جميع الإشعارات
              </Text>
            </View>
          </View>
          <Switch
            value={settings.enabled}
            onValueChange={(value) => updateSetting('enabled', value)}
            trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
            thumbColor={settings.enabled ? '#FFFFFF' : '#9CA3AF'}
          />
        </View>

        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Ionicons name="volume-high-outline" size={24} color="#10B981" />
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>الصوت</Text>
              <Text style={styles.settingDescription}>
                تشغيل صوت مع الإشعارات
              </Text>
            </View>
          </View>
          <Switch
            value={settings.soundEnabled}
            onValueChange={(value) => updateSetting('soundEnabled', value)}
            disabled={!settings.enabled}
            trackColor={{ false: '#E5E7EB', true: '#10B981' }}
            thumbColor={settings.soundEnabled ? '#FFFFFF' : '#9CA3AF'}
          />
        </View>

        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Ionicons name="phone-portrait-outline" size={24} color="#8B5CF6" />
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>الاهتزاز</Text>
              <Text style={styles.settingDescription}>
                اهتزاز الهاتف مع الإشعارات
              </Text>
            </View>
          </View>
          <Switch
            value={settings.vibrationEnabled}
            onValueChange={(value) => updateSetting('vibrationEnabled', value)}
            disabled={!settings.enabled}
            trackColor={{ false: '#E5E7EB', true: '#8B5CF6' }}
            thumbColor={settings.vibrationEnabled ? '#FFFFFF' : '#9CA3AF'}
          />
        </View>
      </View>

      {/* Notification Types */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>أنواع الإشعارات</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Ionicons name="card-outline" size={24} color="#F59E0B" />
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>تذكير المدفوعات</Text>
              <Text style={styles.settingDescription}>
                تذكير بمواعيد الدفعات المستحقة
              </Text>
            </View>
          </View>
          <Switch
            value={settings.paymentReminders}
            onValueChange={(value) => updateSetting('paymentReminders', value)}
            disabled={!settings.enabled}
            trackColor={{ false: '#E5E7EB', true: '#F59E0B' }}
            thumbColor={settings.paymentReminders ? '#FFFFFF' : '#9CA3AF'}
          />
        </View>

        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Ionicons name="business-outline" size={24} color="#EF4444" />
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>تحديثات الجمعيات</Text>
              <Text style={styles.settingDescription}>
                إشعارات حول تحديثات الجمعيات المشترك بها
              </Text>
            </View>
          </View>
          <Switch
            value={settings.jamiyaUpdates}
            onValueChange={(value) => updateSetting('jamiyaUpdates', value)}
            disabled={!settings.enabled}
            trackColor={{ false: '#E5E7EB', true: '#EF4444' }}
            thumbColor={settings.jamiyaUpdates ? '#FFFFFF' : '#9CA3AF'}
          />
        </View>

        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Ionicons name="information-circle-outline" size={24} color="#6B7280" />
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>الإشعارات العامة</Text>
              <Text style={styles.settingDescription}>
                إشعارات عامة ونصائح استثمارية
              </Text>
            </View>
          </View>
          <Switch
            value={settings.generalNotifications}
            onValueChange={(value) => updateSetting('generalNotifications', value)}
            disabled={!settings.enabled}
            trackColor={{ false: '#E5E7EB', true: '#6B7280' }}
            thumbColor={settings.generalNotifications ? '#FFFFFF' : '#9CA3AF'}
          />
        </View>
      </View>

      {/* Reminder Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>إعدادات التذكير</Text>
        
        <View style={styles.reminderDaysContainer}>
          <Text style={styles.reminderDaysLabel}>
            التذكير قبل موعد الدفع بـ {settings.reminderDays} أيام
          </Text>
          
          <View style={styles.reminderDaysButtons}>
            {[1, 2, 3, 5, 7].map((days) => (
              <TouchableOpacity
                key={days}
                style={[
                  styles.reminderDayButton,
                  settings.reminderDays === days && styles.reminderDayButtonActive
                ]}
                onPress={() => handleReminderDaysChange(days)}
                disabled={!settings.enabled || !settings.paymentReminders}
              >
                <Text style={[
                  styles.reminderDayButtonText,
                  settings.reminderDays === days && styles.reminderDayButtonTextActive
                ]}>
                  {days}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* Test Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>اختبار</Text>
        
        <TouchableOpacity
          style={[styles.testButton, !settings.enabled && styles.testButtonDisabled]}
          onPress={testNotification}
          disabled={!settings.enabled}
        >
          <Ionicons name="send-outline" size={20} color={settings.enabled ? "white" : "#9CA3AF"} />
          <Text style={[styles.testButtonText, !settings.enabled && styles.testButtonTextDisabled]}>
            إرسال إشعار تجريبي
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  section: {
    backgroundColor: 'white',
    marginTop: 16,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937',
    paddingHorizontal: 20,
    marginBottom: 16,
    textAlign: 'right',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'right',
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'right',
  },
  reminderDaysContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  reminderDaysLabel: {
    fontSize: 16,
    color: '#1F2937',
    marginBottom: 16,
    textAlign: 'right',
  },
  reminderDaysButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  reminderDayButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  reminderDayButtonActive: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
  },
  reminderDayButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B7280',
  },
  reminderDayButtonTextActive: {
    color: 'white',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    marginHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
  },
  testButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  testButtonTextDisabled: {
    color: '#9CA3AF',
  },
});
