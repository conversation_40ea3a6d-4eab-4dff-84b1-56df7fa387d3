{"name": "@aljameia/frontend", "version": "1.0.0", "description": "Frontend web application for Al-Jameia Financial Association Management System", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "cross-env ANALYZE=true next build", "export": "next export", "docker:build": "docker build -t aljameia-frontend .", "docker:run": "docker run -p 3001:3000 aljameia-frontend"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next/bundle-analyzer": "^14.0.3", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "clsx": "^2.0.0", "critters": "^0.0.23", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "html2canvas": "^1.4.1", "i18next": "^23.7.6", "jspdf": "^2.5.1", "next": "^14.0.3", "next-i18next": "^15.2.0", "next-pwa": "^5.6.0", "postcss": "^8.4.32", "qr-scanner": "^1.4.2", "react": "^18.2.0", "react-datepicker": "^4.24.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-intersection-observer": "^9.5.3", "react-pdf": "^7.5.1", "react-qr-code": "^2.0.12", "react-query": "^3.39.3", "react-router-dom": "^6.19.0", "react-spring": "^9.7.3", "react-table": "^7.8.0", "react-webcam": "^7.2.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.6", "workbox-webpack-plugin": "^7.0.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@storybook/addon-essentials": "^7.6.3", "@storybook/addon-interactions": "^7.6.3", "@storybook/addon-links": "^7.6.3", "@storybook/blocks": "^7.6.3", "@storybook/nextjs": "^7.6.3", "@storybook/react": "^7.6.3", "@storybook/testing-library": "^0.2.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.9.0", "@types/react": "^18.2.38", "@types/react-datepicker": "^4.19.4", "@types/react-dom": "^18.2.17", "@types/react-pdf": "^7.0.0", "@types/react-table": "^7.7.18", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "cross-env": "^7.0.3", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "storybook": "^7.6.3", "typescript": "^5.2.2"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "testPathIgnorePatterns": ["<rootDir>/.next/", "<rootDir>/node_modules/"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1", "^@/components/(.*)$": "<rootDir>/src/components/$1", "^@/pages/(.*)$": "<rootDir>/src/pages/$1", "^@/utils/(.*)$": "<rootDir>/src/utils/$1", "^@/hooks/(.*)$": "<rootDir>/src/hooks/$1", "^@/store/(.*)$": "<rootDir>/src/store/$1", "^@/types/(.*)$": "<rootDir>/src/types/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/pages/_app.tsx", "!src/pages/_document.tsx"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}