'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  DocumentTextIcon,
  FolderIcon,
  CloudArrowUpIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
  Squares2X2Icon,
  ListBulletIcon,
  CalendarIcon,
  UserIcon,
  TagIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import type { Document, DocumentFolder, DocumentCategory, DocumentType, DocumentStatus } from '@/types/document';

export default function DocumentsPage() {
  const { user } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [folders, setFolders] = useState<DocumentFolder[]>([]);
  const [currentFolder, setCurrentFolder] = useState<DocumentFolder | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<DocumentCategory | 'all'>('all');
  const [selectedType, setSelectedType] = useState<DocumentType | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = useState<DocumentStatus | 'all'>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const mockFolders: DocumentFolder[] = [
    {
      id: '1',
      name: 'العقود والاتفاقيات',
      description: 'جميع العقود والاتفاقيات الخاصة بالجمعية',
      parentId: undefined,
      path: '/contracts',
      level: 0,
      isRoot: true,
      permissions: {
        isPublic: false,
        allowedUsers: ['admin', 'manager'],
        allowedRoles: ['admin', 'manager'],
        allowedGroups: [],
        permissions: {
          view: true,
          upload: true,
          createFolder: true,
          edit: true,
          delete: true,
          manage: true
        }
      },
      createdBy: 'admin',
      createdByName: 'المدير العام',
      createdAt: '2024-01-15T10:00:00Z',
      lastModified: '2024-06-18T15:30:00Z',
      lastModifiedBy: 'admin',
      documentCount: 15,
      subfolderCount: 3,
      totalSize: ********,
      color: '#3B82F6',
      icon: 'document',
      isArchived: false
    },
    {
      id: '2',
      name: 'التقارير المالية',
      description: 'التقارير المالية الشهرية والسنوية',
      parentId: undefined,
      path: '/financial-reports',
      level: 0,
      isRoot: true,
      permissions: {
        isPublic: false,
        allowedUsers: ['admin', 'accountant'],
        allowedRoles: ['admin', 'financial'],
        allowedGroups: [],
        permissions: {
          view: true,
          upload: true,
          createFolder: true,
          edit: true,
          delete: false,
          manage: true
        }
      },
      createdBy: 'admin',
      createdByName: 'المدير العام',
      createdAt: '2024-02-01T09:00:00Z',
      lastModified: '2024-06-17T14:20:00Z',
      lastModifiedBy: 'accountant',
      documentCount: 24,
      subfolderCount: 2,
      totalSize: ********,
      color: '#10B981',
      icon: 'chart',
      isArchived: false
    },
    {
      id: '3',
      name: 'السياسات والإجراءات',
      description: 'سياسات الجمعية والإجراءات التشغيلية',
      parentId: undefined,
      path: '/policies',
      level: 0,
      isRoot: true,
      permissions: {
        isPublic: true,
        allowedUsers: [],
        allowedRoles: [],
        allowedGroups: [],
        permissions: {
          view: true,
          upload: false,
          createFolder: false,
          edit: false,
          delete: false,
          manage: false
        }
      },
      createdBy: 'admin',
      createdByName: 'المدير العام',
      createdAt: '2024-01-10T08:00:00Z',
      lastModified: '2024-06-10T11:15:00Z',
      lastModifiedBy: 'admin',
      documentCount: 8,
      subfolderCount: 0,
      totalSize: ********,
      color: '#8B5CF6',
      icon: 'shield',
      isArchived: false
    }
  ];

  const mockDocuments: Document[] = [
    {
      id: '1',
      name: 'تقرير مالي يونيو 2024',
      originalName: 'financial_report_june_2024.pdf',
      description: 'التقرير المالي الشهري لشهر يونيو 2024',
      type: 'pdf',
      category: 'financial',
      mimeType: 'application/pdf',
      size: 2048000,
      url: '/documents/financial_report_june_2024.pdf',
      thumbnailUrl: '/thumbnails/financial_report_june_2024.jpg',
      downloadUrl: '/api/documents/1/download',
      version: 1,
      isLatestVersion: true,
      tags: ['مالي', 'تقرير', 'يونيو', '2024'],
      metadata: {
        author: 'المحاسب الرئيسي',
        subject: 'التقرير المالي الشهري',
        keywords: ['مالي', 'تقرير', 'يونيو'],
        language: 'ar',
        pageCount: 25,
        createdDate: '2024-06-30',
        application: 'Microsoft Word',
        customFields: {}
      },
      permissions: {
        isPublic: false,
        allowedUsers: ['admin', 'accountant'],
        allowedRoles: ['admin', 'financial'],
        allowedGroups: [],
        permissions: {
          view: true,
          download: true,
          edit: false,
          delete: false,
          share: true,
          comment: true,
          approve: false
        }
      },
      status: 'approved',
      uploadedBy: 'accountant',
      uploadedByName: 'أحمد المحاسب',
      uploadedAt: '2024-06-30T16:00:00Z',
      lastModified: '2024-06-30T16:00:00Z',
      lastModifiedBy: 'accountant',
      lastModifiedByName: 'أحمد المحاسب',
      isArchived: false,
      downloadCount: 15,
      viewCount: 45,
      shareCount: 3,
      comments: [],
      approvals: [],
      relatedDocuments: [],
      checksum: 'abc123def456'
    },
    {
      id: '2',
      name: 'عقد شراكة مع الشركة التقنية',
      originalName: 'partnership_agreement_tech_company.docx',
      description: 'عقد شراكة استراتيجية مع الشركة التقنية المتقدمة',
      type: 'word',
      category: 'contracts',
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      size: 1536000,
      url: '/documents/partnership_agreement_tech_company.docx',
      downloadUrl: '/api/documents/2/download',
      version: 2,
      isLatestVersion: true,
      tags: ['عقد', 'شراكة', 'تقنية'],
      metadata: {
        author: 'المستشار القانوني',
        subject: 'عقد شراكة',
        keywords: ['عقد', 'شراكة', 'تقنية'],
        language: 'ar',
        pageCount: 12,
        wordCount: 3500,
        createdDate: '2024-06-15',
        modifiedDate: '2024-06-18',
        application: 'Microsoft Word',
        customFields: {
          contractValue: '500000',
          duration: '24 months'
        }
      },
      permissions: {
        isPublic: false,
        allowedUsers: ['admin', 'legal'],
        allowedRoles: ['admin', 'legal'],
        allowedGroups: [],
        permissions: {
          view: true,
          download: true,
          edit: true,
          delete: false,
          share: false,
          comment: true,
          approve: true
        }
      },
      status: 'under_review',
      uploadedBy: 'legal',
      uploadedByName: 'سارة المستشارة القانونية',
      uploadedAt: '2024-06-15T10:30:00Z',
      lastModified: '2024-06-18T14:45:00Z',
      lastModifiedBy: 'legal',
      lastModifiedByName: 'سارة المستشارة القانونية',
      expiryDate: '2026-06-15',
      isArchived: false,
      downloadCount: 8,
      viewCount: 22,
      shareCount: 1,
      comments: [
        {
          id: '1',
          content: 'يرجى مراجعة البند الخامس من العقد',
          authorId: 'admin',
          authorName: 'المدير العام',
          createdAt: '2024-06-16T09:15:00Z',
          isResolved: false,
          replies: []
        }
      ],
      approvals: [
        {
          id: '1',
          approverId: 'admin',
          approverName: 'المدير العام',
          status: 'pending',
          level: 1,
          isRequired: true
        }
      ],
      relatedDocuments: [],
      checksum: 'def456ghi789'
    },
    {
      id: '3',
      name: 'سياسة الموارد البشرية',
      originalName: 'hr_policy_2024.pdf',
      description: 'سياسة الموارد البشرية المحدثة لعام 2024',
      type: 'pdf',
      category: 'policies',
      mimeType: 'application/pdf',
      size: 3072000,
      url: '/documents/hr_policy_2024.pdf',
      thumbnailUrl: '/thumbnails/hr_policy_2024.jpg',
      downloadUrl: '/api/documents/3/download',
      version: 3,
      isLatestVersion: true,
      tags: ['سياسة', 'موارد بشرية', '2024'],
      metadata: {
        author: 'مدير الموارد البشرية',
        subject: 'سياسة الموارد البشرية',
        keywords: ['سياسة', 'موارد بشرية', 'HR'],
        language: 'ar',
        pageCount: 45,
        createdDate: '2024-01-01',
        modifiedDate: '2024-06-01',
        application: 'Adobe Acrobat',
        customFields: {
          effectiveDate: '2024-01-01',
          reviewDate: '2024-12-31'
        }
      },
      permissions: {
        isPublic: true,
        allowedUsers: [],
        allowedRoles: [],
        allowedGroups: [],
        permissions: {
          view: true,
          download: true,
          edit: false,
          delete: false,
          share: true,
          comment: false,
          approve: false
        }
      },
      status: 'published',
      uploadedBy: 'hr_manager',
      uploadedByName: 'محمد مدير الموارد البشرية',
      uploadedAt: '2024-01-01T08:00:00Z',
      lastModified: '2024-06-01T10:00:00Z',
      lastModifiedBy: 'hr_manager',
      lastModifiedByName: 'محمد مدير الموارد البشرية',
      isArchived: false,
      downloadCount: 125,
      viewCount: 340,
      shareCount: 15,
      comments: [],
      approvals: [],
      relatedDocuments: [],
      checksum: 'ghi789jkl012'
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setFolders(mockFolders);
      setDocuments(mockDocuments);
      setIsLoading(false);
    };

    loadData();
  }, []);

  // تصفية الوثائق
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = searchTerm === '' || 
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory;
    const matchesType = selectedType === 'all' || doc.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || doc.status === selectedStatus;
    
    return matchesSearch && matchesCategory && matchesType && matchesStatus;
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: DocumentType) => {
    switch (type) {
      case 'pdf':
        return '📄';
      case 'word':
        return '📝';
      case 'excel':
        return '📊';
      case 'powerpoint':
        return '📋';
      case 'image':
        return '🖼️';
      case 'video':
        return '🎥';
      case 'audio':
        return '🎵';
      default:
        return '📄';
    }
  };

  const getStatusColor = (status: DocumentStatus) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'published':
        return 'bg-blue-100 text-blue-800';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending_review':
        return 'bg-orange-100 text-orange-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'archived':
        return 'bg-purple-100 text-purple-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: DocumentStatus) => {
    switch (status) {
      case 'approved': return 'معتمد';
      case 'published': return 'منشور';
      case 'under_review': return 'قيد المراجعة';
      case 'pending_review': return 'في انتظار المراجعة';
      case 'rejected': return 'مرفوض';
      case 'draft': return 'مسودة';
      case 'archived': return 'مؤرشف';
      case 'expired': return 'منتهي الصلاحية';
      default: return status;
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['documents']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الوثائق...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['documents']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">الجمعية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/dashboard">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    لوحة التحكم
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <DocumentTextIcon className="h-8 w-8 text-blue-600 ml-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">إدارة الوثائق</h1>
                  <p className="text-gray-600">تنظيم وإدارة جميع وثائق الجمعية</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                  <PlusIcon className="h-5 w-5 ml-2" />
                  إنشاء مجلد
                </button>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                  <CloudArrowUpIcon className="h-5 w-5 ml-2" />
                  رفع وثائق
                </button>
              </div>
            </div>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <DocumentTextIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">{documents.length}</p>
                  <p className="text-sm text-gray-600">إجمالي الوثائق</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <FolderIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">{folders.length}</p>
                  <p className="text-sm text-gray-600">المجلدات</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <ClockIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {documents.filter(d => d.status === 'under_review').length}
                  </p>
                  <p className="text-sm text-gray-600">قيد المراجعة</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <ShareIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {documents.reduce((sum, d) => sum + d.shareCount, 0)}
                  </p>
                  <p className="text-sm text-gray-600">مشاركات</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Folders Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-8"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">المجلدات</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {folders.map((folder) => (
                <div
                  key={folder.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
                >
                  <div className="flex items-center mb-4">
                    <div
                      className="h-12 w-12 rounded-lg flex items-center justify-center"
                      style={{ backgroundColor: folder.color + '20' }}
                    >
                      <FolderIcon
                        className="h-8 w-8"
                        style={{ color: folder.color }}
                      />
                    </div>
                    <div className="mr-4 flex-1">
                      <h4 className="font-semibold text-gray-900">{folder.name}</h4>
                      <p className="text-sm text-gray-500">{folder.documentCount} وثيقة</p>
                    </div>
                  </div>
                  {folder.description && (
                    <p className="text-sm text-gray-600 mb-4">{folder.description}</p>
                  )}
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{formatFileSize(folder.totalSize)}</span>
                    <span>{new Date(folder.lastModified).toLocaleDateString('ar-SA')}</span>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Recent Documents */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">الوثائق الحديثة</h3>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="divide-y divide-gray-200">
                {filteredDocuments.slice(0, 5).map((document) => (
                  <div key={document.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="text-2xl mr-4">
                          {getFileIcon(document.type)}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{document.name}</h4>
                          <div className="flex items-center mt-1 text-sm text-gray-500">
                            <UserIcon className="h-4 w-4 ml-1" />
                            <span className="ml-2">{document.uploadedByName}</span>
                            <CalendarIcon className="h-4 w-4 ml-3" />
                            <span>{new Date(document.uploadedAt).toLocaleDateString('ar-SA')}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(document.status)}`}>
                          {getStatusText(document.status)}
                        </span>
                        <span className="text-sm text-gray-500">{formatFileSize(document.size)}</span>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button className="text-blue-600 hover:text-blue-900 p-1">
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-900 p-1">
                            <ArrowDownTrayIcon className="h-4 w-4" />
                          </button>
                          <button className="text-purple-600 hover:text-purple-900 p-1">
                            <ShareIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
