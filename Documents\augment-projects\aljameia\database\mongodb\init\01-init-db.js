// ==============================================
// MongoDB Initialization Script for Aljameia
// This script runs when MongoDB container starts for the first time
// ==============================================

// Switch to the aljameia database
db = db.getSiblingDB('aljameia');

print('🚀 Starting Aljameia database initialization...');

// ==============================================
// Create Application Users
// ==============================================

// Create application user with read/write permissions
db.createUser({
  user: 'aljameia_app',
  pwd: 'app_password_123',
  roles: [
    {
      role: 'readWrite',
      db: 'aljameia'
    },
    {
      role: 'dbAdmin',
      db: 'aljameia'
    }
  ]
});

print('✅ Created application user: aljameia_app');

// Create read-only user for reporting
db.createUser({
  user: 'aljameia_readonly',
  pwd: 'readonly_password_123',
  roles: [
    {
      role: 'read',
      db: 'aljameia'
    }
  ]
});

print('✅ Created read-only user: aljameia_readonly');

// ==============================================
// Create Collections with Schema Validation
// ==============================================

// Users Collection
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'name', 'password', 'role'],
      properties: {
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
          description: 'Must be a valid email address'
        },
        name: {
          bsonType: 'string',
          minLength: 2,
          maxLength: 100,
          description: 'Must be a string between 2-100 characters'
        },
        password: {
          bsonType: 'string',
          minLength: 6,
          description: 'Must be a string with minimum 6 characters'
        },
        role: {
          enum: ['user', 'admin', 'moderator'],
          description: 'Must be one of: user, admin, moderator'
        },
        phone: {
          bsonType: ['string', 'null'],
          pattern: '^[+]?[0-9]{10,15}$',
          description: 'Must be a valid phone number'
        },
        isVerified: {
          bsonType: 'bool',
          description: 'Must be a boolean'
        },
        isActive: {
          bsonType: 'bool',
          description: 'Must be a boolean'
        }
      }
    }
  }
});

print('✅ Created users collection with validation');

// Jamiyas Collection
db.createCollection('jamiyas', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['name', 'description', 'totalAmount', 'monthlyAmount', 'duration', 'maxMembers', 'createdBy'],
      properties: {
        name: {
          bsonType: 'string',
          minLength: 3,
          maxLength: 100,
          description: 'Must be a string between 3-100 characters'
        },
        description: {
          bsonType: 'string',
          maxLength: 500,
          description: 'Must be a string with maximum 500 characters'
        },
        totalAmount: {
          bsonType: 'number',
          minimum: 1000,
          description: 'Must be a number with minimum value 1000'
        },
        monthlyAmount: {
          bsonType: 'number',
          minimum: 100,
          description: 'Must be a number with minimum value 100'
        },
        duration: {
          bsonType: 'int',
          minimum: 6,
          maximum: 60,
          description: 'Must be an integer between 6-60 months'
        },
        maxMembers: {
          bsonType: 'int',
          minimum: 5,
          maximum: 100,
          description: 'Must be an integer between 5-100'
        },
        status: {
          enum: ['draft', 'active', 'full', 'completed', 'cancelled'],
          description: 'Must be one of: draft, active, full, completed, cancelled'
        }
      }
    }
  }
});

print('✅ Created jamiyas collection with validation');

// Subscriptions Collection
db.createCollection('subscriptions', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'jamiyaId', 'shares', 'monthlyAmount', 'status'],
      properties: {
        shares: {
          bsonType: 'int',
          minimum: 1,
          maximum: 10,
          description: 'Must be an integer between 1-10'
        },
        monthlyAmount: {
          bsonType: 'number',
          minimum: 100,
          description: 'Must be a number with minimum value 100'
        },
        status: {
          enum: ['pending', 'active', 'paused', 'cancelled', 'completed'],
          description: 'Must be one of: pending, active, paused, cancelled, completed'
        }
      }
    }
  }
});

print('✅ Created subscriptions collection with validation');

// Payments Collection
db.createCollection('payments', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'subscriptionId', 'amount', 'currency', 'method', 'status'],
      properties: {
        amount: {
          bsonType: 'number',
          minimum: 1,
          description: 'Must be a positive number'
        },
        currency: {
          enum: ['SAR', 'USD', 'EUR'],
          description: 'Must be one of: SAR, USD, EUR'
        },
        method: {
          enum: ['mada', 'stc_pay', 'apple_pay', 'bank_transfer', 'cash'],
          description: 'Must be a valid payment method'
        },
        status: {
          enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
          description: 'Must be a valid payment status'
        }
      }
    }
  }
});

print('✅ Created payments collection with validation');

// Notifications Collection
db.createCollection('notifications', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'type', 'title', 'message'],
      properties: {
        type: {
          enum: ['info', 'success', 'warning', 'error', 'payment', 'jamiya', 'system'],
          description: 'Must be a valid notification type'
        },
        title: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 100,
          description: 'Must be a string between 1-100 characters'
        },
        message: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 500,
          description: 'Must be a string between 1-500 characters'
        },
        isRead: {
          bsonType: 'bool',
          description: 'Must be a boolean'
        }
      }
    }
  }
});

print('✅ Created notifications collection with validation');

// Audit Logs Collection
db.createCollection('auditlogs', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'action', 'resource'],
      properties: {
        action: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 50,
          description: 'Must be a string between 1-50 characters'
        },
        resource: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 50,
          description: 'Must be a string between 1-50 characters'
        }
      }
    }
  }
});

print('✅ Created auditlogs collection with validation');

// ==============================================
// Create Indexes for Performance
// ==============================================

// Users indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ phone: 1 }, { unique: true, sparse: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ isActive: 1 });
db.users.createIndex({ isVerified: 1 });
db.users.createIndex({ createdAt: 1 });
db.users.createIndex({ lastLogin: 1 });

print('✅ Created users indexes');

// Jamiyas indexes
db.jamiyas.createIndex({ name: 1 });
db.jamiyas.createIndex({ status: 1 });
db.jamiyas.createIndex({ createdBy: 1 });
db.jamiyas.createIndex({ startDate: 1 });
db.jamiyas.createIndex({ endDate: 1 });
db.jamiyas.createIndex({ totalAmount: 1 });
db.jamiyas.createIndex({ monthlyAmount: 1 });
db.jamiyas.createIndex({ categories: 1 });
db.jamiyas.createIndex({ tags: 1 });
db.jamiyas.createIndex({ inviteCode: 1 }, { unique: true, sparse: true });

print('✅ Created jamiyas indexes');

// Subscriptions indexes
db.subscriptions.createIndex({ userId: 1 });
db.subscriptions.createIndex({ jamiyaId: 1 });
db.subscriptions.createIndex({ userId: 1, jamiyaId: 1 }, { unique: true });
db.subscriptions.createIndex({ status: 1 });
db.subscriptions.createIndex({ nextPaymentDate: 1 });
db.subscriptions.createIndex({ position: 1 });

print('✅ Created subscriptions indexes');

// Payments indexes
db.payments.createIndex({ userId: 1 });
db.payments.createIndex({ subscriptionId: 1 });
db.payments.createIndex({ status: 1 });
db.payments.createIndex({ method: 1 });
db.payments.createIndex({ createdAt: 1 });
db.payments.createIndex({ transactionId: 1 }, { unique: true, sparse: true });
db.payments.createIndex({ 'metadata.jamiyaId': 1 });

print('✅ Created payments indexes');

// Notifications indexes
db.notifications.createIndex({ userId: 1 });
db.notifications.createIndex({ type: 1 });
db.notifications.createIndex({ isRead: 1 });
db.notifications.createIndex({ createdAt: 1 });

print('✅ Created notifications indexes');

// Audit logs indexes
db.auditlogs.createIndex({ userId: 1 });
db.auditlogs.createIndex({ action: 1 });
db.auditlogs.createIndex({ resource: 1 });
db.auditlogs.createIndex({ createdAt: 1 });

print('✅ Created audit logs indexes');

print('🎉 MongoDB initialization completed successfully!');
print('📊 Database: aljameia');
print('👤 App User: aljameia_app');
print('📖 Read-only User: aljameia_readonly');
