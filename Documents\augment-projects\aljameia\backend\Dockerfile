# ==============================================
# Multi-stage Dockerfile for Node.js Backend
# ==============================================

# Base image with Node.js LTS
FROM node:20-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# ==============================================
# Dependencies stage
# ==============================================
FROM base AS deps

# Copy package files
COPY package*.json ./
COPY yarn.lock* ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# ==============================================
# Development stage
# ==============================================
FROM base AS development

# Install development dependencies
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .

# Change ownership to nodejs user
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start development server
CMD ["dumb-init", "npm", "run", "dev"]

# ==============================================
# Build stage
# ==============================================
FROM base AS builder

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev)
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Remove dev dependencies
RUN npm prune --production

# ==============================================
# Production stage
# ==============================================
FROM base AS production

# Set production environment
ENV NODE_ENV=production
ENV PORT=3000

# Copy production dependencies
COPY --from=deps /app/node_modules ./node_modules

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package*.json ./

# Copy necessary files
COPY --from=builder /app/public ./public
COPY --from=builder /app/uploads ./uploads

# Create uploads directory with proper permissions
RUN mkdir -p /app/uploads /app/logs && \
    chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start production server
CMD ["dumb-init", "node", "dist/server.js"]

# ==============================================
# Testing stage
# ==============================================
FROM development AS testing

# Install testing dependencies
RUN npm ci

# Copy test files
COPY tests/ ./tests/
COPY jest.config.js ./
COPY .eslintrc.js ./

# Run tests
CMD ["npm", "test"]
