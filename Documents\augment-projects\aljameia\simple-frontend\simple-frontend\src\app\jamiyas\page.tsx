'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  UserGroupIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  BanknotesIcon,
  UsersIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import type { Jamiya, JamiyaType, JamiyaStatus } from '@/types/jamiya';

export default function JamiyasPage() {
  const { user } = useAuth();
  const [jamiyas, setJamiyas] = useState<Jamiya[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<JamiyaType | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = useState<JamiyaStatus | 'all'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  const jamiyasPerPage = 10;

  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const mockJamiyas: Jamiya[] = [
    {
      id: '1',
      name: 'جمعية الأصدقاء',
      description: 'جمعية مالية بين مجموعة من الأصدقاء المقربين',
      type: 'simple',
      status: 'active',
      totalMembers: 12,
      monthlyAmount: 5000,
      currency: 'SAR',
      duration: 12,
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      createdAt: '2023-12-15T10:00:00Z',
      updatedAt: '2024-06-18T14:30:00Z',
      members: [],
      cycles: [],
      contributions: [],
      organizer: {
        id: 'org1',
        name: 'أحمد المنظم',
        email: '<EMAIL>',
        phone: '+966501234567',
        nationalId: '1234567890',
        organizingExperience: 5,
        successfulJamiyas: 8,
        rating: 4.8,
        isVerified: true
      },
      guarantors: [],
      rules: {
        allowEarlyWithdrawal: false,
        allowLateJoining: false,
        requireGuarantor: true,
        lateFee: 50,
        earlyWithdrawalDiscount: 0,
        lateWithdrawalBonus: 0,
        allowBidding: false,
        minimumBid: 0,
        biddingDeadline: 0,
        gracePeriod: 3,
        maxMissedPayments: 2,
        autoSuspension: true,
        advanceNotice: 7,
        withdrawalPenalty: 100
      },
      statistics: {
        totalCollected: 240000,
        totalDistributed: 200000,
        totalPending: 40000,
        activeMembers: 12,
        suspendedMembers: 0,
        completedMembers: 4,
        onTimePayments: 44,
        latePayments: 2,
        missedPayments: 0,
        completedCycles: 4,
        pendingCycles: 8,
        collectionRate: 100,
        onTimeRate: 95.7,
        completionRate: 100
      },
      settings: {
        enableEmailNotifications: true,
        enableSmsNotifications: true,
        enablePushNotifications: true,
        paymentReminderDays: [7, 3, 1],
        cycleReminderDays: [7, 1],
        autoProcessPayments: false,
        autoDistributeFunds: false,
        autoSuspendDefaulters: true,
        showMemberDetails: true,
        showPaymentHistory: false,
        allowMemberCommunication: true,
        requireTwoFactorAuth: true,
        requirePaymentVerification: true,
        logAllActivities: true
      }
    },
    {
      id: '2',
      name: 'جمعية العمل',
      description: 'جمعية مالية لزملاء العمل في الشركة',
      type: 'auction',
      status: 'recruiting',
      totalMembers: 15,
      monthlyAmount: 3000,
      currency: 'SAR',
      duration: 15,
      startDate: '2024-07-01',
      endDate: '2025-09-30',
      createdAt: '2024-05-20T09:00:00Z',
      updatedAt: '2024-06-18T11:15:00Z',
      members: [],
      cycles: [],
      contributions: [],
      organizer: {
        id: 'org2',
        name: 'سارة المديرة',
        email: '<EMAIL>',
        phone: '+966502345678',
        nationalId: '2345678901',
        organizingExperience: 3,
        successfulJamiyas: 4,
        rating: 4.5,
        isVerified: true
      },
      guarantors: [],
      rules: {
        allowEarlyWithdrawal: true,
        allowLateJoining: true,
        requireGuarantor: false,
        lateFee: 100,
        earlyWithdrawalDiscount: 2,
        lateWithdrawalBonus: 1,
        allowBidding: true,
        minimumBid: 100,
        biddingDeadline: 48,
        gracePeriod: 5,
        maxMissedPayments: 1,
        autoSuspension: false,
        advanceNotice: 14,
        withdrawalPenalty: 200
      },
      statistics: {
        totalCollected: 0,
        totalDistributed: 0,
        totalPending: 0,
        activeMembers: 8,
        suspendedMembers: 0,
        completedMembers: 0,
        onTimePayments: 0,
        latePayments: 0,
        missedPayments: 0,
        completedCycles: 0,
        pendingCycles: 15,
        collectionRate: 0,
        onTimeRate: 0,
        completionRate: 0
      },
      settings: {
        enableEmailNotifications: true,
        enableSmsNotifications: false,
        enablePushNotifications: true,
        paymentReminderDays: [5, 2],
        cycleReminderDays: [10, 3],
        autoProcessPayments: true,
        autoDistributeFunds: true,
        autoSuspendDefaulters: false,
        showMemberDetails: false,
        showPaymentHistory: true,
        allowMemberCommunication: false,
        requireTwoFactorAuth: false,
        requirePaymentVerification: false,
        logAllActivities: true
      }
    },
    {
      id: '3',
      name: 'جمعية الحي',
      description: 'جمعية مالية لسكان الحي الواحد',
      type: 'discount',
      status: 'completed',
      totalMembers: 10,
      monthlyAmount: 8000,
      currency: 'SAR',
      duration: 10,
      startDate: '2023-01-01',
      endDate: '2023-10-31',
      createdAt: '2022-12-01T08:00:00Z',
      updatedAt: '2023-11-01T16:00:00Z',
      members: [],
      cycles: [],
      contributions: [],
      organizer: {
        id: 'org3',
        name: 'محمد الجار',
        email: '<EMAIL>',
        phone: '+966503456789',
        nationalId: '3456789012',
        organizingExperience: 7,
        successfulJamiyas: 12,
        rating: 4.9,
        isVerified: true
      },
      guarantors: [],
      rules: {
        allowEarlyWithdrawal: true,
        allowLateJoining: false,
        requireGuarantor: true,
        lateFee: 200,
        earlyWithdrawalDiscount: 5,
        lateWithdrawalBonus: 3,
        allowBidding: false,
        minimumBid: 0,
        biddingDeadline: 0,
        gracePeriod: 2,
        maxMissedPayments: 1,
        autoSuspension: true,
        advanceNotice: 10,
        withdrawalPenalty: 500
      },
      statistics: {
        totalCollected: 800000,
        totalDistributed: 800000,
        totalPending: 0,
        activeMembers: 0,
        suspendedMembers: 0,
        completedMembers: 10,
        onTimePayments: 98,
        latePayments: 2,
        missedPayments: 0,
        completedCycles: 10,
        pendingCycles: 0,
        collectionRate: 100,
        onTimeRate: 98,
        completionRate: 100
      },
      settings: {
        enableEmailNotifications: true,
        enableSmsNotifications: true,
        enablePushNotifications: false,
        paymentReminderDays: [7, 3, 1],
        cycleReminderDays: [7, 1],
        autoProcessPayments: false,
        autoDistributeFunds: false,
        autoSuspendDefaulters: true,
        showMemberDetails: true,
        showPaymentHistory: true,
        allowMemberCommunication: true,
        requireTwoFactorAuth: true,
        requirePaymentVerification: true,
        logAllActivities: true
      }
    }
  ];

  useEffect(() => {
    const loadJamiyas = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setJamiyas(mockJamiyas);
      setIsLoading(false);
    };

    loadJamiyas();
  }, []);

  // تصفية الجمعيات
  const filteredJamiyas = jamiyas.filter(jamiya => {
    const matchesSearch = searchTerm === '' || 
      jamiya.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      jamiya.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      jamiya.organizer.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = selectedType === 'all' || jamiya.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || jamiya.status === selectedStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  // تقسيم الصفحات
  const totalPages = Math.ceil(filteredJamiyas.length / jamiyasPerPage);
  const startIndex = (currentPage - 1) * jamiyasPerPage;
  const paginatedJamiyas = filteredJamiyas.slice(startIndex, startIndex + jamiyasPerPage);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusIcon = (status: JamiyaStatus) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-blue-500" />;
      case 'recruiting':
        return <UsersIcon className="h-4 w-4 text-yellow-500" />;
      case 'suspended':
        return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      case 'planning':
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: JamiyaStatus) => {
    switch (status) {
      case 'planning': return 'قيد التخطيط';
      case 'recruiting': return 'جمع الأعضاء';
      case 'active': return 'نشطة';
      case 'completed': return 'مكتملة';
      case 'suspended': return 'موقوفة';
      case 'cancelled': return 'ملغية';
      default: return status;
    }
  };

  const getTypeText = (type: JamiyaType) => {
    switch (type) {
      case 'simple': return 'بسيطة';
      case 'auction': return 'بالمزايدة';
      case 'discount': return 'بالخصم';
      case 'flexible': return 'مرنة';
      default: return type;
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['jamiya_management']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الجمعيات...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['jamiya_management']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">منصة الجمعيات المالية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/jamiya-dashboard">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    لوحة التحكم
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <UserGroupIcon className="h-8 w-8 text-blue-600 ml-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">إدارة الجمعيات المالية</h1>
                  <p className="text-gray-600">عرض وإدارة جميع الجمعيات المالية والحَصص</p>
                </div>
              </div>

              <Link href="/jamiyas/create">
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                  <PlusIcon className="h-5 w-5 ml-2" />
                  إنشاء جمعية جديدة
                </button>
              </Link>
            </div>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <UserGroupIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">{jamiyas.length}</p>
                  <p className="text-sm text-gray-600">إجمالي الجمعيات</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircleIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {jamiyas.filter(j => j.status === 'active').length}
                  </p>
                  <p className="text-sm text-gray-600">جمعيات نشطة</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <BanknotesIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(jamiyas.reduce((sum, j) => sum + j.statistics.totalCollected, 0))}
                  </p>
                  <p className="text-sm text-gray-600">إجمالي المجموع</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <UsersIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {jamiyas.reduce((sum, j) => sum + j.statistics.activeMembers, 0)}
                  </p>
                  <p className="text-sm text-gray-600">إجمالي الأعضاء</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Jamiyas List - Simple View */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            {paginatedJamiyas.map((jamiya) => (
              <div
                key={jamiya.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 ml-3">{jamiya.name}</h3>
                      <div className="flex items-center ml-3">
                        {getStatusIcon(jamiya.status)}
                        <span className="mr-1 text-sm text-gray-600">
                          {getStatusText(jamiya.status)}
                        </span>
                      </div>
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                        {getTypeText(jamiya.type)}
                      </span>
                    </div>

                    <p className="text-gray-600 mb-4">{jamiya.description}</p>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <CurrencyDollarIcon className="h-4 w-4 ml-2" />
                        <span>{formatCurrency(jamiya.monthlyAmount)} شهرياً</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-500">
                        <UsersIcon className="h-4 w-4 ml-2" />
                        <span>{jamiya.statistics.activeMembers}/{jamiya.totalMembers} عضو</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-500">
                        <CalendarIcon className="h-4 w-4 ml-2" />
                        <span>{jamiya.duration} شهر</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-500">
                        <BanknotesIcon className="h-4 w-4 ml-2" />
                        <span>{formatCurrency(jamiya.statistics.totalCollected)}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                        <div>
                          <span className="font-medium">المنظم: </span>
                          <span>{jamiya.organizer.name}</span>
                        </div>

                        <div>
                          <span className="font-medium">معدل الالتزام: </span>
                          <span className="text-green-600">{jamiya.statistics.onTimeRate.toFixed(1)}%</span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Link href={`/jamiyas/${jamiya.id}`}>
                          <button className="text-blue-600 hover:text-blue-900 p-2 rounded-lg hover:bg-blue-50">
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        </Link>
                        <Link href={`/jamiyas/${jamiya.id}/edit`}>
                          <button className="text-green-600 hover:text-green-900 p-2 rounded-lg hover:bg-green-50">
                            <PencilIcon className="h-4 w-4" />
                          </button>
                        </Link>
                        <button className="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50">
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
