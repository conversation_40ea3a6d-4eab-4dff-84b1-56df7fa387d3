# ==============================================
# Redis Configuration for Aljameia Platform
# ==============================================

# Network
bind 0.0.0.0
port 6379
protected-mode yes
tcp-backlog 511
timeout 0
tcp-keepalive 300

# General
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# Snapshotting
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

# Replication
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600
replica-priority 100

# Security
requirepass redis_password_123
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b835c3f8a5d2e7f1"

# Memory Management
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Lazy Freeing
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# Append Only File
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Lua Scripting
lua-time-limit 5000

# Redis Cluster
cluster-enabled no

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency Monitor
latency-monitor-threshold 0

# Event Notification
notify-keyspace-events ""

# Hash
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List
list-max-ziplist-size -2
list-compress-depth 0

# Set
set-max-intset-entries 512

# Sorted Set
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog
hll-sparse-max-bytes 3000

# Streams
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active Rehashing
activerehashing yes

# Client Output Buffer Limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client Query Buffer Limit
client-query-buffer-limit 1gb

# Protocol Buffer Limit
proto-max-bulk-len 512mb

# Frequency
hz 10

# Dynamic HZ
dynamic-hz yes

# AOF Rewrite Incremental Fsync
aof-rewrite-incremental-fsync yes

# RDB Save Incremental Fsync
rdb-save-incremental-fsync yes

# LFU
lfu-log-factor 10
lfu-decay-time 1

# ==============================================
# Custom Configuration for Aljameia
# ==============================================

# Session Management
# Sessions expire after 24 hours
# Format: session:user_id -> session_data

# Cache Configuration
# API responses cached for 5 minutes
# User data cached for 15 minutes
# Static data cached for 1 hour

# Rate Limiting
# API rate limits stored with TTL
# Format: rate_limit:ip:endpoint -> count

# Real-time Features
# Socket.io sessions and room management
# Live notifications and updates

# Queue Management
# Background job queues for:
# - Email sending
# - SMS notifications
# - Payment processing
# - Report generation

# Monitoring
# Performance metrics and health checks
# Error tracking and logging
