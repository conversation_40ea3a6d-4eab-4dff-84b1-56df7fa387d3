import { useState, ReactNode } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  useTheme,
  useMediaQuery,
  Container,
  Fade,
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
} from '@mui/icons-material';

import { useAuth } from '@/hooks/useAuth';
import Sidebar from './Sidebar';
import Header from './Header';
import Footer from './Footer';
import AuthGuard from '@/components/Auth/AuthGuard';
import LoadingSpinner from '@/components/Common/LoadingSpinner';

const DRAWER_WIDTH = 280;
const MINI_DRAWER_WIDTH = 64;

interface LayoutProps {
  children: ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const { user, isLoading } = useAuth();

  const [mobileOpen, setMobileOpen] = useState(false);
  const [desktopOpen, setDesktopOpen] = useState(true);

  // Pages that don't require authentication
  const publicPages = ['/login', '/register', '/forgot-password', '/reset-password'];
  const isPublicPage = publicPages.includes(router.pathname);

  // Pages that don't need the full layout
  const authPages = ['/login', '/register', '/forgot-password', '/reset-password'];
  const isAuthPage = authPages.includes(router.pathname);

  const handleDrawerToggle = () => {
    if (isMobile) {
      setMobileOpen(!mobileOpen);
    } else {
      setDesktopOpen(!desktopOpen);
    }
  };

  const handleMobileDrawerClose = () => {
    setMobileOpen(false);
  };

  // Show loading spinner while checking authentication
  if (isLoading && !isPublicPage) {
    return <LoadingSpinner />;
  }

  // Render auth pages without layout
  if (isAuthPage) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
        }}
      >
        {children}
      </Box>
    );
  }

  // Render main layout with authentication guard
  return (
    <AuthGuard>
      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        {/* App Bar */}
        <AppBar
          position="fixed"
          sx={{
            width: {
              lg: desktopOpen 
                ? `calc(100% - ${DRAWER_WIDTH}px)` 
                : `calc(100% - ${MINI_DRAWER_WIDTH}px)`,
            },
            ml: {
              lg: desktopOpen ? `${DRAWER_WIDTH}px` : `${MINI_DRAWER_WIDTH}px`,
            },
            transition: theme.transitions.create(['width', 'margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
            backgroundColor: theme.palette.background.paper,
            color: theme.palette.text.primary,
            boxShadow: theme.shadows[1],
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Header 
            onMenuClick={handleDrawerToggle}
            drawerOpen={isMobile ? mobileOpen : desktopOpen}
          />
        </AppBar>

        {/* Navigation Drawer */}
        <Box
          component="nav"
          sx={{
            width: { lg: desktopOpen ? DRAWER_WIDTH : MINI_DRAWER_WIDTH },
            flexShrink: { lg: 0 },
          }}
        >
          {/* Mobile Drawer */}
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleMobileDrawerClose}
            ModalProps={{
              keepMounted: true, // Better open performance on mobile.
            }}
            sx={{
              display: { xs: 'block', lg: 'none' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: DRAWER_WIDTH,
                backgroundColor: theme.palette.background.paper,
                borderRight: `1px solid ${theme.palette.divider}`,
              },
            }}
          >
            <Sidebar 
              onClose={handleMobileDrawerClose}
              variant="temporary"
              open={true}
            />
          </Drawer>

          {/* Desktop Drawer */}
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: 'none', lg: 'block' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: desktopOpen ? DRAWER_WIDTH : MINI_DRAWER_WIDTH,
                backgroundColor: theme.palette.background.paper,
                borderRight: `1px solid ${theme.palette.divider}`,
                transition: theme.transitions.create('width', {
                  easing: theme.transitions.easing.sharp,
                  duration: theme.transitions.duration.enteringScreen,
                }),
                overflowX: 'hidden',
              },
            }}
            open={desktopOpen}
          >
            <Sidebar 
              variant="permanent"
              open={desktopOpen}
            />
          </Drawer>
        </Box>

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            width: {
              lg: desktopOpen 
                ? `calc(100% - ${DRAWER_WIDTH}px)` 
                : `calc(100% - ${MINI_DRAWER_WIDTH}px)`,
            },
            minHeight: '100vh',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: theme.palette.background.default,
          }}
        >
          {/* Toolbar spacer */}
          <Toolbar />

          {/* Page Content */}
          <Box
            sx={{
              flexGrow: 1,
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
            }}
          >
            <Fade in timeout={300}>
              <Box sx={{ flexGrow: 1 }}>
                {children}
              </Box>
            </Fade>
          </Box>

          {/* Footer */}
          <Footer />
        </Box>
      </Box>
    </AuthGuard>
  );
};

export default Layout;
