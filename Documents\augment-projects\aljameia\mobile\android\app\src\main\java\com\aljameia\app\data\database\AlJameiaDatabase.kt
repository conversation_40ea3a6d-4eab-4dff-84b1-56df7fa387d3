package com.aljameia.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.aljameia.app.data.dao.TransactionDao
import com.aljameia.app.data.dao.UserDao
import com.aljameia.app.data.entities.Document
import com.aljameia.app.data.entities.Event
import com.aljameia.app.data.entities.Transaction
import com.aljameia.app.data.entities.User

@Database(
    entities = [
        User::class,
        Transaction::class,
        Document::class,
        Event::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(Converters::class)
abstract class AlJameiaDatabase : RoomDatabase() {
    
    abstract fun userDao(): UserDao
    abstract fun transactionDao(): TransactionDao
    // abstract fun documentDao(): DocumentDao
    // abstract fun eventDao(): EventDao
    
    companion object {
        @Volatile
        private var INSTANCE: AlJameiaDatabase? = null
        
        fun getDatabase(context: Context): AlJameiaDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AlJameiaDatabase::class.java,
                    "aljameia_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
