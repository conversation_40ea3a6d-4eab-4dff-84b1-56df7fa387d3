#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n${description}...`, 'cyan');
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      cwd: process.cwd()
    });
    log(`✅ ${description} completed successfully`, 'green');
    return { success: true, output };
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    log(error.stdout || error.message, 'red');
    return { success: false, error: error.stdout || error.message };
  }
}

function generateTestReport(results) {
  const reportPath = path.join(process.cwd(), 'test-report.md');
  const timestamp = new Date().toISOString();
  
  let report = `# Test Report\n\n`;
  report += `**Generated:** ${timestamp}\n\n`;
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  
  results.forEach(result => {
    report += `## ${result.name}\n\n`;
    if (result.success) {
      report += `✅ **Status:** PASSED\n\n`;
      passedTests++;
    } else {
      report += `❌ **Status:** FAILED\n\n`;
      report += `**Error:**\n\`\`\`\n${result.error}\n\`\`\`\n\n`;
      failedTests++;
    }
    totalTests++;
  });
  
  report += `## Summary\n\n`;
  report += `- **Total Tests:** ${totalTests}\n`;
  report += `- **Passed:** ${passedTests}\n`;
  report += `- **Failed:** ${failedTests}\n`;
  report += `- **Success Rate:** ${((passedTests / totalTests) * 100).toFixed(2)}%\n`;
  
  fs.writeFileSync(reportPath, report);
  log(`\n📊 Test report generated: ${reportPath}`, 'magenta');
}

function checkTestEnvironment() {
  log('🔍 Checking test environment...', 'yellow');
  
  // Check if Jest is installed
  try {
    execSync('npx jest --version', { stdio: 'pipe' });
    log('✅ Jest is installed', 'green');
  } catch (error) {
    log('❌ Jest is not installed', 'red');
    return false;
  }
  
  // Check if test files exist
  const testDirs = ['__tests__', '__tests__/services', '__tests__/components', '__tests__/contexts'];
  let hasTests = false;
  
  testDirs.forEach(dir => {
    const testPath = path.join(process.cwd(), dir);
    if (fs.existsSync(testPath)) {
      const files = fs.readdirSync(testPath).filter(file => 
        file.endsWith('.test.ts') || file.endsWith('.test.tsx')
      );
      if (files.length > 0) {
        log(`✅ Found ${files.length} test files in ${dir}`, 'green');
        hasTests = true;
      }
    }
  });
  
  if (!hasTests) {
    log('❌ No test files found', 'red');
    return false;
  }
  
  return true;
}

function main() {
  log('🚀 Starting comprehensive test suite...', 'bright');
  
  // Check environment
  if (!checkTestEnvironment()) {
    log('❌ Test environment check failed', 'red');
    process.exit(1);
  }
  
  const results = [];
  
  // Run different test suites
  const testSuites = [
    {
      name: 'Unit Tests',
      command: 'npm run test -- --testPathPattern="__tests__/(services|utils)" --verbose',
      description: 'Running unit tests for services and utilities'
    },
    {
      name: 'Component Tests',
      command: 'npm run test -- --testPathPattern="__tests__/components" --verbose',
      description: 'Running component tests'
    },
    {
      name: 'Context Tests',
      command: 'npm run test -- --testPathPattern="__tests__/contexts" --verbose',
      description: 'Running context tests'
    },
    {
      name: 'Integration Tests',
      command: 'npm run test -- --testPathPattern="__tests__/integration" --verbose',
      description: 'Running integration tests'
    },
    {
      name: 'Performance Tests',
      command: 'npm run test -- --testPathPattern="__tests__/performance" --verbose',
      description: 'Running performance tests'
    }
  ];
  
  // Run each test suite
  testSuites.forEach(suite => {
    const result = runCommand(suite.command, suite.description);
    results.push({
      name: suite.name,
      success: result.success,
      output: result.output,
      error: result.error
    });
  });
  
  // Run coverage report
  log('\n📊 Generating coverage report...', 'cyan');
  const coverageResult = runCommand(
    'npm run test:coverage -- --silent',
    'Generating test coverage report'
  );
  
  if (coverageResult.success) {
    log('✅ Coverage report generated in coverage/ directory', 'green');
  }
  
  // Generate test report
  generateTestReport(results);
  
  // Summary
  const passedSuites = results.filter(r => r.success).length;
  const totalSuites = results.length;
  
  log('\n' + '='.repeat(50), 'bright');
  log('📋 TEST SUMMARY', 'bright');
  log('='.repeat(50), 'bright');
  log(`Total Test Suites: ${totalSuites}`, 'cyan');
  log(`Passed: ${passedSuites}`, 'green');
  log(`Failed: ${totalSuites - passedSuites}`, 'red');
  log(`Success Rate: ${((passedSuites / totalSuites) * 100).toFixed(2)}%`, 'yellow');
  
  if (passedSuites === totalSuites) {
    log('\n🎉 All tests passed! Your app is ready for deployment.', 'green');
    process.exit(0);
  } else {
    log('\n⚠️  Some tests failed. Please review the errors above.', 'yellow');
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  log('📖 Test Runner Help', 'bright');
  log('');
  log('Usage: node scripts/test-runner.js [options]', 'cyan');
  log('');
  log('Options:', 'yellow');
  log('  --help, -h     Show this help message');
  log('  --watch, -w    Run tests in watch mode');
  log('  --coverage, -c Run only coverage report');
  log('  --unit, -u     Run only unit tests');
  log('  --integration, -i Run only integration tests');
  log('');
  process.exit(0);
}

if (args.includes('--watch') || args.includes('-w')) {
  log('👀 Running tests in watch mode...', 'yellow');
  runCommand('npm run test:watch', 'Starting test watcher');
  process.exit(0);
}

if (args.includes('--coverage') || args.includes('-c')) {
  log('📊 Running coverage report only...', 'yellow');
  runCommand('npm run test:coverage', 'Generating coverage report');
  process.exit(0);
}

if (args.includes('--unit') || args.includes('-u')) {
  log('🔧 Running unit tests only...', 'yellow');
  runCommand('npm run test -- --testPathPattern="__tests__/(services|utils)"', 'Running unit tests');
  process.exit(0);
}

if (args.includes('--integration') || args.includes('-i')) {
  log('🔗 Running integration tests only...', 'yellow');
  runCommand('npm run test -- --testPathPattern="__tests__/integration"', 'Running integration tests');
  process.exit(0);
}

// Run main test suite
main();
