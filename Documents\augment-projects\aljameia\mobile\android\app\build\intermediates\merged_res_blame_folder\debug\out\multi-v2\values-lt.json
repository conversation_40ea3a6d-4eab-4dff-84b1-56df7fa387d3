{"logs": [{"outputFile": "com.aljameia.app-mergeDebugResources-104:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\85fae80ff7869a8da6251422548fb462\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,989,1058,1144,1232,1307,1387,1470", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,984,1053,1139,1227,1302,1382,1465,1587"}, "to": {"startLines": "54,55,108,110,112,130,131,190,191,192,193,195,196,199,203,204,205", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5125,5218,11428,11618,11803,13866,13943,18822,18909,18993,19063,19214,19300,19556,19925,20005,20088", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "5213,5297,11521,11718,11893,13938,14029,18904,18988,19058,19127,19295,19383,19626,20000,20083,20205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\523b9fad2b83d89c2b74860689d8fb91\\transformed\\biometric-1.1.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,259,385,526,665,795,927,1064,1161,1316,1459", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "162,254,380,521,660,790,922,1059,1156,1311,1454,1576"}, "to": {"startLines": "75,109,119,120,121,122,123,124,125,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7646,11526,12466,12592,12733,12872,13002,13134,13271,13368,13523,13666", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "7753,11613,12587,12728,12867,12997,13129,13266,13363,13518,13661,13783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\cdc55fe2502a063bcee343c082439e6f\\transformed\\navigation-ui-2.7.6\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "18593,18703", "endColumns": "109,118", "endOffsets": "18698,18817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b3e74a7425b14daa5ccd7c3c1ea8df2a\\transformed\\browser-1.4.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,105", "endOffsets": "155,260,374,480"}, "to": {"startLines": "76,116,117,118", "startColumns": "4,4,4,4", "startOffsets": "7758,12141,12246,12360", "endColumns": "104,104,113,105", "endOffsets": "7858,12241,12355,12461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f1a3dac0af0a85f165dc53a6762b4a81\\transformed\\play-services-basement-18.2.0\\res\\values-lt\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "154", "endOffsets": "349"}, "to": {"startLines": "65", "startColumns": "4", "startOffsets": "6386", "endColumns": "158", "endOffsets": "6540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\21cc0a6be925d3c2a0c747bb469e8733\\transformed\\appcompat-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "425,541,645,758,845,947,1069,1152,1232,1326,1422,1519,1615,1718,1814,1912,2008,2102,2196,2279,2388,2496,2596,2706,2811,2917,3093,19472", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "536,640,753,840,942,1064,1147,1227,1321,1417,1514,1610,1713,1809,1907,2003,2097,2191,2274,2383,2491,2591,2701,2806,2912,3088,3189,19551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c514f303830aa824ccabc1313adde6e2\\transformed\\material3-1.1.2\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,399,513,591,681,789,917,1029,1169,1249,1344,1436,1531,1652,1766,1866,2001,2132,2267,2459,2585,2699,2822,2946,3039,3136,3254,3379,3473,3572,3675,3808,3952,4057,4156,4236,4314,4398,4484,4591,4674,4757,4853,4958,5050,5145,5229,5336,5428,5523,5657,5737,5836", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "165,281,394,508,586,676,784,912,1024,1164,1244,1339,1431,1526,1647,1761,1861,1996,2127,2262,2454,2580,2694,2817,2941,3034,3131,3249,3374,3468,3567,3670,3803,3947,4052,4151,4231,4309,4393,4479,4586,4669,4752,4848,4953,5045,5140,5224,5331,5423,5518,5652,5732,5831,5924"}, "to": {"startLines": "35,36,37,38,56,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,111,114,197,200,202,206,207,208,209,210,211,212,213,214,215,216,217,218,219", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3194,3309,3425,3538,5302,7863,7953,8061,8189,8301,8441,8521,8616,8708,8803,8924,9038,9138,9273,9404,9539,9731,9857,9971,10094,10218,10311,10408,10526,10651,10745,10844,10947,11080,11224,11329,11723,11964,19388,19631,19818,20210,20293,20376,20472,20577,20669,20764,20848,20955,21047,21142,21276,21356,21455", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "3304,3420,3533,3647,5375,7948,8056,8184,8296,8436,8516,8611,8703,8798,8919,9033,9133,9268,9399,9534,9726,9852,9966,10089,10213,10306,10403,10521,10646,10740,10839,10942,11075,11219,11324,11423,11798,12037,19467,19712,19920,20288,20371,20467,20572,20664,20759,20843,20950,21042,21137,21271,21351,21450,21543"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1c0b3a48f4d4c74d31971bddddc3d282\\transformed\\play-services-base-18.1.0\\res\\values-lt\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,444,572,675,824,950,1065,1167,1329,1434,1595,1725,1874,2020,2084,2146", "endColumns": "102,147,127,102,148,125,114,101,161,104,160,129,148,145,63,61,85", "endOffsets": "295,443,571,674,823,949,1064,1166,1328,1433,1594,1724,1873,2019,2083,2145,2231"}, "to": {"startLines": "57,58,59,60,61,62,63,64,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5380,5487,5639,5771,5878,6031,6161,6280,6545,6711,6820,6985,7119,7272,7422,7490,7556", "endColumns": "106,151,131,106,152,129,118,105,165,108,164,133,152,149,67,65,89", "endOffsets": "5482,5634,5766,5873,6026,6156,6275,6381,6706,6815,6980,7114,7267,7417,7485,7551,7641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d2a56f6da0e2e4ab4da1b326f38c566a\\transformed\\material-1.11.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1163,1262,1340,1405,1515,1578,1650,1709,1783,1844,1898,2022,2083,2145,2199,2277,2411,2499,2583,2724,2803,2887,3030,3127,3204,3260,3314,3380,3455,3534,3622,3702,3778,3856,3929,4006,4113,4200,4281,4371,4463,4535,4616,4708,4763,4845,4911,4996,5083,5145,5209,5272,5344,5455,5571,5672,5781,5841,5899", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,142,96,76,55,53,65,74,78,87,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1158,1257,1335,1400,1510,1573,1645,1704,1778,1839,1893,2017,2078,2140,2194,2272,2406,2494,2578,2719,2798,2882,3025,3122,3199,3255,3309,3375,3450,3529,3617,3697,3773,3851,3924,4001,4108,4195,4276,4366,4458,4530,4611,4703,4758,4840,4906,4991,5078,5140,5204,5267,5339,5450,5566,5667,5776,5836,5894,5976"}, "to": {"startLines": "2,39,40,41,42,43,51,52,53,113,115,129,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3652,3731,3809,3892,3986,4827,4923,5041,11898,12042,13788,14034,14099,14209,14272,14344,14403,14477,14538,14592,14716,14777,14839,14893,14971,15105,15193,15277,15418,15497,15581,15724,15821,15898,15954,16008,16074,16149,16228,16316,16396,16472,16550,16623,16700,16807,16894,16975,17065,17157,17229,17310,17402,17457,17539,17605,17690,17777,17839,17903,17966,18038,18149,18265,18366,18475,18535,19132", "endLines": "7,39,40,41,42,43,51,52,53,113,115,129,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,194", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,142,96,76,55,53,65,74,78,87,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81", "endOffsets": "420,3726,3804,3887,3981,4071,4918,5036,5120,11959,12136,13861,14094,14204,14267,14339,14398,14472,14533,14587,14711,14772,14834,14888,14966,15100,15188,15272,15413,15492,15576,15719,15816,15893,15949,16003,16069,16144,16223,16311,16391,16467,16545,16618,16695,16802,16889,16970,17060,17152,17224,17305,17397,17452,17534,17600,17685,17772,17834,17898,17961,18033,18144,18260,18361,18470,18530,18588,19209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5081b8eacd451e5b41f2b566f41c579e\\transformed\\core-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "44,45,46,47,48,49,50,201", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4076,4174,4284,4383,4486,4597,4707,19717", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "4169,4279,4378,4481,4592,4702,4822,19813"}}]}]}