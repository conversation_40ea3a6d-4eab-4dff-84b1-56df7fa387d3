# ==============================================
# Performance Optimization for Aljameia Platform
# ==============================================

# Gzip Compression
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_proxied any;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    text/csv
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    application/rdf+xml
    application/rss+xml
    application/xhtml+xml
    application/x-font-ttf
    application/x-web-app-manifest+json
    font/opentype
    image/svg+xml
    image/x-icon
    text/cache-manifest;

# Brotli Compression (if module is available)
# brotli on;
# brotli_comp_level 6;
# brotli_min_length 1024;
# brotli_types
#     text/plain
#     text/css
#     text/xml
#     text/javascript
#     application/json
#     application/javascript
#     application/xml+rss
#     application/atom+xml
#     image/svg+xml;

# File Caching
open_file_cache max=1000 inactive=20s;
open_file_cache_valid 30s;
open_file_cache_min_uses 2;
open_file_cache_errors on;

# Sendfile and TCP optimizations
sendfile on;
tcp_nopush on;
tcp_nodelay on;

# Keepalive settings
keepalive_timeout 65;
keepalive_requests 100;

# Buffer sizes
client_body_buffer_size 128k;
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;
output_buffers 1 32k;
postpone_output 1460;

# Proxy buffering
proxy_buffering on;
proxy_buffer_size 128k;
proxy_buffers 4 256k;
proxy_busy_buffers_size 256k;
proxy_temp_file_write_size 256k;

# FastCGI buffering (if using PHP-FPM)
fastcgi_buffering on;
fastcgi_buffer_size 128k;
fastcgi_buffers 4 256k;
fastcgi_busy_buffers_size 256k;
fastcgi_temp_file_write_size 256k;

# Cache zones
proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=100m inactive=60m use_temp_path=off;
proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=500m inactive=1d use_temp_path=off;

# Cache key
proxy_cache_key "$scheme$request_method$host$request_uri";

# Cache conditions
map $request_method $no_cache {
    default 0;
    POST 1;
    PUT 1;
    DELETE 1;
    PATCH 1;
}

map $request_uri $cache_bypass {
    default 0;
    ~*/api/auth 1;
    ~*/api/payments 1;
    ~*/api/users/me 1;
    ~*/socket.io 1;
}

# Static file caching
location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|tar|gz)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary "Accept-Encoding";
    access_log off;
    
    # Enable gzip for static files
    gzip_static on;
    
    # Set ETag
    etag on;
    
    # Handle conditional requests
    if_modified_since exact;
}

# Font files caching
location ~* \.(woff|woff2|ttf|eot|otf)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Access-Control-Allow-Origin "*";
    access_log off;
}

# Image optimization
location ~* \.(jpg|jpeg|png|gif|webp|avif)$ {
    expires 1M;
    add_header Cache-Control "public, immutable";
    add_header Vary "Accept";
    access_log off;
    
    # WebP support
    location ~* \.(jpg|jpeg|png)$ {
        add_header Vary "Accept";
        try_files $uri$webp_suffix $uri =404;
    }
}

# WebP suffix mapping
map $http_accept $webp_suffix {
    default "";
    "~*webp" ".webp";
}

# API response caching
location /api/ {
    proxy_cache api_cache;
    proxy_cache_valid 200 302 5m;
    proxy_cache_valid 404 1m;
    proxy_cache_bypass $no_cache $cache_bypass;
    proxy_no_cache $no_cache $cache_bypass;
    
    # Cache headers
    add_header X-Cache-Status $upstream_cache_status;
    
    # Proxy settings
    proxy_pass http://backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Static content caching
location /static/ {
    proxy_cache static_cache;
    proxy_cache_valid 200 1d;
    proxy_cache_valid 404 1m;
    
    add_header X-Cache-Status $upstream_cache_status;
    
    expires 1d;
    add_header Cache-Control "public, immutable";
}

# Microcaching for dynamic content
location / {
    proxy_cache api_cache;
    proxy_cache_valid 200 1s;
    proxy_cache_bypass $no_cache $cache_bypass;
    proxy_no_cache $no_cache $cache_bypass;
    
    add_header X-Cache-Status $upstream_cache_status;
}

# Cache purging endpoint (for admin)
location ~ /purge(/.*) {
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;
    deny all;
    
    proxy_cache_purge api_cache "$scheme$request_method$host$1";
}

# Health check with no caching
location /health {
    proxy_cache off;
    proxy_pass http://backend/health;
    access_log off;
}

# Monitoring endpoints
location /nginx_status {
    stub_status on;
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;
    deny all;
    access_log off;
}

# Real-time endpoints (no caching)
location /socket.io/ {
    proxy_cache off;
    proxy_buffering off;
    proxy_pass http://backend;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Preload critical resources
location = / {
    add_header Link "</static/css/main.css>; rel=preload; as=style" always;
    add_header Link "</static/js/main.js>; rel=preload; as=script" always;
    add_header Link "</static/fonts/cairo.woff2>; rel=preload; as=font; type=font/woff2; crossorigin" always;
}
