'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';

// Types
interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'member' | 'moderator';
  avatar?: string;
  permissions: string[];
  membershipNumber?: string;
  membershipType: 'basic' | 'premium' | 'vip';
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
  checkAuthStatus: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
}

interface RegisterData {
  name: string;
  email: string;
  phone?: string;
  password: string;
  membershipType: 'basic' | 'premium' | 'vip';
}

interface AuthProviderProps {
  children: ReactNode;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!user;

  // Check for existing session on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        setIsLoading(false);
        return;
      }

      // In a real app, validate token with backend
      // For now, simulate with mock data
      const mockUser: User = {
        id: 'user_001',
        name: 'أحمد الراشد',
        email: '<EMAIL>',
        phone: '+966501234567',
        role: 'admin',
        permissions: ['read', 'write', 'admin', 'financial', 'members', 'events'],
        membershipNumber: 'AJ2024001',
        membershipType: 'premium',
        isActive: true,
        emailVerified: true,
        createdAt: '2024-01-15T10:00:00Z',
        lastLoginAt: new Date().toISOString(),
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
      };
      
      setUser(mockUser);
    } catch (error) {
      console.error('Auth check failed:', error);
      localStorage.removeItem('auth_token');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string, rememberMe = false) => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock validation
      if (email === '<EMAIL>' && password === 'password123') {
        const token = 'mock_token_' + Date.now();
        localStorage.setItem('auth_token', token);
        
        if (rememberMe) {
          localStorage.setItem('remember_me', 'true');
        }
        
        const mockUser: User = {
          id: 'user_001',
          name: 'أحمد الراشد',
          email: email,
          phone: '+966501234567',
          role: 'admin',
          permissions: ['read', 'write', 'admin', 'financial', 'members', 'events'],
          membershipNumber: 'AJ2024001',
          membershipType: 'premium',
          isActive: true,
          emailVerified: true,
          createdAt: '2024-01-15T10:00:00Z',
          lastLoginAt: new Date().toISOString(),
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
        };
        
        setUser(mockUser);
        router.push('/dashboard');
      } else {
        throw new Error('البريد الإلكتروني أو كلمة المرور غير صحيحة');
      }
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      
      // In real app, call logout API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      localStorage.removeItem('auth_token');
      localStorage.removeItem('remember_me');
      setUser(null);
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock successful registration
      const token = 'mock_token_' + Date.now();
      localStorage.setItem('auth_token', token);
      
      const newUser: User = {
        id: 'user_' + Date.now(),
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        role: 'member',
        permissions: ['read'],
        membershipNumber: 'AJ' + new Date().getFullYear() + Math.floor(Math.random() * 1000).toString().padStart(3, '0'),
        membershipType: userData.membershipType,
        isActive: true,
        emailVerified: false,
        createdAt: new Date().toISOString(),
      };
      
      setUser(newUser);
      router.push('/dashboard');
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateUser = async (userData: Partial<User>) => {
    try {
      if (!user) throw new Error('No user logged in');
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
    } catch (error) {
      throw error;
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    return user.permissions.includes(permission) || user.permissions.includes('admin');
  };

  const hasRole = (role: string): boolean => {
    if (!user) return false;
    return user.role === role || user.role === 'admin';
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    register,
    updateUser,
    checkAuthStatus,
    hasPermission,
    hasRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export { AuthContext };
