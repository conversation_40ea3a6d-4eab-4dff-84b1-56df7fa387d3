# ==============================================
# Security Configuration for Aljameia Platform
# ==============================================

# Hide Nginx version
server_tokens off;

# Rate Limiting Zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=20r/s;

# Connection Limiting
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
limit_conn_zone $server_name zone=conn_limit_per_server:10m;

# Request Size Limits
client_max_body_size 10m;
client_body_buffer_size 128k;
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;

# Timeout Settings
client_body_timeout 12;
client_header_timeout 12;
keepalive_timeout 15;
send_timeout 10;

# Buffer Overflow Protection
client_body_buffer_size 1K;
client_header_buffer_size 1k;
client_max_body_size 1k;
large_client_header_buffers 2 1k;

# Security Headers Map
map $sent_http_content_type $content_security_policy {
    default "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://www.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' wss: https:; frame-src 'self' https:; object-src 'none'; base-uri 'self'; form-action 'self';";
    ~^text/html "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://www.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' wss: https:; frame-src 'self' https:; object-src 'none'; base-uri 'self'; form-action 'self';";
}

# Block common attack patterns
map $request_uri $blocked_uri {
    default 0;
    ~*\.(php|asp|aspx|jsp|cgi)$ 1;
    ~*/\.(git|svn|hg)/ 1;
    ~*/wp-admin/ 1;
    ~*/wp-login.php 1;
    ~*/phpmyadmin/ 1;
    ~*/admin/ 1;
    ~*/administrator/ 1;
    ~*/(config|configuration)/ 1;
    ~*/\.env 1;
    ~*/\.htaccess 1;
    ~*/\.htpasswd 1;
}

# Block malicious user agents
map $http_user_agent $blocked_agent {
    default 0;
    ~*bot 0;
    ~*spider 0;
    ~*crawler 0;
    ~*scanner 1;
    ~*nikto 1;
    ~*sqlmap 1;
    ~*nmap 1;
    ~*masscan 1;
    ~*zmap 1;
    ~*curl 0;
    ~*wget 0;
    "" 1;
}

# Block suspicious request methods
map $request_method $blocked_method {
    default 0;
    ~*^(TRACE|DELETE|PUT|PATCH)$ 1;
}

# GeoIP blocking (if enabled)
# map $geoip_country_code $blocked_country {
#     default 0;
#     CN 1;
#     RU 1;
#     KP 1;
# }

# Real IP configuration
set_real_ip_from 10.0.0.0/8;
set_real_ip_from **********/12;
set_real_ip_from ***********/16;
set_real_ip_from 127.0.0.1;
real_ip_header X-Forwarded-For;
real_ip_recursive on;

# Log format for security events
log_format security '$remote_addr - $remote_user [$time_local] '
                   '"$request" $status $body_bytes_sent '
                   '"$http_referer" "$http_user_agent" '
                   '$request_time $upstream_response_time '
                   'blocked_uri=$blocked_uri blocked_agent=$blocked_agent '
                   'blocked_method=$blocked_method';

# Deny access to hidden files
location ~ /\. {
    deny all;
    access_log off;
    log_not_found off;
}

# Deny access to backup files
location ~ ~$ {
    deny all;
    access_log off;
    log_not_found off;
}

# Deny access to configuration files
location ~* \.(conf|config|cfg|ini|log|bak|backup|old|tmp|temp)$ {
    deny all;
    access_log off;
    log_not_found off;
}

# Block SQL injection attempts
location ~* (union.*select|select.*from|insert.*into|delete.*from|drop.*table) {
    deny all;
    access_log /var/log/nginx/security.log security;
}

# Block XSS attempts
location ~* (<script|javascript:|vbscript:|onload=|onerror=|onclick=) {
    deny all;
    access_log /var/log/nginx/security.log security;
}

# Block file inclusion attempts
location ~* (\.\.\/|\.\.\\|etc\/passwd|proc\/self\/environ) {
    deny all;
    access_log /var/log/nginx/security.log security;
}

# Security headers for all responses
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()" always;
add_header Content-Security-Policy $content_security_policy always;

# HSTS for HTTPS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# Feature Policy
add_header Feature-Policy "camera 'none'; microphone 'none'; geolocation 'self'; payment 'self'" always;
