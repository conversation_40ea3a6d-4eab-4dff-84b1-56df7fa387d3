1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.aljameia.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.CAMERA" />
15-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:9:5-65
15-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:9:22-62
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:10:5-80
16-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:10:22-77
17    <uses-permission
17-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:11:22-78
19        android:maxSdkVersion="28" />
19-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:12:9-35
20    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
20-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:13:5-74
20-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:13:22-71
21    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
21-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:14:5-72
21-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:14:22-69
22    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
22-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:15:5-79
22-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:15:22-76
23    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
23-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:16:5-81
23-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:16:22-78
24    <uses-permission android:name="android.permission.VIBRATE" />
24-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:17:5-66
24-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:17:22-63
25    <uses-permission android:name="android.permission.WAKE_LOCK" />
25-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:18:5-68
25-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:18:22-65
26    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
26-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:19:5-81
26-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:19:22-78
27    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
27-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:20:5-77
27-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:20:22-74
28    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
28-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:21:5-95
28-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:21:22-92
29
30    <!-- Firebase Cloud Messaging -->
31    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
31-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:24:5-82
31-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:24:22-79
32
33    <!-- Features -->
34    <uses-feature
34-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:27:5-29:36
35        android:name="android.hardware.camera"
35-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:28:9-47
36        android:required="false" />
36-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:29:9-33
37    <uses-feature
37-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:30:5-32:36
38        android:name="android.hardware.fingerprint"
38-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:31:9-52
39        android:required="false" />
39-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:32:9-33
40    <uses-feature
40-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:33:5-35:36
41        android:name="android.hardware.location"
41-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:34:9-49
42        android:required="false" />
42-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:35:9-33
43    <uses-feature
43-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:36:5-38:36
44        android:name="android.hardware.location.gps"
44-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:37:9-53
45        android:required="false" />
45-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:38:9-33
46
47    <!-- Queries for Android 11+ -->
48    <queries>
48-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:227:5-249:15
49        <intent>
49-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:228:9-232:18
50            <action android:name="android.intent.action.VIEW" />
50-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:17-69
50-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:25-66
51
52            <category android:name="android.intent.category.BROWSABLE" />
52-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:17-78
52-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:27-75
53
54            <data android:scheme="https" />
54-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
54-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:23-45
55        </intent>
56        <intent>
56-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:233:9-237:18
57            <action android:name="android.intent.action.VIEW" />
57-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:17-69
57-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:25-66
58
59            <category android:name="android.intent.category.BROWSABLE" />
59-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:17-78
59-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:27-75
60
61            <data android:scheme="http" />
61-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
61-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:23-45
62        </intent>
63        <intent>
63-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:238:9-241:18
64            <action android:name="android.intent.action.SEND" />
64-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:239:13-65
64-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:239:21-62
65
66            <data android:mimeType="*/*" />
66-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
66-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:240:19-41
67        </intent>
68        <intent>
68-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:242:9-245:18
69            <action android:name="android.intent.action.SENDTO" />
69-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:243:13-67
69-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:243:21-64
70
71            <data android:scheme="mailto" />
71-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
71-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:23-45
72        </intent>
73        <intent>
73-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:246:9-248:18
74            <action android:name="android.intent.action.DIAL" />
74-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:247:13-65
74-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:247:21-62
75        </intent>
76        <!-- Needs to be explicitly declared on Android R+ -->
77        <package android:name="com.google.android.apps.maps" />
77-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
77-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
78    </queries>
79
80    <uses-feature
80-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
81        android:glEsVersion="0x00020000"
81-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
82        android:required="true" />
82-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
83
84    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
84-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:5-77
84-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:22-74
85    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
85-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
85-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
86    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
86-->[com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
86-->[com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ede515efda956d0e3465e0f74aff02f9\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:22-76
87
88    <permission
88-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
89        android:name="com.aljameia.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
89-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
90        android:protectionLevel="signature" />
90-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
91
92    <uses-permission android:name="com.aljameia.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
92-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
92-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
93    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
93-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
93-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
94    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
94-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
94-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
95
96    <uses-feature
96-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
97        android:name="android.hardware.camera.front"
97-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
98        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
98-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
99    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
100    <uses-feature
100-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
101        android:name="android.hardware.camera.autofocus"
101-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
102        android:required="false" />
102-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
103    <uses-feature
103-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
104        android:name="android.hardware.camera.flash"
104-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
105        android:required="false" />
105-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
106    <uses-feature
106-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
107        android:name="android.hardware.screen.landscape"
107-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
108        android:required="false" />
108-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
109    <uses-feature
109-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
110        android:name="android.hardware.wifi"
110-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
111        android:required="false" />
111-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
112
113    <application
113-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:40:5-224:19
114        android:name="com.aljameia.app.app.AlJameiaApplication"
114-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:41:9-48
115        android:allowBackup="false"
115-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:42:9-36
116        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
116-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5081b8eacd451e5b41f2b566f41c579e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
117        android:dataExtractionRules="@xml/data_extraction_rules"
117-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:43:9-65
118        android:debuggable="true"
119        android:extractNativeLibs="false"
120        android:fullBackupContent="@xml/backup_rules"
120-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:44:9-54
121        android:icon="@mipmap/ic_launcher"
121-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:45:9-43
122        android:label="@string/app_name"
122-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:46:9-41
123        android:networkSecurityConfig="@xml/network_security_config"
123-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:50:9-69
124        android:requestLegacyExternalStorage="false"
124-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:51:9-53
125        android:roundIcon="@mipmap/ic_launcher_round"
125-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:47:9-54
126        android:theme="@style/Theme.AlJameia"
126-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:48:9-46
127        android:usesCleartextTraffic="false" >
127-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:49:9-45
128
129        <!-- Main Activity -->
130        <activity
130-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:55:9-77:20
131            android:name="com.aljameia.app.app.ui.main.MainActivity"
131-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:56:13-53
132            android:exported="false"
132-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:57:13-37
133            android:launchMode="singleTop"
133-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:58:13-43
134            android:screenOrientation="portrait"
134-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:59:13-49
135            android:windowSoftInputMode="adjustResize" >
135-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:60:13-55
136
137            <!-- Deep Links -->
138            <intent-filter android:autoVerify="true" >
138-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:63:13-69:29
138-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:63:28-53
139                <action android:name="android.intent.action.VIEW" />
139-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:17-69
139-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:25-66
140
141                <category android:name="android.intent.category.DEFAULT" />
141-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:65:17-76
141-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:65:27-73
142                <category android:name="android.intent.category.BROWSABLE" />
142-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:17-78
142-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:27-75
143
144                <data
144-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
145                    android:host="aljameia.com"
145-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:68:21-48
146                    android:scheme="https" />
146-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:23-45
147            </intent-filter>
148            <intent-filter>
148-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:71:13-76:29
149                <action android:name="android.intent.action.VIEW" />
149-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:17-69
149-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:25-66
150
151                <category android:name="android.intent.category.DEFAULT" />
151-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:65:17-76
151-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:65:27-73
152                <category android:name="android.intent.category.BROWSABLE" />
152-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:17-78
152-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:27-75
153
154                <data android:scheme="aljameia" />
154-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
154-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:23-45
155            </intent-filter>
156        </activity>
157
158        <!-- Authentication Activity -->
159        <activity
159-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:80:9-85:58
160            android:name="com.aljameia.app.app.ui.auth.AuthActivity"
160-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:81:13-53
161            android:exported="false"
161-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:82:13-37
162            android:screenOrientation="portrait"
162-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:83:13-49
163            android:theme="@style/Theme.AlJameia.Auth"
163-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:85:13-55
164            android:windowSoftInputMode="adjustResize" />
164-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:84:13-55
165
166        <!-- Simple Main Activity for Testing -->
167        <activity
167-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:88:9-96:20
168            android:name="com.aljameia.app.app.ui.main.SimpleMainActivity"
168-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:89:13-59
169            android:exported="true"
169-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:90:13-36
170            android:screenOrientation="portrait" >
170-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:91:13-49
171            <intent-filter>
171-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:92:13-95:29
172                <action android:name="android.intent.action.MAIN" />
172-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:93:17-69
172-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:93:25-66
173
174                <category android:name="android.intent.category.LAUNCHER" />
174-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:94:17-77
174-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:94:27-74
175            </intent-filter>
176        </activity>
177
178        <!-- Document Viewer Activity -->
179        <activity
179-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:99:9-102:52
180            android:name="com.aljameia.app.app.ui.documents.DocumentViewerActivity"
180-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:100:13-68
181            android:exported="false"
181-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:101:13-37
182            android:screenOrientation="portrait" />
182-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:102:13-49
183
184        <!-- Settings Activity -->
185        <activity
185-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:105:9-109:70
186            android:name="com.aljameia.app.app.ui.settings.SettingsActivity"
186-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:106:13-61
187            android:exported="false"
187-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:107:13-37
188            android:parentActivityName="com.aljameia.app.app.ui.main.MainActivity"
188-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:109:13-67
189            android:screenOrientation="portrait" />
189-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:108:13-49
190
191        <!-- Profile Activity -->
192        <activity
192-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:112:9-116:70
193            android:name="com.aljameia.app.app.ui.profile.ProfileActivity"
193-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:113:13-59
194            android:exported="false"
194-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:114:13-37
195            android:parentActivityName="com.aljameia.app.app.ui.main.MainActivity"
195-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:116:13-67
196            android:screenOrientation="portrait" />
196-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:115:13-49
197
198        <!-- Firebase Cloud Messaging Service -->
199        <service
199-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:119:9-125:19
200            android:name="com.aljameia.app.app.services.FCMService"
200-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:120:13-52
201            android:exported="false" >
201-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:121:13-37
202            <intent-filter>
202-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:122:13-124:29
203                <action android:name="com.google.firebase.MESSAGING_EVENT" />
203-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:123:17-78
203-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:123:25-75
204            </intent-filter>
205        </service>
206
207        <!-- Background Sync Service -->
208        <service
208-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:128:9-131:72
209            android:name="com.aljameia.app.app.services.SyncService"
209-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:129:13-53
210            android:exported="false"
210-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:130:13-37
211            android:permission="android.permission.BIND_JOB_SERVICE" />
211-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:131:13-69
212
213        <!-- File Provider -->
214        <provider
215            android:name="androidx.core.content.FileProvider"
215-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:135:13-62
216            android:authorities="com.aljameia.app.debug.fileprovider"
216-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:136:13-64
217            android:exported="false"
217-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:137:13-37
218            android:grantUriPermissions="true" >
218-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:138:13-47
219            <meta-data
219-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:139:13-141:54
220                android:name="android.support.FILE_PROVIDER_PATHS"
220-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:140:17-67
221                android:resource="@xml/file_paths" />
221-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:141:17-51
222        </provider>
223
224        <!-- Broadcast Receivers -->
225        <receiver
225-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:145:9-155:20
226            android:name="com.aljameia.app.app.receivers.BootReceiver"
226-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:146:13-55
227            android:enabled="true"
227-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:147:13-35
228            android:exported="true" >
228-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:148:13-36
229            <intent-filter android:priority="1000" >
229-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:149:13-154:29
229-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:149:28-51
230                <action android:name="android.intent.action.BOOT_COMPLETED" />
230-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:150:17-79
230-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:150:25-76
231                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
231-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:151:17-84
231-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:151:25-81
232                <action android:name="android.intent.action.PACKAGE_REPLACED" />
232-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:152:17-81
232-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:152:25-78
233
234                <data android:scheme="package" />
234-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
234-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:23-45
235            </intent-filter>
236        </receiver>
237        <receiver
237-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:157:9-164:20
238            android:name="com.aljameia.app.app.receivers.NetworkReceiver"
238-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:158:13-58
239            android:enabled="true"
239-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:159:13-35
240            android:exported="false" >
240-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:160:13-37
241            <intent-filter>
241-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:161:13-163:29
242                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
242-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:162:17-79
242-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:162:25-76
243            </intent-filter>
244        </receiver>
245
246        <!-- Work Manager -->
247        <provider
248            android:name="androidx.startup.InitializationProvider"
248-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:168:13-67
249            android:authorities="com.aljameia.app.debug.androidx-startup"
249-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:169:13-68
250            android:exported="false" >
250-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:170:13-37
251            <meta-data
251-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
252                android:name="androidx.emoji2.text.EmojiCompatInitializer"
252-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
253                android:value="androidx.startup" />
253-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\937659c3c23b49396a8d4652fbe67bb5\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
254            <meta-data
254-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
255                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
255-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
256                android:value="androidx.startup" />
256-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\aea9cf0fe2b15d126017e2dbfc8c53f8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
257            <meta-data
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
258                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
259                android:value="androidx.startup" />
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
260        </provider>
261
262        <!-- Firebase Configuration -->
263        <meta-data
263-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:179:9-181:60
264            android:name="com.google.firebase.messaging.default_notification_icon"
264-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:180:13-83
265            android:resource="@drawable/ic_notification" />
265-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:181:13-57
266        <meta-data
266-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:182:9-184:49
267            android:name="com.google.firebase.messaging.default_notification_color"
267-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:183:13-84
268            android:resource="@color/primary" />
268-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:184:13-46
269        <meta-data
269-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:185:9-187:71
270            android:name="com.google.firebase.messaging.default_notification_channel_id"
270-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:186:13-89
271            android:value="@string/default_notification_channel_id" />
271-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:187:13-68
272
273        <!-- Security Configuration -->
274        <meta-data
274-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:190:9-192:42
275            android:name="android.security.SECURITY_PATCH_LEVEL"
275-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:191:13-65
276            android:value="2023-01-01" />
276-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:192:13-39
277
278        <!-- App Shortcuts -->
279        <meta-data
279-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:195:9-197:49
280            android:name="android.app.shortcuts"
280-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:196:13-49
281            android:resource="@xml/shortcuts" />
281-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:197:13-46
282
283        <!-- Auto Backup -->
284        <meta-data
284-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:200:9-202:54
285            android:name="com.google.android.backup.api_key"
285-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:201:13-61
286            android:value="@string/backup_api_key" />
286-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:202:13-51
287
288        <!-- Analytics -->
289        <meta-data
289-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:205:9-207:54
290            android:name="com.google.android.gms.analytics.globalConfigResource"
290-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:206:13-81
291            android:resource="@xml/global_tracker" />
291-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:207:13-51
292
293        <!-- Crash Reporting -->
294        <meta-data
294-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:210:9-212:36
295            android:name="firebase_crashlytics_collection_enabled"
295-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:211:13-67
296            android:value="true" />
296-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:212:13-33
297
298        <!-- Performance Monitoring -->
299        <meta-data
299-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:215:9-217:36
300            android:name="firebase_performance_collection_enabled"
300-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:216:13-67
301            android:value="true" />
301-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:217:13-33
302
303        <!-- Remote Config -->
304        <meta-data
304-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:220:9-222:37
305            android:name="firebase_remote_config_developer_mode_enabled"
305-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:221:13-73
306            android:value="false" />
306-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:222:13-34
307
308        <activity
308-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
309            android:name="com.karumi.dexter.DexterActivity"
309-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
310            android:theme="@style/Dexter.Internal.Theme.Transparent" />
310-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4a2578828f412c99aa2b86a09de4a284\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
311
312        <service
312-->[com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:9:9-15:19
313            android:name="com.google.firebase.components.ComponentDiscoveryService"
313-->[com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:10:13-84
314            android:directBootAware="true"
314-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
315            android:exported="false" >
315-->[com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:11:13-37
316            <meta-data
316-->[com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:12:13-14:85
317                android:name="com.google.firebase.components:com.google.firebase.perf.ktx.FirebasePerfLegacyRegistrar"
317-->[com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:13:17-119
318                android:value="com.google.firebase.components.ComponentRegistrar" />
318-->[com.google.firebase:firebase-perf-ktx:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\23ccc906e6db86f8fd112c65e19fa29b\transformed\firebase-perf-ktx-20.5.1\AndroidManifest.xml:14:17-82
319            <meta-data
319-->[com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:14:13-16:85
320                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar"
320-->[com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:15:17-112
321                android:value="com.google.firebase.components.ComponentRegistrar" />
321-->[com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:16:17-82
322            <meta-data
322-->[com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:17:13-19:85
323                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
323-->[com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:18:17-109
324                android:value="com.google.firebase.components.ComponentRegistrar" />
324-->[com.google.firebase:firebase-perf:20.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\675917a14b0d8bb60b4fd2de3b2d89c1\transformed\firebase-perf-20.5.1\AndroidManifest.xml:19:17-82
325            <meta-data
325-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
326                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
326-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
327                android:value="com.google.firebase.components.ComponentRegistrar" />
327-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebd354fb05c86c72a0ceec23a6522deb\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
328            <meta-data
328-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
329                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
329-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
330                android:value="com.google.firebase.components.ComponentRegistrar" />
330-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
331            <meta-data
331-->[com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:12:13-14:85
332                android:name="com.google.firebase.components:com.google.firebase.storage.ktx.FirebaseStorageLegacyRegistrar"
332-->[com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:13:17-125
333                android:value="com.google.firebase.components.ComponentRegistrar" />
333-->[com.google.firebase:firebase-storage-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12baf95f309ee36fc39215555071fe09\transformed\firebase-storage-ktx-20.3.0\AndroidManifest.xml:14:17-82
334            <meta-data
334-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
335                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
335-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
336                android:value="com.google.firebase.components.ComponentRegistrar" />
336-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
337            <meta-data
337-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
338                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
338-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
339                android:value="com.google.firebase.components.ComponentRegistrar" />
339-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e844becc3e1062992b7c01348053ac0\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
340            <meta-data
340-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:12:13-14:85
341                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
341-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:13:17-129
342                android:value="com.google.firebase.components.ComponentRegistrar" />
342-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\194de724705d308dd9048fd1a840eaaa\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:14:17-82
343            <meta-data
343-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
344                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
344-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
345                android:value="com.google.firebase.components.ComponentRegistrar" />
345-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
346            <meta-data
346-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
347                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
347-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
348                android:value="com.google.firebase.components.ComponentRegistrar" />
348-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebb9f04b3b085304c54ccab193d12536\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
349            <meta-data
349-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
350                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
350-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
351                android:value="com.google.firebase.components.ComponentRegistrar" />
351-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5131e63d371da9eedd320eeebc3f1f5\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
352            <meta-data
352-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
353                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
353-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
354                android:value="com.google.firebase.components.ComponentRegistrar" />
354-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
355            <meta-data
355-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
356                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
356-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
357                android:value="com.google.firebase.components.ComponentRegistrar" />
357-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
358            <meta-data
358-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:25:13-27:85
359                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
359-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:26:17-120
360                android:value="com.google.firebase.components.ComponentRegistrar" />
360-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:27:17-82
361            <meta-data
361-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:28:13-30:85
362                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
362-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:29:17-117
363                android:value="com.google.firebase.components.ComponentRegistrar" />
363-->[com.google.firebase:firebase-appcheck:17.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02587df7c804ea037df4a6e9c370740a\transformed\firebase-appcheck-17.1.1\AndroidManifest.xml:30:17-82
364            <meta-data
364-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
365                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
365-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
366                android:value="com.google.firebase.components.ComponentRegistrar" />
366-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eec921049bc3026a801fbf572c7eb9d9\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
367            <meta-data
367-->[com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:24:13-26:85
368                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
368-->[com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:25:17-130
369                android:value="com.google.firebase.components.ComponentRegistrar" />
369-->[com.google.firebase:firebase-crashlytics-ktx:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83e821be88bf5f5b0f8560342e26b3fb\transformed\firebase-crashlytics-ktx-18.6.0\AndroidManifest.xml:26:17-82
370            <meta-data
370-->[com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:15:13-17:85
371                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
371-->[com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:16:17-126
372                android:value="com.google.firebase.components.ComponentRegistrar" />
372-->[com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:17:17-82
373            <meta-data
373-->[com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:18:13-20:85
374                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
374-->[com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:19:17-115
375                android:value="com.google.firebase.components.ComponentRegistrar" />
375-->[com.google.firebase:firebase-crashlytics:18.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a8289d1c3136ca48e6e3be4505d37832\transformed\firebase-crashlytics-18.6.0\AndroidManifest.xml:20:17-82
376            <meta-data
376-->[com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:29:13-31:85
377                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
377-->[com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:30:17-117
378                android:value="com.google.firebase.components.ComponentRegistrar" />
378-->[com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:31:17-82
379            <meta-data
379-->[com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:12:13-14:85
380                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.ktx.FirebaseConfigLegacyRegistrar"
380-->[com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:13:17-129
381                android:value="com.google.firebase.components.ComponentRegistrar" />
381-->[com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f7cf0f1c8da7d526116140d7c618d54b\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:14:17-82
382            <meta-data
382-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:29:13-31:85
383                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
383-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:30:17-128
384                android:value="com.google.firebase.components.ComponentRegistrar" />
384-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:31:17-82
385            <meta-data
385-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:32:13-34:85
386                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
386-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:33:17-117
387                android:value="com.google.firebase.components.ComponentRegistrar" />
387-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d543e3ae08f79b67b7677759f08065c\transformed\firebase-config-21.6.0\AndroidManifest.xml:34:17-82
388            <meta-data
388-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
389                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
389-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
390                android:value="com.google.firebase.components.ComponentRegistrar" />
390-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
391            <meta-data
391-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
392                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
392-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
393                android:value="com.google.firebase.components.ComponentRegistrar" />
393-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
394            <meta-data
394-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
395                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
395-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
396                android:value="com.google.firebase.components.ComponentRegistrar" />
396-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8892c00dbb518198277634310938cb1e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
397            <meta-data
397-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
398                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
398-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
399                android:value="com.google.firebase.components.ComponentRegistrar" />
399-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\2db348d2e429cbae1fcb69df27a4e30a\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
400            <meta-data
400-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
401                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
401-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
402                android:value="com.google.firebase.components.ComponentRegistrar" />
402-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
403            <meta-data
403-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
404                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
404-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
405                android:value="com.google.firebase.components.ComponentRegistrar" />
405-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a51b0a0bad13b98a7e0afda2c2c41bd\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
406            <meta-data
406-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
407                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
407-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
408                android:value="com.google.firebase.components.ComponentRegistrar" />
408-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c890daa56b18021abfbb06da69d6c4e\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
409        </service>
410
411        <activity
411-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
412            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
412-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
413            android:excludeFromRecents="true"
413-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
414            android:exported="false"
414-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
415            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
415-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
416        <!--
417            Service handling Google Sign-In user revocation. For apps that do not integrate with
418            Google Sign-In, this service will never be started.
419        -->
420        <service
420-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
421            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
421-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
422            android:exported="true"
422-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
423            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
423-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
424            android:visibleToInstantApps="true" /> <!-- Needs to be explicitly declared on P+ -->
424-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13a7d5cf8b0d31ea816513486a1b2cf1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
425        <uses-library
425-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
426            android:name="org.apache.http.legacy"
426-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
427            android:required="false" />
427-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e72237b740dd5962a0844e739dcc6ac4\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
428
429        <activity
429-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
430            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
430-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
431            android:excludeFromRecents="true"
431-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
432            android:exported="true"
432-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
433            android:launchMode="singleTask"
433-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
434            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
434-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
435            <intent-filter>
435-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
436                <action android:name="android.intent.action.VIEW" />
436-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:17-69
436-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:25-66
437
438                <category android:name="android.intent.category.DEFAULT" />
438-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:65:17-76
438-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:65:27-73
439                <category android:name="android.intent.category.BROWSABLE" />
439-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:17-78
439-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:27-75
440
441                <data
441-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
442                    android:host="firebase.auth"
442-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:68:21-48
443                    android:path="/"
444                    android:scheme="genericidp" />
444-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:23-45
445            </intent-filter>
446        </activity>
447        <activity
447-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
448            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
448-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
449            android:excludeFromRecents="true"
449-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
450            android:exported="true"
450-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
451            android:launchMode="singleTask"
451-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
452            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
452-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
453            <intent-filter>
453-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf767f68b62fdd72ea4a3108b85d7a50\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
454                <action android:name="android.intent.action.VIEW" />
454-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:17-69
454-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:25-66
455
456                <category android:name="android.intent.category.DEFAULT" />
456-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:65:17-76
456-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:65:27-73
457                <category android:name="android.intent.category.BROWSABLE" />
457-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:17-78
457-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:27-75
458
459                <data
459-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
460                    android:host="firebase.auth"
460-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:68:21-48
461                    android:path="/"
462                    android:scheme="recaptcha" />
462-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:23-45
463            </intent-filter>
464        </activity>
465
466        <receiver
466-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
467            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
467-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
468            android:exported="true"
468-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
469            android:permission="com.google.android.c2dm.permission.SEND" >
469-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
470            <intent-filter>
470-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
471                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
471-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
471-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
472            </intent-filter>
473
474            <meta-data
474-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
475                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
475-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
476                android:value="true" />
476-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
477        </receiver>
478        <!--
479             FirebaseMessagingService performs security checks at runtime,
480             but set to not exported to explicitly avoid allowing another app to call it.
481        -->
482        <service
482-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
483            android:name="com.google.firebase.messaging.FirebaseMessagingService"
483-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
484            android:directBootAware="true"
484-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
485            android:exported="false" >
485-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d71315c5d3280e412dfef205927d9411\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
486            <intent-filter android:priority="-500" >
486-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:122:13-124:29
487                <action android:name="com.google.firebase.MESSAGING_EVENT" />
487-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:123:17-78
487-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:123:25-75
488            </intent-filter>
489        </service>
490
491        <activity
491-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
492            android:name="com.google.android.gms.common.api.GoogleApiActivity"
492-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
493            android:exported="false"
493-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
494            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
494-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1c0b3a48f4d4c74d31971bddddc3d282\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
495        <activity
495-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
496            android:name="androidx.compose.ui.tooling.PreviewActivity"
496-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
497            android:exported="true" />
497-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\89d524c9a9e4c67b70b2ffdc78619f3f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
498        <activity
498-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
499            android:name="androidx.activity.ComponentActivity"
499-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
500            android:exported="true" />
500-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287d5f065cf318a9d19e934f7c2e815\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
501
502        <service
502-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
503            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
503-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
504            android:directBootAware="false"
504-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
505            android:enabled="@bool/enable_system_alarm_service_default"
505-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
506            android:exported="false" />
506-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
507        <service
507-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
508            android:name="androidx.work.impl.background.systemjob.SystemJobService"
508-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
509            android:directBootAware="false"
509-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
510            android:enabled="@bool/enable_system_job_service_default"
510-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
511            android:exported="true"
511-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
512            android:permission="android.permission.BIND_JOB_SERVICE" />
512-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
513        <service
513-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
514            android:name="androidx.work.impl.foreground.SystemForegroundService"
514-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
515            android:directBootAware="false"
515-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
516            android:enabled="@bool/enable_system_foreground_service_default"
516-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
517            android:exported="false" />
517-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
518
519        <receiver
519-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
520            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
520-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
521            android:directBootAware="false"
521-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
522            android:enabled="true"
522-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
523            android:exported="false" />
523-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
524        <receiver
524-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
525            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
525-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
526            android:directBootAware="false"
526-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
527            android:enabled="false"
527-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
528            android:exported="false" >
528-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
529            <intent-filter>
529-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
530                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
530-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
530-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
531                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
531-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
531-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
532            </intent-filter>
533        </receiver>
534        <receiver
534-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
535            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
535-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
536            android:directBootAware="false"
536-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
537            android:enabled="false"
537-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
538            android:exported="false" >
538-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
539            <intent-filter>
539-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
540                <action android:name="android.intent.action.BATTERY_OKAY" />
540-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
540-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
541                <action android:name="android.intent.action.BATTERY_LOW" />
541-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
541-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
542            </intent-filter>
543        </receiver>
544        <receiver
544-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
545            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
545-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
546            android:directBootAware="false"
546-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
547            android:enabled="false"
547-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
548            android:exported="false" >
548-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
549            <intent-filter>
549-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
550                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
550-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
550-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
551                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
551-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
551-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
552            </intent-filter>
553        </receiver>
554        <receiver
554-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
555            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
555-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
556            android:directBootAware="false"
556-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
557            android:enabled="false"
557-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
558            android:exported="false" >
558-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
559            <intent-filter>
559-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:161:13-163:29
560                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
560-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:162:17-79
560-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:162:25-76
561            </intent-filter>
562        </receiver>
563        <receiver
563-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
564            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
564-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
565            android:directBootAware="false"
565-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
566            android:enabled="false"
566-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
567            android:exported="false" >
567-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
568            <intent-filter>
568-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
569                <action android:name="android.intent.action.BOOT_COMPLETED" />
569-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:150:17-79
569-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:150:25-76
570                <action android:name="android.intent.action.TIME_SET" />
570-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
570-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
571                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
571-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
571-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
572            </intent-filter>
573        </receiver>
574        <receiver
574-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
575            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
575-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
576            android:directBootAware="false"
576-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
577            android:enabled="@bool/enable_system_alarm_service_default"
577-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
578            android:exported="false" >
578-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
579            <intent-filter>
579-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
580                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
580-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
580-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
581            </intent-filter>
582        </receiver>
583        <receiver
583-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
584            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
584-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
585            android:directBootAware="false"
585-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
586            android:enabled="true"
586-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
587            android:exported="true"
587-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
588            android:permission="android.permission.DUMP" >
588-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
589            <intent-filter>
589-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
590                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
590-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
590-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebfe528aec1ca5642c4060f30d584ad1\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
591            </intent-filter>
592        </receiver>
593        <receiver
593-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
594            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
594-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
595            android:enabled="true"
595-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
596            android:exported="false" >
596-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
597        </receiver>
598
599        <service
599-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
600            android:name="com.google.android.gms.measurement.AppMeasurementService"
600-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
601            android:enabled="true"
601-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
602            android:exported="false" />
602-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
603        <service
603-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
604            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
604-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
605            android:enabled="true"
605-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
606            android:exported="false"
606-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
607            android:permission="android.permission.BIND_JOB_SERVICE" />
607-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0429a6521e0736ebf216906d232f96f2\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
608
609        <uses-library
609-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
610            android:name="androidx.window.extensions"
610-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
611            android:required="false" />
611-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
612        <uses-library
612-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
613            android:name="androidx.window.sidecar"
613-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
614            android:required="false" />
614-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d028e913678b9473978bcce37634707e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
615
616        <service
616-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
617            android:name="androidx.room.MultiInstanceInvalidationService"
617-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
618            android:directBootAware="true"
618-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
619            android:exported="false" />
619-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\86b50ec42809993e031694f0f02f3c2e\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
620        <service
620-->[com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:22:9-25:40
621            android:name="com.google.firebase.sessions.SessionLifecycleService"
621-->[com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:23:13-80
622            android:enabled="true"
622-->[com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:24:13-35
623            android:exported="false" />
623-->[com.google.firebase:firebase-sessions:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\822b3d47a0c5d7c069a16d734ee45543\transformed\firebase-sessions-1.2.0\AndroidManifest.xml:25:13-37
624
625        <uses-library
625-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
626            android:name="android.ext.adservices"
626-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
627            android:required="false" />
627-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.9\transforms\e66221cdce221ec0d023edfe7b113482\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
628
629        <property
629-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
630            android:name="android.adservices.AD_SERVICES_CONFIG"
630-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
631            android:resource="@xml/ga_ad_services_config" />
631-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e820701e769c44af0fc39e5e9dc28ee\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
632
633        <provider
633-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
634            android:name="com.google.firebase.provider.FirebaseInitProvider"
634-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
635            android:authorities="com.aljameia.app.debug.firebaseinitprovider"
635-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
636            android:directBootAware="true"
636-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
637            android:exported="false"
637-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
638            android:initOrder="100" />
638-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\824cbb75b8171e7804d83a43c8352e16\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
639
640        <meta-data
640-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
641            android:name="com.google.android.gms.version"
641-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
642            android:value="@integer/google_play_services_version" />
642-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1a3dac0af0a85f165dc53a6762b4a81\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
643
644        <provider
644-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:32:9-40:20
645            android:name="leakcanary.internal.LeakCanaryFileProvider"
645-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:33:13-70
646            android:authorities="com.squareup.leakcanary.fileprovider.com.aljameia.app.debug"
646-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:34:13-88
647            android:exported="false"
647-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:35:13-37
648            android:grantUriPermissions="true" >
648-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:36:13-47
649            <meta-data
649-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:139:13-141:54
650                android:name="android.support.FILE_PROVIDER_PATHS"
650-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:140:17-67
651                android:resource="@xml/leak_canary_file_paths" />
651-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:141:17-51
652        </provider>
653
654        <activity
654-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:42:9-73:20
655            android:name="leakcanary.internal.activity.LeakActivity"
655-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:43:13-69
656            android:exported="true"
656-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:44:13-36
657            android:icon="@mipmap/leak_canary_icon"
657-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:45:13-52
658            android:label="@string/leak_canary_display_activity_label"
658-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:46:13-71
659            android:taskAffinity="com.squareup.leakcanary.com.aljameia.app.debug"
659-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:47:13-76
660            android:theme="@style/leak_canary_LeakCanary.Base" >
660-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:48:13-63
661            <intent-filter android:label="@string/leak_canary_import_hprof_file" >
661-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:49:13-72:29
661-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:49:28-81
662                <action android:name="android.intent.action.VIEW" />
662-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:17-69
662-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:64:25-66
663
664                <category android:name="android.intent.category.DEFAULT" />
664-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:65:17-76
664-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:65:27-73
665                <category android:name="android.intent.category.BROWSABLE" />
665-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:17-78
665-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:66:27-75
666
667                <data android:scheme="file" />
667-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
667-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:23-45
668                <data android:scheme="content" />
668-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
668-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:23-45
669                <data android:mimeType="*/*" />
669-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
669-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:240:19-41
670                <data android:host="*" />
670-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
670-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:68:21-48
671                <data android:pathPattern=".*\\.hprof" />
671-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
672                <data android:pathPattern=".*\\..*\\.hprof" />
672-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
673                <data android:pathPattern=".*\\..*\\..*\\.hprof" />
673-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
674                <data android:pathPattern=".*\\..*\\..*\\..*\\.hprof" />
674-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
675                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.hprof" />
675-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
676                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
676-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
677                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
677-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:67:17-68:51
678                <!--
679            Since hprof isn't a standard MIME type, we have to declare such patterns.
680            Most file providers will generate URIs including their own package name,
681            which contains `.` characters that must be explicitly escaped in pathPattern.
682            @see https://stackoverflow.com/a/31028507/703646
683                -->
684            </intent-filter>
685        </activity>
686
687        <activity-alias
687-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:75:9-92:26
688            android:name="leakcanary.internal.activity.LeakLauncherActivity"
688-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:76:13-77
689            android:banner="@drawable/leak_canary_tv_icon"
689-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:77:13-59
690            android:enabled="@bool/leak_canary_add_launcher_icon"
690-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:78:13-66
691            android:exported="true"
691-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:79:13-36
692            android:icon="@mipmap/leak_canary_icon"
692-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:80:13-52
693            android:label="@string/leak_canary_display_activity_label"
693-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:81:13-71
694            android:targetActivity="leakcanary.internal.activity.LeakActivity"
694-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:82:13-79
695            android:taskAffinity="com.squareup.leakcanary.com.aljameia.app.debug"
695-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:83:13-76
696            android:theme="@style/leak_canary_LeakCanary.Base" >
696-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:84:13-63
697            <intent-filter>
697-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:85:13-91:29
698                <action android:name="android.intent.action.MAIN" />
698-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:93:17-69
698-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:93:25-66
699
700                <category android:name="android.intent.category.LAUNCHER" />
700-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:94:17-77
700-->C:\Users\<USER>\Documents\augment-projects\aljameia\mobile\android\app\src\main\AndroidManifest.xml:94:27-74
701                <!-- Android TV launcher intent -->
702                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
702-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:90:17-86
702-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:90:27-83
703            </intent-filter>
704        </activity-alias>
705
706        <activity
706-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:94:9-100:68
707            android:name="leakcanary.internal.RequestPermissionActivity"
707-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:95:13-73
708            android:excludeFromRecents="true"
708-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:96:13-46
709            android:icon="@mipmap/leak_canary_icon"
709-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:97:13-52
710            android:label="@string/leak_canary_storage_permission_activity_label"
710-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:98:13-82
711            android:taskAffinity="com.squareup.leakcanary.com.aljameia.app.debug"
711-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:99:13-76
712            android:theme="@style/leak_canary_Theme.Transparent" />
712-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:100:13-65
713
714        <receiver android:name="leakcanary.internal.NotificationReceiver" />
714-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:102:9-77
714-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\36d878c40c24f2efc940ac02b7dc7b88\transformed\leakcanary-android-core-2.12\AndroidManifest.xml:102:19-74
715
716        <provider
716-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:8:9-12:40
717            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
717-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:9:13-78
718            android:authorities="com.aljameia.app.debug.leakcanary-installer"
718-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:10:13-72
719            android:enabled="@bool/leak_canary_watcher_auto_install"
719-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:11:13-69
720            android:exported="false" />
720-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\defe60d4892ccabd860c93608094945a\transformed\leakcanary-object-watcher-android-2.12\AndroidManifest.xml:12:13-37
721        <provider
721-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:8:9-12:40
722            android:name="leakcanary.internal.PlumberInstaller"
722-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:9:13-64
723            android:authorities="com.aljameia.app.debug.plumber-installer"
723-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:10:13-69
724            android:enabled="@bool/leak_canary_plumber_auto_install"
724-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:11:13-69
725            android:exported="false" />
725-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.9\transforms\2f4342d02947af46b8507c7ec38149aa\transformed\plumber-android-2.12\AndroidManifest.xml:12:13-37
726
727        <service
727-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
728            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
728-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
729            android:exported="false" >
729-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
730            <meta-data
730-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
731                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
731-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
732                android:value="cct" />
732-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\b5449d22104f31e8d95d95199d5ec559\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
733        </service>
734
735        <receiver
735-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
736            android:name="androidx.profileinstaller.ProfileInstallReceiver"
736-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
737            android:directBootAware="false"
737-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
738            android:enabled="true"
738-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
739            android:exported="true"
739-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
740            android:permission="android.permission.DUMP" >
740-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
741            <intent-filter>
741-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
742                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
742-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
742-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
743            </intent-filter>
744            <intent-filter>
744-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
745                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
745-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
745-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
746            </intent-filter>
747            <intent-filter>
747-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
748                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
748-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
748-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
749            </intent-filter>
750            <intent-filter>
750-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
751                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
751-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
751-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\22ebdfbb01e461a7d51bb1440499bf2d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
752            </intent-filter>
753        </receiver>
754
755        <service
755-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
756            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
756-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
757            android:exported="false"
757-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
758            android:permission="android.permission.BIND_JOB_SERVICE" >
758-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
759        </service>
760
761        <receiver
761-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
762            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
762-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
763            android:exported="false" />
763-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\8e9e79e8ecba49d9f156e1d51884cb22\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
764
765        <activity
765-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
766            android:name="com.journeyapps.barcodescanner.CaptureActivity"
766-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
767            android:clearTaskOnLaunch="true"
767-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
768            android:screenOrientation="sensorLandscape"
768-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
769            android:stateNotNeeded="true"
769-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
770            android:theme="@style/zxing_CaptureTheme"
770-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
771            android:windowSoftInputMode="stateAlwaysHidden" />
771-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\403ef3e018096a306df17d01c38df724\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
772    </application>
773
774</manifest>
