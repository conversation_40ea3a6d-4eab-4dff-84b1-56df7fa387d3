import { useTranslation } from 'next-i18next';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

const DashboardStats = () => {
  const { t } = useTranslation('dashboard');

  // Sample data for charts
  const revenueData = [
    { month: 'Jan', revenue: 45000, expenses: 32000, members: 2100 },
    { month: 'Feb', revenue: 52000, expenses: 35000, members: 2200 },
    { month: 'Mar', revenue: 48000, expenses: 33000, members: 2350 },
    { month: 'Apr', revenue: 61000, expenses: 38000, members: 2400 },
    { month: 'May', revenue: 55000, expenses: 36000, members: 2450 },
    { month: 'Jun', revenue: 67000, expenses: 42000, members: 2547 },
  ];

  const membershipData = [
    { month: 'Jan', new: 45, renewed: 120, expired: 23 },
    { month: 'Feb', new: 52, renewed: 135, expired: 18 },
    { month: 'Mar', new: 48, renewed: 142, expired: 25 },
    { month: 'Apr', new: 61, renewed: 128, expired: 20 },
    { month: 'May', new: 55, renewed: 156, expired: 22 },
    { month: 'Jun', new: 67, renewed: 148, expired: 19 },
  ];

  const paymentMethodData = [
    { method: 'Bank Transfer', amount: 45000, percentage: 45 },
    { method: 'Credit Card', amount: 32000, percentage: 32 },
    { method: 'Cash', amount: 15000, percentage: 15 },
    { method: 'Digital Wallet', amount: 8000, percentage: 8 },
  ];

  return (
    <div className="space-y-6">
      {/* Revenue vs Expenses Chart */}
      <Card>
        <CardHeader>
          <CardTitle>{t('charts.revenue_expenses.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    `₹ ${value.toLocaleString()}`,
                    name === 'revenue' ? t('charts.revenue_expenses.revenue') : t('charts.revenue_expenses.expenses')
                  ]}
                />
                <Area 
                  type="monotone" 
                  dataKey="revenue" 
                  stackId="1"
                  stroke="#3B82F6" 
                  fill="#3B82F6" 
                  fillOpacity={0.6}
                />
                <Area 
                  type="monotone" 
                  dataKey="expenses" 
                  stackId="2"
                  stroke="#EF4444" 
                  fill="#EF4444" 
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Member Growth Chart */}
        <Card>
          <CardHeader>
            <CardTitle>{t('charts.member_growth.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value) => [value, t('charts.member_growth.members')]}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="members" 
                    stroke="#10B981" 
                    strokeWidth={3}
                    dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Membership Activity */}
        <Card>
          <CardHeader>
            <CardTitle>{t('charts.membership_activity.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={membershipData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="new" fill="#3B82F6" name={t('charts.membership_activity.new')} />
                  <Bar dataKey="renewed" fill="#10B981" name={t('charts.membership_activity.renewed')} />
                  <Bar dataKey="expired" fill="#EF4444" name={t('charts.membership_activity.expired')} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Methods Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>{t('charts.payment_methods.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {paymentMethodData.map((method, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ 
                      backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6'][index] 
                    }}
                  ></div>
                  <span className="font-medium text-gray-900">{method.method}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-gray-600">₹ {method.amount.toLocaleString()}</span>
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div 
                      className="h-2 rounded-full"
                      style={{ 
                        width: `${method.percentage}%`,
                        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6'][index]
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-8">
                    {method.percentage}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardStats;
