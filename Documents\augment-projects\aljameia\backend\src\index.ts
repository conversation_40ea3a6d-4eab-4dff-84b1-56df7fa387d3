import 'reflect-metadata';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import session from 'express-session';
import RedisStore from 'connect-redis';
import { createClient } from 'redis';
import swaggerUi from 'swagger-ui-express';
import swaggerJsdoc from 'swagger-jsdoc';

import { config } from '@/config/environment';
import { connectDatabase } from '@/config/database';
import { redisClient } from '@/config/redis';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { authMiddleware } from '@/middleware/auth';
import { auditMiddleware } from '@/middleware/audit';

// Import routes
import authRoutes from '@/routes/auth';
import memberRoutes from '@/routes/members';
import contributionRoutes from '@/routes/contributions';
import loanRoutes from '@/routes/loans';
import documentRoutes from '@/routes/documents';
import transactionRoutes from '@/routes/transactions';
import reportRoutes from '@/routes/reports';
import notificationRoutes from '@/routes/notifications';
import supportRoutes from '@/routes/support';
import adminRoutes from '@/routes/admin';
import healthRoutes from '@/routes/health';

class Application {
  public app: express.Application;

  constructor() {
    this.app = express();
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
    this.initializeSwagger();
  }

  private initializeMiddlewares(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      crossOriginEmbedderPolicy: false,
    }));

    // CORS configuration
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Compression
    this.app.use(compression());

    // Request logging
    this.app.use(morgan('combined', {
      stream: { write: (message) => logger.info(message.trim()) }
    }));

    // Body parsing
    this.app.use(express.json({ limit: config.api.bodyLimit }));
    this.app.use(express.urlencoded({ extended: true, limit: config.api.bodyLimit }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests, please try again later',
        },
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api', limiter);

    // Session configuration
    const redisStore = new RedisStore({
      client: redisClient,
      prefix: 'sess:',
    });

    this.app.use(session({
      store: redisStore,
      secret: config.session.secret,
      resave: false,
      saveUninitialized: false,
      rolling: true,
      cookie: {
        secure: config.env === 'production',
        httpOnly: true,
        maxAge: config.session.maxAge,
        sameSite: 'strict',
      },
    }));

    // Audit logging middleware
    this.app.use(auditMiddleware);

    // Trust proxy for accurate IP addresses
    this.app.set('trust proxy', 1);
  }

  private initializeRoutes(): void {
    // Health check (no auth required)
    this.app.use('/health', healthRoutes);

    // API routes with versioning
    const apiRouter = express.Router();
    
    // Public routes (no auth required)
    apiRouter.use('/auth', authRoutes);
    
    // Protected routes (auth required)
    apiRouter.use('/members', authMiddleware, memberRoutes);
    apiRouter.use('/contributions', authMiddleware, contributionRoutes);
    apiRouter.use('/loans', authMiddleware, loanRoutes);
    apiRouter.use('/documents', authMiddleware, documentRoutes);
    apiRouter.use('/transactions', authMiddleware, transactionRoutes);
    apiRouter.use('/reports', authMiddleware, reportRoutes);
    apiRouter.use('/notifications', authMiddleware, notificationRoutes);
    apiRouter.use('/support', authMiddleware, supportRoutes);
    
    // Admin routes (admin auth required)
    apiRouter.use('/admin', authMiddleware, adminRoutes);

    this.app.use(`/api/${config.api.version}`, apiRouter);

    // 404 handler for undefined routes
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'ROUTE_NOT_FOUND',
          message: 'The requested route was not found',
        },
      });
    });
  }

  private initializeErrorHandling(): void {
    this.app.use(errorHandler);
  }

  private initializeSwagger(): void {
    const options = {
      definition: {
        openapi: '3.0.0',
        info: {
          title: 'Al-Jameia Financial Association API',
          version: '1.0.0',
          description: 'Comprehensive financial association management system API',
          contact: {
            name: 'Al-Jameia Development Team',
            email: '<EMAIL>',
          },
        },
        servers: [
          {
            url: `http://localhost:${config.port}/api/${config.api.version}`,
            description: 'Development server',
          },
          {
            url: `https://api.aljameia.com/api/${config.api.version}`,
            description: 'Production server',
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
        security: [
          {
            bearerAuth: [],
          },
        ],
      },
      apis: ['./src/routes/*.ts', './src/controllers/*.ts'],
    };

    const specs = swaggerJsdoc(options);
    this.app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: 'Al-Jameia API Documentation',
    }));
  }

  public async start(): Promise<void> {
    try {
      // Initialize database connection
      await connectDatabase();
      logger.info('Database connection established');

      // Initialize Redis connection
      await redisClient.connect();
      logger.info('Redis connection established');

      // Start the server
      const server = this.app.listen(config.port, () => {
        logger.info(`Server running on port ${config.port}`);
        logger.info(`Environment: ${config.env}`);
        logger.info(`API Documentation: http://localhost:${config.port}/api-docs`);
      });

      // Graceful shutdown
      process.on('SIGTERM', () => {
        logger.info('SIGTERM received, shutting down gracefully');
        server.close(() => {
          redisClient.disconnect();
          process.exit(0);
        });
      });

      process.on('SIGINT', () => {
        logger.info('SIGINT received, shutting down gracefully');
        server.close(() => {
          redisClient.disconnect();
          process.exit(0);
        });
      });

    } catch (error) {
      logger.error('Failed to start application:', error);
      process.exit(1);
    }
  }
}

// Start the application
const app = new Application();
app.start().catch((error) => {
  logger.error('Application startup failed:', error);
  process.exit(1);
});

export default app;
