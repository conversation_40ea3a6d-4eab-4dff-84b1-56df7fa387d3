{"name": "@aljameia/backend", "version": "1.0.0", "description": "Backend API for Al-Jameia Financial Association Management System", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "migrate": "ts-node src/database/migrate.ts", "seed": "ts-node src/database/seed.ts", "db:reset": "npm run migrate && npm run seed", "docker:build": "docker build -t aljameia-backend .", "docker:run": "docker run -p 3000:3000 aljameia-backend"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mongoose": "^8.0.3", "redis": "^4.6.10", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "aws-sdk": "^2.1489.0", "stripe": "^14.7.0", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "express-winston": "^4.2.0", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.13", "uuid": "^9.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "pdf-lib": "^1.17.1", "puppeteer": "^21.5.2", "cron": "^3.1.6", "socket.io": "^4.7.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/node": "^20.9.0", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/speakeasy": "^2.0.10", "@types/qrcode": "^1.5.5", "@types/joi": "^17.2.3", "@types/express-session": "^1.17.10", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/swagger-ui-express": "^4.1.6", "@types/swagger-jsdoc": "^6.0.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.2.2", "ts-node": "^10.9.1", "tsc-alias": "^1.8.8", "nodemon": "^3.0.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.1.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "transform": {"^.+\\.ts$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts", "!src/database/migrations/**", "!src/database/seeds/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}