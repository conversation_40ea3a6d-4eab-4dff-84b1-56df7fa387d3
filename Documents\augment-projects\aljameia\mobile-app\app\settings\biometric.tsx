import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { biometricService, BiometricSettings, BiometricCapabilities } from '../../services/biometricService';

export default function BiometricSettingsScreen() {
  const [settings, setSettings] = useState<BiometricSettings>({
    enabled: false,
    type: 'none',
    fallbackToPassword: true,
    autoLockTimeout: 5,
  });
  const [capabilities, setCapabilities] = useState<BiometricCapabilities>({
    isAvailable: false,
    supportedTypes: [],
    isEnrolled: false,
    securityLevel: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Initialize biometric service
      await biometricService.initialize();
      
      // Load current settings
      const currentSettings = biometricService.getSettings();
      setSettings(currentSettings);
      
      // Check capabilities
      const caps = await biometricService.getCapabilities();
      setCapabilities(caps);
    } catch (error) {
      console.error('Failed to load biometric data:', error);
      Alert.alert('خطأ', 'فشل في تحميل إعدادات المصادقة البيومترية');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnableBiometric = async (enabled: boolean) => {
    try {
      if (enabled) {
        if (!capabilities.isAvailable) {
          Alert.alert('غير متوفر', 'المصادقة البيومترية غير متوفرة على هذا الجهاز');
          return;
        }

        if (!capabilities.isEnrolled) {
          Alert.alert(
            'غير مسجل',
            'يرجى تسجيل بيانات بيومترية في إعدادات الجهاز أولاً',
            [
              { text: 'إلغاء', style: 'cancel' },
              { text: 'فتح الإعدادات', onPress: () => {
                // You can implement deep linking to device settings here
              }}
            ]
          );
          return;
        }

        const success = await biometricService.enableBiometric();
        if (success) {
          const newSettings = biometricService.getSettings();
          setSettings(newSettings);
          Alert.alert('تم التفعيل', 'تم تفعيل المصادقة البيومترية بنجاح');
        } else {
          Alert.alert('فشل', 'فشل في تفعيل المصادقة البيومترية');
        }
      } else {
        Alert.alert(
          'تأكيد',
          'هل تريد إيقاف المصادقة البيومترية؟',
          [
            { text: 'إلغاء', style: 'cancel' },
            { 
              text: 'إيقاف', 
              style: 'destructive',
              onPress: async () => {
                await biometricService.disableBiometric();
                const newSettings = biometricService.getSettings();
                setSettings(newSettings);
                Alert.alert('تم الإيقاف', 'تم إيقاف المصادقة البيومترية');
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Failed to toggle biometric:', error);
      Alert.alert('خطأ', 'حدث خطأ أثناء تغيير إعدادات المصادقة البيومترية');
    }
  };

  const handleSettingChange = async (key: keyof BiometricSettings, value: any) => {
    try {
      await biometricService.updateSettings({ [key]: value });
      const newSettings = biometricService.getSettings();
      setSettings(newSettings);
    } catch (error) {
      console.error('Failed to update setting:', error);
      Alert.alert('خطأ', 'فشل في تحديث الإعدادات');
    }
  };

  const testBiometric = async () => {
    try {
      const result = await biometricService.authenticate('اختبار المصادقة البيومترية');
      
      if (result.success) {
        Alert.alert('نجح', 'تم اختبار المصادقة البيومترية بنجاح');
      } else {
        Alert.alert('فشل', result.error || 'فشل في اختبار المصادقة البيومترية');
      }
    } catch (error) {
      console.error('Failed to test biometric:', error);
      Alert.alert('خطأ', 'حدث خطأ أثناء اختبار المصادقة البيومترية');
    }
  };

  const getBiometricIcon = () => {
    switch (settings.type) {
      case 'fingerprint':
        return 'finger-print-outline';
      case 'face':
        return 'scan-outline';
      case 'iris':
        return 'eye-outline';
      default:
        return 'shield-outline';
    }
  };

  const getAutoLockTimeoutText = (minutes: number) => {
    if (minutes === 0) return 'معطل';
    if (minutes === 1) return 'دقيقة واحدة';
    if (minutes === 2) return 'دقيقتان';
    if (minutes < 11) return `${minutes} دقائق`;
    return `${minutes} دقيقة`;
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>جاري التحميل...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#3B82F6" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>المصادقة البيومترية</Text>
      </View>

      {/* Status Card */}
      <View style={styles.statusCard}>
        <View style={styles.statusIcon}>
          <Ionicons 
            name={getBiometricIcon() as any} 
            size={48} 
            color={settings.enabled ? '#10B981' : '#6B7280'} 
          />
        </View>
        <Text style={styles.statusTitle}>
          {settings.enabled ? 'مفعل' : 'معطل'}
        </Text>
        <Text style={styles.statusDescription}>
          {settings.enabled 
            ? `المصادقة بـ ${biometricService.getBiometricTypeName()}`
            : 'المصادقة البيومترية غير مفعلة'
          }
        </Text>
      </View>

      {/* Capabilities Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>معلومات الجهاز</Text>
        
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>الدعم المتوفر:</Text>
          <Text style={[styles.infoValue, { color: capabilities.isAvailable ? '#10B981' : '#EF4444' }]}>
            {capabilities.isAvailable ? 'متوفر' : 'غير متوفر'}
          </Text>
        </View>
        
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>البيانات المسجلة:</Text>
          <Text style={[styles.infoValue, { color: capabilities.isEnrolled ? '#10B981' : '#EF4444' }]}>
            {capabilities.isEnrolled ? 'مسجلة' : 'غير مسجلة'}
          </Text>
        </View>
        
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>الأنواع المدعومة:</Text>
          <Text style={styles.infoValue}>
            {capabilities.supportedTypes.length > 0 
              ? capabilities.supportedTypes.map(type => {
                  switch (type) {
                    case 1: return 'بصمة الإصبع';
                    case 2: return 'التعرف على الوجه';
                    case 3: return 'مسح القزحية';
                    default: return 'غير معروف';
                  }
                }).join(', ')
              : 'لا يوجد'
            }
          </Text>
        </View>
      </View>

      {/* Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>الإعدادات</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Ionicons name="shield-checkmark-outline" size={24} color="#3B82F6" />
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>تفعيل المصادقة البيومترية</Text>
              <Text style={styles.settingDescription}>
                استخدام البصمة أو الوجه لتسجيل الدخول
              </Text>
            </View>
          </View>
          <Switch
            value={settings.enabled}
            onValueChange={handleEnableBiometric}
            disabled={!capabilities.isAvailable || !capabilities.isEnrolled}
            trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
            thumbColor={settings.enabled ? '#FFFFFF' : '#9CA3AF'}
          />
        </View>

        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Ionicons name="key-outline" size={24} color="#F59E0B" />
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>الرجوع لكلمة المرور</Text>
              <Text style={styles.settingDescription}>
                السماح بالرجوع لكلمة المرور عند فشل المصادقة البيومترية
              </Text>
            </View>
          </View>
          <Switch
            value={settings.fallbackToPassword}
            onValueChange={(value) => handleSettingChange('fallbackToPassword', value)}
            disabled={!settings.enabled}
            trackColor={{ false: '#E5E7EB', true: '#F59E0B' }}
            thumbColor={settings.fallbackToPassword ? '#FFFFFF' : '#9CA3AF'}
          />
        </View>
      </View>

      {/* Auto Lock Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>القفل التلقائي</Text>
        
        <View style={styles.autoLockContainer}>
          <Text style={styles.autoLockLabel}>
            قفل التطبيق تلقائياً بعد: {getAutoLockTimeoutText(settings.autoLockTimeout)}
          </Text>
          
          <View style={styles.timeoutButtons}>
            {[0, 1, 2, 5, 10, 15, 30].map((minutes) => (
              <TouchableOpacity
                key={minutes}
                style={[
                  styles.timeoutButton,
                  settings.autoLockTimeout === minutes && styles.timeoutButtonActive
                ]}
                onPress={() => handleSettingChange('autoLockTimeout', minutes)}
                disabled={!settings.enabled}
              >
                <Text style={[
                  styles.timeoutButtonText,
                  settings.autoLockTimeout === minutes && styles.timeoutButtonTextActive
                ]}>
                  {minutes === 0 ? 'معطل' : `${minutes}د`}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* Test Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>اختبار</Text>
        
        <TouchableOpacity
          style={[styles.testButton, !settings.enabled && styles.testButtonDisabled]}
          onPress={testBiometric}
          disabled={!settings.enabled}
        >
          <Ionicons 
            name="checkmark-circle-outline" 
            size={20} 
            color={settings.enabled ? "white" : "#9CA3AF"} 
          />
          <Text style={[styles.testButtonText, !settings.enabled && styles.testButtonTextDisabled]}>
            اختبار المصادقة البيومترية
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  statusCard: {
    backgroundColor: 'white',
    margin: 20,
    padding: 30,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statusIcon: {
    width: 80,
    height: 80,
    backgroundColor: '#F3F4F6',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  statusDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  section: {
    backgroundColor: 'white',
    marginTop: 16,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937',
    paddingHorizontal: 20,
    marginBottom: 16,
    textAlign: 'right',
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'right',
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'right',
  },
  autoLockContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  autoLockLabel: {
    fontSize: 16,
    color: '#1F2937',
    marginBottom: 16,
    textAlign: 'right',
  },
  timeoutButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  timeoutButton: {
    width: '13%',
    aspectRatio: 1,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  timeoutButtonActive: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
  },
  timeoutButtonText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#6B7280',
  },
  timeoutButtonTextActive: {
    color: 'white',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    marginHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
  },
  testButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  testButtonTextDisabled: {
    color: '#9CA3AF',
  },
});
