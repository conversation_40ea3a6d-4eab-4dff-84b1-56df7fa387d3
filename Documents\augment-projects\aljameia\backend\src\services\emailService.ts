import nodemailer from 'nodemailer';
import { logger } from '@/utils/logger';

// Email configuration interface
interface EmailConfig {
  to: string | string[];
  subject: string;
  template?: string;
  html?: string;
  text?: string;
  data?: any;
  attachments?: Array<{
    filename: string;
    path?: string;
    content?: Buffer;
    contentType?: string;
  }>;
}

// Email templates
const emailTemplates = {
  emailVerification: (data: any) => ({
    subject: 'تأكيد البريد الإلكتروني - الجمعية',
    html: `
      <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">الجمعية</h1>
          <p style="color: white; margin: 10px 0 0 0;">منصة الجمعيات المالية</p>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333; margin-bottom: 20px;">مرحباً ${data.name}</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">
            شكراً لك على التسجيل في منصة الجمعية. لإكمال عملية التسجيل، يرجى تأكيد بريدك الإلكتروني بالنقر على الرابط أدناه:
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.verificationUrl}" 
               style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              تأكيد البريد الإلكتروني
            </a>
          </div>
          
          <p style="color: #999; font-size: 14px; margin-top: 30px;">
            إذا لم تقم بإنشاء حساب، يرجى تجاهل هذا البريد الإلكتروني.
          </p>
          
          <p style="color: #999; font-size: 14px;">
            هذا الرابط صالح لمدة 24 ساعة فقط.
          </p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p style="margin: 0;">© 2024 الجمعية. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    `,
    text: `
      مرحباً ${data.name},
      
      شكراً لك على التسجيل في منصة الجمعية.
      
      لتأكيد بريدك الإلكتروني، يرجى زيارة الرابط التالي:
      ${data.verificationUrl}
      
      إذا لم تقم بإنشاء حساب، يرجى تجاهل هذا البريد الإلكتروني.
      
      مع تحيات فريق الجمعية
    `
  }),

  passwordReset: (data: any) => ({
    subject: 'إعادة تعيين كلمة المرور - الجمعية',
    html: `
      <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">الجمعية</h1>
          <p style="color: white; margin: 10px 0 0 0;">منصة الجمعيات المالية</p>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333; margin-bottom: 20px;">مرحباً ${data.name}</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">
            تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك. إذا كنت قد طلبت ذلك، يرجى النقر على الرابط أدناه:
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.resetUrl}" 
               style="background: #e74c3c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              إعادة تعيين كلمة المرور
            </a>
          </div>
          
          <p style="color: #999; font-size: 14px; margin-top: 30px;">
            إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.
          </p>
          
          <p style="color: #999; font-size: 14px;">
            هذا الرابط صالح لمدة 10 دقائق فقط.
          </p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p style="margin: 0;">© 2024 الجمعية. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    `,
    text: `
      مرحباً ${data.name},
      
      تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك.
      
      لإعادة تعيين كلمة المرور، يرجى زيارة الرابط التالي:
      ${data.resetUrl}
      
      إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.
      
      مع تحيات فريق الجمعية
    `
  }),

  paymentConfirmation: (data: any) => ({
    subject: `تأكيد الدفع - ${data.amount} ${data.currency}`,
    html: `
      <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">الجمعية</h1>
          <p style="color: white; margin: 10px 0 0 0;">تأكيد الدفع</p>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333; margin-bottom: 20px;">مرحباً ${data.userName}</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">
            تم استلام دفعتك بنجاح. إليك تفاصيل العملية:
          </p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 10px; border-bottom: 1px solid #eee;"><strong>رقم العملية:</strong></td>
                <td style="padding: 10px; border-bottom: 1px solid #eee;">${data.transactionId}</td>
              </tr>
              <tr>
                <td style="padding: 10px; border-bottom: 1px solid #eee;"><strong>المبلغ:</strong></td>
                <td style="padding: 10px; border-bottom: 1px solid #eee;">${data.amount} ${data.currency}</td>
              </tr>
              <tr>
                <td style="padding: 10px; border-bottom: 1px solid #eee;"><strong>الجمعية:</strong></td>
                <td style="padding: 10px; border-bottom: 1px solid #eee;">${data.jamiyaName}</td>
              </tr>
              <tr>
                <td style="padding: 10px; border-bottom: 1px solid #eee;"><strong>طريقة الدفع:</strong></td>
                <td style="padding: 10px; border-bottom: 1px solid #eee;">${data.paymentMethod}</td>
              </tr>
              <tr>
                <td style="padding: 10px;"><strong>التاريخ:</strong></td>
                <td style="padding: 10px;">${data.date}</td>
              </tr>
            </table>
          </div>
          
          <p style="color: #666; line-height: 1.6;">
            شكراً لك على ثقتك في منصة الجمعية.
          </p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p style="margin: 0;">© 2024 الجمعية. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    `,
    text: `
      مرحباً ${data.userName},
      
      تم استلام دفعتك بنجاح.
      
      تفاصيل العملية:
      - رقم العملية: ${data.transactionId}
      - المبلغ: ${data.amount} ${data.currency}
      - الجمعية: ${data.jamiyaName}
      - طريقة الدفع: ${data.paymentMethod}
      - التاريخ: ${data.date}
      
      شكراً لك على ثقتك في منصة الجمعية.
      
      مع تحيات فريق الجمعية
    `
  }),

  jamiyaInvitation: (data: any) => ({
    subject: `دعوة للانضمام إلى جمعية ${data.jamiyaName}`,
    html: `
      <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">الجمعية</h1>
          <p style="color: white; margin: 10px 0 0 0;">دعوة للانضمام</p>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333; margin-bottom: 20px;">مرحباً ${data.inviteeName}</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">
            تمت دعوتك للانضمام إلى جمعية "${data.jamiyaName}" من قبل ${data.inviterName}.
          </p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">تفاصيل الجمعية:</h3>
            <p><strong>الاسم:</strong> ${data.jamiyaName}</p>
            <p><strong>الوصف:</strong> ${data.jamiyaDescription}</p>
            <p><strong>المبلغ الشهري:</strong> ${data.monthlyAmount} ريال</p>
            <p><strong>المدة:</strong> ${data.duration} شهر</p>
            <p><strong>عدد الأعضاء:</strong> ${data.currentMembers}/${data.maxMembers}</p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.joinUrl}" 
               style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              الانضمام إلى الجمعية
            </a>
          </div>
          
          <p style="color: #999; font-size: 14px;">
            رمز الدعوة: ${data.inviteCode}
          </p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p style="margin: 0;">© 2024 الجمعية. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    `,
    text: `
      مرحباً ${data.inviteeName},
      
      تمت دعوتك للانضمام إلى جمعية "${data.jamiyaName}" من قبل ${data.inviterName}.
      
      تفاصيل الجمعية:
      - الاسم: ${data.jamiyaName}
      - الوصف: ${data.jamiyaDescription}
      - المبلغ الشهري: ${data.monthlyAmount} ريال
      - المدة: ${data.duration} شهر
      - عدد الأعضاء: ${data.currentMembers}/${data.maxMembers}
      
      للانضمام، يرجى زيارة الرابط التالي:
      ${data.joinUrl}
      
      رمز الدعوة: ${data.inviteCode}
      
      مع تحيات فريق الجمعية
    `
  }),
};

// Create email transporter
const createTransporter = () => {
  const config = {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  };

  return nodemailer.createTransporter(config);
};

// Send email function
export const sendEmail = async (config: EmailConfig): Promise<void> => {
  try {
    const transporter = createTransporter();

    let emailContent: { subject: string; html?: string; text?: string } = {
      subject: config.subject,
    };

    // Use template if provided
    if (config.template && emailTemplates[config.template as keyof typeof emailTemplates]) {
      const templateFunction = emailTemplates[config.template as keyof typeof emailTemplates];
      emailContent = templateFunction(config.data || {});
    } else {
      emailContent.html = config.html;
      emailContent.text = config.text;
    }

    const mailOptions = {
      from: `"الجمعية" <${process.env.SMTP_USER}>`,
      to: Array.isArray(config.to) ? config.to.join(', ') : config.to,
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
      attachments: config.attachments,
    };

    const result = await transporter.sendMail(mailOptions);
    
    logger.info('Email sent successfully', {
      to: config.to,
      subject: config.subject,
      messageId: result.messageId,
    });

  } catch (error) {
    logger.error('Failed to send email', {
      to: config.to,
      subject: config.subject,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

// Send bulk emails
export const sendBulkEmails = async (emails: EmailConfig[]): Promise<void> => {
  const results = await Promise.allSettled(
    emails.map(email => sendEmail(email))
  );

  const failed = results.filter(result => result.status === 'rejected');
  
  if (failed.length > 0) {
    logger.warn(`${failed.length} out of ${emails.length} emails failed to send`);
  }

  logger.info(`Bulk email completed: ${emails.length - failed.length} sent, ${failed.length} failed`);
};

// Verify email configuration
export const verifyEmailConfig = async (): Promise<boolean> => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    logger.info('Email configuration verified successfully');
    return true;
  } catch (error) {
    logger.error('Email configuration verification failed:', error);
    return false;
  }
};

export default {
  sendEmail,
  sendBulkEmails,
  verifyEmailConfig,
};
