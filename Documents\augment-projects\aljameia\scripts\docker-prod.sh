#!/bin/bash

# ==============================================
# Docker Production Script for Aljameia
# ==============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_REGISTRY=${DOCKER_REGISTRY:-"aljameia"}
VERSION=${VERSION:-"latest"}
ENVIRONMENT=${ENVIRONMENT:-"production"}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    # Check Docker
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check docker-compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose is not installed"
        exit 1
    fi
    
    # Check production environment file
    if [ ! -f .env.production ]; then
        log_error ".env.production file not found"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Build production images
build_images() {
    log_info "Building production images..."
    
    # Build backend
    log_info "Building backend image..."
    docker build \
        --target production \
        --tag ${DOCKER_REGISTRY}/aljameia-backend:${VERSION} \
        --tag ${DOCKER_REGISTRY}/aljameia-backend:latest \
        ./backend
    
    # Build admin dashboard
    log_info "Building admin dashboard image..."
    docker build \
        --target production \
        --tag ${DOCKER_REGISTRY}/aljameia-admin:${VERSION} \
        --tag ${DOCKER_REGISTRY}/aljameia-admin:latest \
        ./admin-dashboard
    
    log_success "Images built successfully"
}

# Push images to registry
push_images() {
    log_info "Pushing images to registry..."
    
    docker push ${DOCKER_REGISTRY}/aljameia-backend:${VERSION}
    docker push ${DOCKER_REGISTRY}/aljameia-backend:latest
    docker push ${DOCKER_REGISTRY}/aljameia-admin:${VERSION}
    docker push ${DOCKER_REGISTRY}/aljameia-admin:latest
    
    log_success "Images pushed successfully"
}

# Deploy to production
deploy() {
    log_info "Deploying to production..."
    
    # Copy production environment
    cp .env.production .env
    
    # Pull latest images
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml pull
    
    # Start database services first
    log_info "Starting database services..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d mongodb redis
    
    # Wait for databases
    log_info "Waiting for databases to be ready..."
    sleep 30
    
    # Run migrations
    log_info "Running database migrations..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml run --rm backend npm run migrate
    
    # Start application services
    log_info "Starting application services..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    
    # Health check
    log_info "Performing health checks..."
    sleep 10
    
    if curl -f http://localhost/health > /dev/null 2>&1; then
        log_success "Deployment successful! Application is healthy."
    else
        log_error "Deployment failed! Application health check failed."
        exit 1
    fi
}

# Rolling update
rolling_update() {
    log_info "Performing rolling update..."
    
    # Build new images
    build_images
    
    # Update backend
    log_info "Updating backend service..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --no-deps backend
    
    # Wait and health check
    sleep 10
    if ! curl -f http://localhost/api/health > /dev/null 2>&1; then
        log_error "Backend update failed!"
        exit 1
    fi
    
    # Update admin dashboard
    log_info "Updating admin dashboard..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --no-deps admin-dashboard
    
    # Update nginx
    log_info "Updating nginx..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --no-deps nginx
    
    log_success "Rolling update completed successfully"
}

# Rollback to previous version
rollback() {
    local previous_version=${1:-"previous"}
    log_info "Rolling back to version: $previous_version"
    
    # Update image tags
    export VERSION=$previous_version
    
    # Deploy previous version
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --no-deps backend admin-dashboard
    
    log_success "Rollback completed"
}

# Scale services
scale() {
    local service=$1
    local replicas=$2
    
    if [ -z "$service" ] || [ -z "$replicas" ]; then
        log_error "Usage: scale <service> <replicas>"
        exit 1
    fi
    
    log_info "Scaling $service to $replicas replicas..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --scale $service=$replicas
    
    log_success "Scaling completed"
}

# Backup production data
backup() {
    local backup_name="aljameia_prod_backup_$(date +%Y%m%d_%H%M%S)"
    log_info "Creating production backup: $backup_name"
    
    # Create backup directory
    mkdir -p ./backups
    
    # Backup MongoDB
    docker-compose exec mongodb mongodump \
        --host localhost:27017 \
        --db aljameia \
        --username ${MONGO_ROOT_USERNAME} \
        --password ${MONGO_ROOT_PASSWORD} \
        --authenticationDatabase admin \
        --gzip \
        --out /tmp/$backup_name
    
    # Copy backup to host
    docker cp $(docker-compose ps -q mongodb):/tmp/$backup_name ./backups/
    
    # Backup uploaded files
    docker run --rm \
        -v aljameia_backend_uploads:/data \
        -v $(pwd)/backups:/backup \
        alpine tar czf /backup/${backup_name}_uploads.tar.gz -C /data .
    
    log_success "Backup created: $backup_name"
}

# Monitor production services
monitor() {
    log_info "Monitoring production services..."
    
    while true; do
        clear
        echo "=== Aljameia Production Status ==="
        echo "Time: $(date)"
        echo ""
        
        # Service status
        echo "=== Services ==="
        docker-compose ps
        echo ""
        
        # Resource usage
        echo "=== Resource Usage ==="
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
        echo ""
        
        # Health checks
        echo "=== Health Checks ==="
        if curl -f http://localhost/health > /dev/null 2>&1; then
            echo "✅ Application: Healthy"
        else
            echo "❌ Application: Unhealthy"
        fi
        
        if curl -f http://localhost/api/health > /dev/null 2>&1; then
            echo "✅ API: Healthy"
        else
            echo "❌ API: Unhealthy"
        fi
        
        sleep 5
    done
}

# View logs
logs() {
    local service=${1:-""}
    local lines=${2:-"100"}
    
    if [ -z "$service" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs --tail=$lines -f
    else
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs --tail=$lines -f $service
    fi
}

# Security scan
security_scan() {
    log_info "Running security scan..."
    
    # Scan images for vulnerabilities
    docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
        aquasec/trivy image ${DOCKER_REGISTRY}/aljameia-backend:${VERSION}
    
    docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
        aquasec/trivy image ${DOCKER_REGISTRY}/aljameia-admin:${VERSION}
    
    log_success "Security scan completed"
}

# Show help
show_help() {
    echo "Aljameia Docker Production Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build           Build production images"
    echo "  push            Push images to registry"
    echo "  deploy          Deploy to production"
    echo "  update          Perform rolling update"
    echo "  rollback [ver]  Rollback to previous version"
    echo "  scale <svc> <n> Scale service to n replicas"
    echo "  backup          Create production backup"
    echo "  monitor         Monitor production services"
    echo "  logs [svc] [n]  View logs (optionally for service, last n lines)"
    echo "  security        Run security scan"
    echo "  help            Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  DOCKER_REGISTRY Registry prefix (default: aljameia)"
    echo "  VERSION         Image version (default: latest)"
    echo "  ENVIRONMENT     Environment name (default: production)"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 deploy"
    echo "  VERSION=v1.2.3 $0 update"
    echo "  $0 scale backend 3"
    echo "  $0 logs backend 50"
}

# Main script
main() {
    local command=${1:-"help"}
    
    case $command in
        "build")
            check_prerequisites
            build_images
            ;;
        "push")
            check_prerequisites
            push_images
            ;;
        "deploy")
            check_prerequisites
            deploy
            ;;
        "update")
            check_prerequisites
            rolling_update
            ;;
        "rollback")
            check_prerequisites
            rollback $2
            ;;
        "scale")
            check_prerequisites
            scale $2 $3
            ;;
        "backup")
            check_prerequisites
            backup
            ;;
        "monitor")
            check_prerequisites
            monitor
            ;;
        "logs")
            check_prerequisites
            logs $2 $3
            ;;
        "security")
            check_prerequisites
            security_scan
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
