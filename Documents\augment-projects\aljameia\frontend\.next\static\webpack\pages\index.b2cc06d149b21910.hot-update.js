"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/layout/NotificationDropdown.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/NotificationDropdown.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Menu,Transition!=!../node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BellIcon,CheckIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"__barrel_optimize__?names=formatDistanceToNow!=!../node_modules/date-fns/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst NotificationDropdown = ()=>{\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            type: \"info\",\n            title: t(\"notifications.payment_reminder.title\"),\n            message: t(\"notifications.payment_reminder.message\"),\n            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n            read: false,\n            actionUrl: \"/payments\"\n        },\n        {\n            id: \"2\",\n            type: \"success\",\n            title: t(\"notifications.document_approved.title\"),\n            message: t(\"notifications.document_approved.message\"),\n            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),\n            read: false,\n            actionUrl: \"/documents\"\n        },\n        {\n            id: \"3\",\n            type: \"warning\",\n            title: t(\"notifications.membership_expiring.title\"),\n            message: t(\"notifications.membership_expiring.message\"),\n            timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),\n            read: true,\n            actionUrl: \"/membership\"\n        }\n    ]);\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    const locale = router.locale === \"ar\" ? ar : enUS;\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CheckIcon, {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 16\n                }, undefined);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ExclamationTriangleIcon, {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 16\n                }, undefined);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.XMarkIcon, {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.InformationCircleIcon, {\n                    className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const markAsRead = (id)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === id ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    };\n    const markAllAsRead = ()=>{\n        setNotifications((prev)=>prev.map((notification)=>({\n                    ...notification,\n                    read: true\n                })));\n    };\n    const handleNotificationClick = (notification)=>{\n        markAsRead(notification.id);\n        if (notification.actionUrl) {\n            router.push(notification.actionUrl);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu, {\n        as: \"div\",\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Button, {\n                    className: \"relative rounded-full p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: t(\"notifications.open\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.BellIcon, {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined),\n                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-medium text-white\",\n                            children: unreadCount > 9 ? \"9+\" : unreadCount\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Transition, {\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                enter: \"transition ease-out duration-100\",\n                enterFrom: \"transform opacity-0 scale-95\",\n                enterTo: \"transform opacity-100 scale-100\",\n                leave: \"transition ease-in duration-75\",\n                leaveFrom: \"transform opacity-100 scale-100\",\n                leaveTo: \"transform opacity-0 scale-95\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Items, {\n                    className: \"absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-3 border-b border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: t(\"notifications.title\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: markAllAsRead,\n                                        className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                        children: t(\"notifications.mark_all_read\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-96 overflow-y-auto\",\n                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-6 text-center text-sm text-gray-500\",\n                                children: t(\"notifications.no_notifications\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, undefined) : notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Item, {\n                                    children: (param)=>{\n                                        let { active } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>handleNotificationClick(notification),\n                                            className: \"\".concat(active ? \"bg-gray-50\" : \"\", \" \").concat(!notification.read ? \"bg-blue-50\" : \"\", \" cursor-pointer px-4 py-3 border-b border-gray-100 last:border-b-0\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 mt-1\",\n                                                        children: getNotificationIcon(notification.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium \".concat(!notification.read ? \"text-gray-900\" : \"text-gray-700\"),\n                                                                        children: notification.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                        lineNumber: 159,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                        lineNumber: 165,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__.formatDistanceToNow)(notification.timestamp, {\n                                                                    addSuffix: true,\n                                                                    locale\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    }\n                                }, notification.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined),\n                        notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-3 border-t border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/notifications\"),\n                                className: \"w-full text-center text-sm text-blue-600 hover:text-blue-800\",\n                                children: t(\"notifications.view_all\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aljameia\\\\frontend\\\\src\\\\components\\\\layout\\\\NotificationDropdown.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NotificationDropdown, \"KJav3DzRngB5/0eDu4Bo1b9sNUw=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = NotificationDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (NotificationDropdown);\nvar _c;\n$RefreshReg$(_c, \"NotificationDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvTm90aWZpY2F0aW9uRHJvcGRvd24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBMkM7QUFDRztBQUNPO0FBT2hCO0FBQ1U7QUFDUDtBQVl4QyxNQUFNWSx1QkFBdUI7O0lBQzNCLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUdYLDREQUFjQSxDQUFDO0lBQzdCLE1BQU1ZLFNBQVNILHNEQUFTQTtJQUN4QixNQUFNLENBQUNJLGVBQWVDLGlCQUFpQixHQUFHZiwrQ0FBUUEsQ0FBaUI7UUFDakU7WUFDRWdCLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxPQUFPTixFQUFFO1lBQ1RPLFNBQVNQLEVBQUU7WUFDWFEsV0FBVyxJQUFJQyxLQUFLQSxLQUFLQyxHQUFHLEtBQUssSUFBSSxLQUFLLEtBQUs7WUFDL0NDLE1BQU07WUFDTkMsV0FBVztRQUNiO1FBQ0E7WUFDRVIsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE9BQU9OLEVBQUU7WUFDVE8sU0FBU1AsRUFBRTtZQUNYUSxXQUFXLElBQUlDLEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxLQUFLLEtBQUssS0FBSztZQUNoREMsTUFBTTtZQUNOQyxXQUFXO1FBQ2I7UUFDQTtZQUNFUixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsT0FBT04sRUFBRTtZQUNUTyxTQUFTUCxFQUFFO1lBQ1hRLFdBQVcsSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSyxLQUFLLEtBQUs7WUFDcERDLE1BQU07WUFDTkMsV0FBVztRQUNiO0tBQ0Q7SUFFRCxNQUFNQyxjQUFjWCxjQUFjWSxNQUFNLENBQUNDLENBQUFBLElBQUssQ0FBQ0EsRUFBRUosSUFBSSxFQUFFSyxNQUFNO0lBQzdELE1BQU1DLFNBQVNoQixPQUFPZ0IsTUFBTSxLQUFLLE9BQU9DLEtBQUtDO0lBRTdDLE1BQU1DLHNCQUFzQixDQUFDZjtRQUMzQixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNaLG1LQUFTQTtvQkFBQzRCLFdBQVU7Ozs7OztZQUM5QixLQUFLO2dCQUNILHFCQUFPLDhEQUFDM0IsaUxBQXVCQTtvQkFBQzJCLFdBQVU7Ozs7OztZQUM1QyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDekIsbUtBQVNBO29CQUFDeUIsV0FBVTs7Ozs7O1lBQzlCO2dCQUNFLHFCQUFPLDhEQUFDMUIsK0tBQXFCQTtvQkFBQzBCLFdBQVU7Ozs7OztRQUM1QztJQUNGO0lBRUEsTUFBTUMsYUFBYSxDQUFDbEI7UUFDbEJELGlCQUFpQm9CLENBQUFBLE9BQ2ZBLEtBQUtDLEdBQUcsQ0FBQ0MsQ0FBQUEsZUFDUEEsYUFBYXJCLEVBQUUsS0FBS0EsS0FDaEI7b0JBQUUsR0FBR3FCLFlBQVk7b0JBQUVkLE1BQU07Z0JBQUssSUFDOUJjO0lBR1Y7SUFFQSxNQUFNQyxnQkFBZ0I7UUFDcEJ2QixpQkFBaUJvQixDQUFBQSxPQUNmQSxLQUFLQyxHQUFHLENBQUNDLENBQUFBLGVBQWlCO29CQUFFLEdBQUdBLFlBQVk7b0JBQUVkLE1BQU07Z0JBQUs7SUFFNUQ7SUFFQSxNQUFNZ0IsMEJBQTBCLENBQUNGO1FBQy9CSCxXQUFXRyxhQUFhckIsRUFBRTtRQUMxQixJQUFJcUIsYUFBYWIsU0FBUyxFQUFFO1lBQzFCWCxPQUFPMkIsSUFBSSxDQUFDSCxhQUFhYixTQUFTO1FBQ3BDO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3RCLHlGQUFJQTtRQUFDdUMsSUFBRztRQUFNUixXQUFVOzswQkFDdkIsOERBQUNTOzBCQUNDLDRFQUFDeEMseUZBQUlBLENBQUN5QyxNQUFNO29CQUFDVixXQUFVOztzQ0FDckIsOERBQUNXOzRCQUFLWCxXQUFVO3NDQUFXckIsRUFBRTs7Ozs7O3NDQUM3Qiw4REFBQ1Isa0tBQVFBOzRCQUFDNkIsV0FBVTs7Ozs7O3dCQUNuQlIsY0FBYyxtQkFDYiw4REFBQ21COzRCQUFLWCxXQUFVO3NDQUNiUixjQUFjLElBQUksT0FBT0E7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1sQyw4REFBQ3RCLCtGQUFVQTtnQkFDVHNDLElBQUkxQywyQ0FBUUE7Z0JBQ1o4QyxPQUFNO2dCQUNOQyxXQUFVO2dCQUNWQyxTQUFRO2dCQUNSQyxPQUFNO2dCQUNOQyxXQUFVO2dCQUNWQyxTQUFROzBCQUVSLDRFQUFDaEQseUZBQUlBLENBQUNpRCxLQUFLO29CQUFDbEIsV0FBVTs7c0NBQ3BCLDhEQUFDUzs0QkFBSVQsV0FBVTtzQ0FDYiw0RUFBQ1M7Z0NBQUlULFdBQVU7O2tEQUNiLDhEQUFDbUI7d0NBQUduQixXQUFVO2tEQUNYckIsRUFBRTs7Ozs7O29DQUVKYSxjQUFjLG1CQUNiLDhEQUFDNEI7d0NBQ0NDLFNBQVNoQjt3Q0FDVEwsV0FBVTtrREFFVHJCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1YLDhEQUFDOEI7NEJBQUlULFdBQVU7c0NBQ1puQixjQUFjYyxNQUFNLEtBQUssa0JBQ3hCLDhEQUFDYztnQ0FBSVQsV0FBVTswQ0FDWnJCLEVBQUU7Ozs7OzRDQUdMRSxjQUFjc0IsR0FBRyxDQUFDLENBQUNDLDZCQUNqQiw4REFBQ25DLHlGQUFJQSxDQUFDcUQsSUFBSTs4Q0FDUDs0Q0FBQyxFQUFFQyxNQUFNLEVBQUU7NkRBQ1YsOERBQUNkOzRDQUNDWSxTQUFTLElBQU1mLHdCQUF3QkY7NENBQ3ZDSixXQUFXLEdBR1QsT0FGQXVCLFNBQVMsZUFBZSxJQUN6QixLQUVBLE9BREMsQ0FBQ25CLGFBQWFkLElBQUksR0FBRyxlQUFlLElBQ3JDO3NEQUVELDRFQUFDbUI7Z0RBQUlULFdBQVU7O2tFQUNiLDhEQUFDUzt3REFBSVQsV0FBVTtrRUFDWkQsb0JBQW9CSyxhQUFhcEIsSUFBSTs7Ozs7O2tFQUV4Qyw4REFBQ3lCO3dEQUFJVCxXQUFVOzswRUFDYiw4REFBQ1M7Z0VBQUlULFdBQVU7O2tGQUNiLDhEQUFDd0I7d0VBQUV4QixXQUFXLHVCQUViLE9BREMsQ0FBQ0ksYUFBYWQsSUFBSSxHQUFHLGtCQUFrQjtrRkFFdENjLGFBQWFuQixLQUFLOzs7Ozs7b0VBRXBCLENBQUNtQixhQUFhZCxJQUFJLGtCQUNqQiw4REFBQ21CO3dFQUFJVCxXQUFVOzs7Ozs7Ozs7Ozs7MEVBR25CLDhEQUFDd0I7Z0VBQUV4QixXQUFVOzBFQUNWSSxhQUFhbEIsT0FBTzs7Ozs7OzBFQUV2Qiw4REFBQ3NDO2dFQUFFeEIsV0FBVTswRUFDVnhCLHdHQUFtQkEsQ0FBQzRCLGFBQWFqQixTQUFTLEVBQUU7b0VBQzNDc0MsV0FBVztvRUFDWDdCO2dFQUNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBaENJUSxhQUFhckIsRUFBRTs7Ozs7Ozs7Ozt3QkEyQ3BDRixjQUFjYyxNQUFNLEdBQUcsbUJBQ3RCLDhEQUFDYzs0QkFBSVQsV0FBVTtzQ0FDYiw0RUFBQ29CO2dDQUNDQyxTQUFTLElBQU16QyxPQUFPMkIsSUFBSSxDQUFDO2dDQUMzQlAsV0FBVTswQ0FFVHJCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRbkI7R0FoTE1EOztRQUNVVix3REFBY0E7UUFDYlMsa0RBQVNBOzs7S0FGcEJDO0FBa0xOLCtEQUFlQSxvQkFBb0JBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L05vdGlmaWNhdGlvbkRyb3Bkb3duLnRzeD9mYzM5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZyYWdtZW50LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnbmV4dC1pMThuZXh0JztcbmltcG9ydCB7IE1lbnUsIFRyYW5zaXRpb24gfSBmcm9tICdAaGVhZGxlc3N1aS9yZWFjdCc7XG5pbXBvcnQgeyBcbiAgQmVsbEljb24sIFxuICBDaGVja0ljb24sXG4gIEV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uLFxuICBJbmZvcm1hdGlvbkNpcmNsZUljb24sXG4gIFhNYXJrSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuaW1wb3J0IHsgZm9ybWF0RGlzdGFuY2VUb05vdyB9IGZyb20gJ2RhdGUtZm5zJztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvcm91dGVyJztcblxuaW50ZXJmYWNlIE5vdGlmaWNhdGlvbiB7XG4gIGlkOiBzdHJpbmc7XG4gIHR5cGU6ICdpbmZvJyB8ICd3YXJuaW5nJyB8ICdzdWNjZXNzJyB8ICdlcnJvcic7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgdGltZXN0YW1wOiBEYXRlO1xuICByZWFkOiBib29sZWFuO1xuICBhY3Rpb25Vcmw/OiBzdHJpbmc7XG59XG5cbmNvbnN0IE5vdGlmaWNhdGlvbkRyb3Bkb3duID0gKCkgPT4ge1xuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCdjb21tb24nKTtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IFtub3RpZmljYXRpb25zLCBzZXROb3RpZmljYXRpb25zXSA9IHVzZVN0YXRlPE5vdGlmaWNhdGlvbltdPihbXG4gICAge1xuICAgICAgaWQ6ICcxJyxcbiAgICAgIHR5cGU6ICdpbmZvJyxcbiAgICAgIHRpdGxlOiB0KCdub3RpZmljYXRpb25zLnBheW1lbnRfcmVtaW5kZXIudGl0bGUnKSxcbiAgICAgIG1lc3NhZ2U6IHQoJ25vdGlmaWNhdGlvbnMucGF5bWVudF9yZW1pbmRlci5tZXNzYWdlJyksXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKERhdGUubm93KCkgLSAyICogNjAgKiA2MCAqIDEwMDApLCAvLyAyIGhvdXJzIGFnb1xuICAgICAgcmVhZDogZmFsc2UsXG4gICAgICBhY3Rpb25Vcmw6ICcvcGF5bWVudHMnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJzInLFxuICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgdGl0bGU6IHQoJ25vdGlmaWNhdGlvbnMuZG9jdW1lbnRfYXBwcm92ZWQudGl0bGUnKSxcbiAgICAgIG1lc3NhZ2U6IHQoJ25vdGlmaWNhdGlvbnMuZG9jdW1lbnRfYXBwcm92ZWQubWVzc2FnZScpLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMjQgKiA2MCAqIDYwICogMTAwMCksIC8vIDEgZGF5IGFnb1xuICAgICAgcmVhZDogZmFsc2UsXG4gICAgICBhY3Rpb25Vcmw6ICcvZG9jdW1lbnRzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICczJyxcbiAgICAgIHR5cGU6ICd3YXJuaW5nJyxcbiAgICAgIHRpdGxlOiB0KCdub3RpZmljYXRpb25zLm1lbWJlcnNoaXBfZXhwaXJpbmcudGl0bGUnKSxcbiAgICAgIG1lc3NhZ2U6IHQoJ25vdGlmaWNhdGlvbnMubWVtYmVyc2hpcF9leHBpcmluZy5tZXNzYWdlJyksXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKERhdGUubm93KCkgLSAzICogMjQgKiA2MCAqIDYwICogMTAwMCksIC8vIDMgZGF5cyBhZ29cbiAgICAgIHJlYWQ6IHRydWUsXG4gICAgICBhY3Rpb25Vcmw6ICcvbWVtYmVyc2hpcCdcbiAgICB9XG4gIF0pO1xuXG4gIGNvbnN0IHVucmVhZENvdW50ID0gbm90aWZpY2F0aW9ucy5maWx0ZXIobiA9PiAhbi5yZWFkKS5sZW5ndGg7XG4gIGNvbnN0IGxvY2FsZSA9IHJvdXRlci5sb2NhbGUgPT09ICdhcicgPyBhciA6IGVuVVM7XG5cbiAgY29uc3QgZ2V0Tm90aWZpY2F0aW9uSWNvbiA9ICh0eXBlOiBOb3RpZmljYXRpb25bJ3R5cGUnXSkgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSAnc3VjY2Vzcyc6XG4gICAgICAgIHJldHVybiA8Q2hlY2tJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmVlbi01MDBcIiAvPjtcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgICByZXR1cm4gPEV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC15ZWxsb3ctNTAwXCIgLz47XG4gICAgICBjYXNlICdlcnJvcic6XG4gICAgICAgIHJldHVybiA8WE1hcmtJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1yZWQtNTAwXCIgLz47XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPEluZm9ybWF0aW9uQ2lyY2xlSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS01MDBcIiAvPjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbWFya0FzUmVhZCA9IChpZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0Tm90aWZpY2F0aW9ucyhwcmV2ID0+IFxuICAgICAgcHJldi5tYXAobm90aWZpY2F0aW9uID0+IFxuICAgICAgICBub3RpZmljYXRpb24uaWQgPT09IGlkIFxuICAgICAgICAgID8geyAuLi5ub3RpZmljYXRpb24sIHJlYWQ6IHRydWUgfVxuICAgICAgICAgIDogbm90aWZpY2F0aW9uXG4gICAgICApXG4gICAgKTtcbiAgfTtcblxuICBjb25zdCBtYXJrQWxsQXNSZWFkID0gKCkgPT4ge1xuICAgIHNldE5vdGlmaWNhdGlvbnMocHJldiA9PiBcbiAgICAgIHByZXYubWFwKG5vdGlmaWNhdGlvbiA9PiAoeyAuLi5ub3RpZmljYXRpb24sIHJlYWQ6IHRydWUgfSkpXG4gICAgKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVOb3RpZmljYXRpb25DbGljayA9IChub3RpZmljYXRpb246IE5vdGlmaWNhdGlvbikgPT4ge1xuICAgIG1hcmtBc1JlYWQobm90aWZpY2F0aW9uLmlkKTtcbiAgICBpZiAobm90aWZpY2F0aW9uLmFjdGlvblVybCkge1xuICAgICAgcm91dGVyLnB1c2gobm90aWZpY2F0aW9uLmFjdGlvblVybCk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPE1lbnUgYXM9XCJkaXZcIiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgPGRpdj5cbiAgICAgICAgPE1lbnUuQnV0dG9uIGNsYXNzTmFtZT1cInJlbGF0aXZlIHJvdW5kZWQtZnVsbCBwLTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpyaW5nLW9mZnNldC0yXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPnt0KCdub3RpZmljYXRpb25zLm9wZW4nKX08L3NwYW4+XG4gICAgICAgICAgPEJlbGxJY29uIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPlxuICAgICAgICAgIHt1bnJlYWRDb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0xIC1yaWdodC0xIGZsZXggaC01IHctNSBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1mdWxsIGJnLXJlZC01MDAgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgIHt1bnJlYWRDb3VudCA+IDkgPyAnOSsnIDogdW5yZWFkQ291bnR9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9NZW51LkJ1dHRvbj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8VHJhbnNpdGlvblxuICAgICAgICBhcz17RnJhZ21lbnR9XG4gICAgICAgIGVudGVyPVwidHJhbnNpdGlvbiBlYXNlLW91dCBkdXJhdGlvbi0xMDBcIlxuICAgICAgICBlbnRlckZyb209XCJ0cmFuc2Zvcm0gb3BhY2l0eS0wIHNjYWxlLTk1XCJcbiAgICAgICAgZW50ZXJUbz1cInRyYW5zZm9ybSBvcGFjaXR5LTEwMCBzY2FsZS0xMDBcIlxuICAgICAgICBsZWF2ZT1cInRyYW5zaXRpb24gZWFzZS1pbiBkdXJhdGlvbi03NVwiXG4gICAgICAgIGxlYXZlRnJvbT1cInRyYW5zZm9ybSBvcGFjaXR5LTEwMCBzY2FsZS0xMDBcIlxuICAgICAgICBsZWF2ZVRvPVwidHJhbnNmb3JtIG9wYWNpdHktMCBzY2FsZS05NVwiXG4gICAgICA+XG4gICAgICAgIDxNZW51Lkl0ZW1zIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTAgei0xMCBtdC0yIHctODAgb3JpZ2luLXRvcC1yaWdodCByb3VuZGVkLW1kIGJnLXdoaXRlIHB5LTEgc2hhZG93LWxnIHJpbmctMSByaW5nLWJsYWNrIHJpbmctb3BhY2l0eS01IGZvY3VzOm91dGxpbmUtbm9uZVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS0zIGJvcmRlci1iIGJvcmRlci1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgIHt0KCdub3RpZmljYXRpb25zLnRpdGxlJyl9XG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIHt1bnJlYWRDb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e21hcmtBbGxBc1JlYWR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge3QoJ25vdGlmaWNhdGlvbnMubWFya19hbGxfcmVhZCcpfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC1oLTk2IG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAge25vdGlmaWNhdGlvbnMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgcHktNiB0ZXh0LWNlbnRlciB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICB7dCgnbm90aWZpY2F0aW9ucy5ub19ub3RpZmljYXRpb25zJyl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgbm90aWZpY2F0aW9ucy5tYXAoKG5vdGlmaWNhdGlvbikgPT4gKFxuICAgICAgICAgICAgICAgIDxNZW51Lkl0ZW0ga2V5PXtub3RpZmljYXRpb24uaWR9PlxuICAgICAgICAgICAgICAgICAgeyh7IGFjdGl2ZSB9KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVOb3RpZmljYXRpb25DbGljayhub3RpZmljYXRpb24pfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YCR7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmUgPyAnYmctZ3JheS01MCcgOiAnJ1xuICAgICAgICAgICAgICAgICAgICAgIH0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICFub3RpZmljYXRpb24ucmVhZCA/ICdiZy1ibHVlLTUwJyA6ICcnXG4gICAgICAgICAgICAgICAgICAgICAgfSBjdXJzb3ItcG9pbnRlciBweC00IHB5LTMgYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwIGxhc3Q6Ym9yZGVyLWItMGB9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Tm90aWZpY2F0aW9uSWNvbihub3RpZmljYXRpb24udHlwZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAhbm90aWZpY2F0aW9uLnJlYWQgPyAndGV4dC1ncmF5LTkwMCcgOiAndGV4dC1ncmF5LTcwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IW5vdGlmaWNhdGlvbi5yZWFkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ibHVlLTUwMCByb3VuZGVkLWZ1bGwgZmxleC1zaHJpbmstMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb24ubWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREaXN0YW5jZVRvTm93KG5vdGlmaWNhdGlvbi50aW1lc3RhbXAsIHsgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhZGRTdWZmaXg6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhbGUgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9NZW51Lkl0ZW0+XG4gICAgICAgICAgICAgICkpXG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAge25vdGlmaWNhdGlvbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgcHktMyBib3JkZXItdCBib3JkZXItZ3JheS0xMDBcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvbm90aWZpY2F0aW9ucycpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWNlbnRlciB0ZXh0LXNtIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7dCgnbm90aWZpY2F0aW9ucy52aWV3X2FsbCcpfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvTWVudS5JdGVtcz5cbiAgICAgIDwvVHJhbnNpdGlvbj5cbiAgICA8L01lbnU+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBOb3RpZmljYXRpb25Ecm9wZG93bjtcbiJdLCJuYW1lcyI6WyJGcmFnbWVudCIsInVzZVN0YXRlIiwidXNlVHJhbnNsYXRpb24iLCJNZW51IiwiVHJhbnNpdGlvbiIsIkJlbGxJY29uIiwiQ2hlY2tJY29uIiwiRXhjbGFtYXRpb25UcmlhbmdsZUljb24iLCJJbmZvcm1hdGlvbkNpcmNsZUljb24iLCJYTWFya0ljb24iLCJmb3JtYXREaXN0YW5jZVRvTm93IiwidXNlUm91dGVyIiwiTm90aWZpY2F0aW9uRHJvcGRvd24iLCJ0Iiwicm91dGVyIiwibm90aWZpY2F0aW9ucyIsInNldE5vdGlmaWNhdGlvbnMiLCJpZCIsInR5cGUiLCJ0aXRsZSIsIm1lc3NhZ2UiLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwicmVhZCIsImFjdGlvblVybCIsInVucmVhZENvdW50IiwiZmlsdGVyIiwibiIsImxlbmd0aCIsImxvY2FsZSIsImFyIiwiZW5VUyIsImdldE5vdGlmaWNhdGlvbkljb24iLCJjbGFzc05hbWUiLCJtYXJrQXNSZWFkIiwicHJldiIsIm1hcCIsIm5vdGlmaWNhdGlvbiIsIm1hcmtBbGxBc1JlYWQiLCJoYW5kbGVOb3RpZmljYXRpb25DbGljayIsInB1c2giLCJhcyIsImRpdiIsIkJ1dHRvbiIsInNwYW4iLCJlbnRlciIsImVudGVyRnJvbSIsImVudGVyVG8iLCJsZWF2ZSIsImxlYXZlRnJvbSIsImxlYXZlVG8iLCJJdGVtcyIsImgzIiwiYnV0dG9uIiwib25DbGljayIsIkl0ZW0iLCJhY3RpdmUiLCJwIiwiYWRkU3VmZml4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/layout/NotificationDropdown.tsx\n"));

/***/ })

});