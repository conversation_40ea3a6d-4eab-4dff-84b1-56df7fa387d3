#!/bin/bash

# ==============================================
# Backup Script for Aljameia Platform
# ==============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKUP_DIR="/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="aljameia_backup_${TIMESTAMP}"
RETENTION_DAYS=${RETENTION_DAYS:-30}

# Database configuration
MONGO_HOST=${MONGO_HOST:-"mongodb"}
MONGO_PORT=${MONGO_PORT:-27017}
MONGO_DATABASE=${MONGO_DATABASE:-"aljameia"}
MONGO_USERNAME=${MONGO_ROOT_USERNAME:-"admin"}
MONGO_PASSWORD=${MONGO_ROOT_PASSWORD:-"password"}

REDIS_HOST=${REDIS_HOST:-"redis"}
REDIS_PORT=${REDIS_PORT:-6379}
REDIS_PASSWORD=${REDIS_PASSWORD:-"redis_password_123"}

# S3 configuration (optional)
S3_BUCKET=${S3_BUCKET:-""}
AWS_REGION=${AWS_REGION:-"us-east-1"}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create backup directory
create_backup_dir() {
    mkdir -p "${BACKUP_DIR}/${BACKUP_NAME}"
    log_info "Created backup directory: ${BACKUP_DIR}/${BACKUP_NAME}"
}

# Backup MongoDB
backup_mongodb() {
    log_info "Starting MongoDB backup..."
    
    local mongo_backup_dir="${BACKUP_DIR}/${BACKUP_NAME}/mongodb"
    mkdir -p "$mongo_backup_dir"
    
    # Create MongoDB dump
    mongodump \
        --host "${MONGO_HOST}:${MONGO_PORT}" \
        --db "$MONGO_DATABASE" \
        --username "$MONGO_USERNAME" \
        --password "$MONGO_PASSWORD" \
        --authenticationDatabase admin \
        --gzip \
        --out "$mongo_backup_dir"
    
    if [ $? -eq 0 ]; then
        log_success "MongoDB backup completed"
        
        # Get backup size
        local backup_size=$(du -sh "$mongo_backup_dir" | cut -f1)
        log_info "MongoDB backup size: $backup_size"
    else
        log_error "MongoDB backup failed"
        return 1
    fi
}

# Backup Redis
backup_redis() {
    log_info "Starting Redis backup..."
    
    local redis_backup_dir="${BACKUP_DIR}/${BACKUP_NAME}/redis"
    mkdir -p "$redis_backup_dir"
    
    # Create Redis dump
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --rdb "${redis_backup_dir}/dump.rdb"
    
    if [ $? -eq 0 ]; then
        log_success "Redis backup completed"
        
        # Get backup size
        local backup_size=$(du -sh "${redis_backup_dir}/dump.rdb" | cut -f1)
        log_info "Redis backup size: $backup_size"
    else
        log_error "Redis backup failed"
        return 1
    fi
}

# Backup application files
backup_app_files() {
    log_info "Starting application files backup..."
    
    local files_backup_dir="${BACKUP_DIR}/${BACKUP_NAME}/files"
    mkdir -p "$files_backup_dir"
    
    # Backup uploaded files
    if [ -d "/app/uploads" ]; then
        cp -r /app/uploads "$files_backup_dir/"
        log_info "Uploaded files backed up"
    fi
    
    # Backup logs
    if [ -d "/app/logs" ]; then
        cp -r /app/logs "$files_backup_dir/"
        log_info "Log files backed up"
    fi
    
    # Backup configuration files
    if [ -d "/app/config" ]; then
        cp -r /app/config "$files_backup_dir/"
        log_info "Configuration files backed up"
    fi
    
    log_success "Application files backup completed"
}

# Create backup metadata
create_metadata() {
    log_info "Creating backup metadata..."
    
    local metadata_file="${BACKUP_DIR}/${BACKUP_NAME}/metadata.json"
    
    cat > "$metadata_file" << EOF
{
  "backup_name": "$BACKUP_NAME",
  "timestamp": "$TIMESTAMP",
  "date": "$(date -Iseconds)",
  "version": "1.0.0",
  "environment": "${NODE_ENV:-development}",
  "databases": {
    "mongodb": {
      "host": "$MONGO_HOST",
      "port": $MONGO_PORT,
      "database": "$MONGO_DATABASE"
    },
    "redis": {
      "host": "$REDIS_HOST",
      "port": $REDIS_PORT
    }
  },
  "backup_type": "full",
  "retention_days": $RETENTION_DAYS
}
EOF
    
    log_success "Backup metadata created"
}

# Compress backup
compress_backup() {
    log_info "Compressing backup..."
    
    cd "$BACKUP_DIR"
    tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
    
    if [ $? -eq 0 ]; then
        # Remove uncompressed directory
        rm -rf "$BACKUP_NAME"
        
        local compressed_size=$(du -sh "${BACKUP_NAME}.tar.gz" | cut -f1)
        log_success "Backup compressed successfully"
        log_info "Compressed backup size: $compressed_size"
    else
        log_error "Backup compression failed"
        return 1
    fi
}

# Upload to S3 (optional)
upload_to_s3() {
    if [ -z "$S3_BUCKET" ]; then
        log_info "S3 upload skipped (no bucket configured)"
        return 0
    fi
    
    log_info "Uploading backup to S3..."
    
    aws s3 cp "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" \
        "s3://${S3_BUCKET}/backups/${BACKUP_NAME}.tar.gz" \
        --region "$AWS_REGION"
    
    if [ $? -eq 0 ]; then
        log_success "Backup uploaded to S3 successfully"
    else
        log_error "S3 upload failed"
        return 1
    fi
}

# Clean old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups..."
    
    # Remove local backups older than retention period
    find "$BACKUP_DIR" -name "aljameia_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete
    
    local remaining_backups=$(find "$BACKUP_DIR" -name "aljameia_backup_*.tar.gz" | wc -l)
    log_info "Local backups remaining: $remaining_backups"
    
    # Clean S3 backups if configured
    if [ -n "$S3_BUCKET" ]; then
        local cutoff_date=$(date -d "$RETENTION_DAYS days ago" +%Y%m%d)
        
        aws s3 ls "s3://${S3_BUCKET}/backups/" | while read -r line; do
            local backup_file=$(echo "$line" | awk '{print $4}')
            local backup_date=$(echo "$backup_file" | grep -o '[0-9]\{8\}' | head -1)
            
            if [ "$backup_date" -lt "$cutoff_date" ]; then
                aws s3 rm "s3://${S3_BUCKET}/backups/${backup_file}"
                log_info "Removed old S3 backup: $backup_file"
            fi
        done
    fi
    
    log_success "Old backups cleaned up"
}

# Verify backup integrity
verify_backup() {
    log_info "Verifying backup integrity..."
    
    local backup_file="${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
    
    # Test archive integrity
    tar -tzf "$backup_file" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "Backup integrity verified"
        
        # Generate checksum
        local checksum=$(sha256sum "$backup_file" | cut -d' ' -f1)
        echo "$checksum" > "${backup_file}.sha256"
        log_info "Backup checksum: $checksum"
    else
        log_error "Backup integrity check failed"
        return 1
    fi
}

# Send notification
send_notification() {
    local status=$1
    local message=$2
    
    if [ "$status" = "success" ]; then
        log_success "$message"
    else
        log_error "$message"
    fi
    
    # Send email notification (if configured)
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        local subject="Aljameia Backup $status"
        echo "$message" | mail -s "$subject" "$NOTIFICATION_EMAIL"
    fi
    
    # Send webhook notification (if configured)
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"status\":\"$status\",\"message\":\"$message\",\"timestamp\":\"$TIMESTAMP\"}"
    fi
}

# Main backup function
main() {
    log_info "Starting Aljameia platform backup..."
    log_info "Backup name: $BACKUP_NAME"
    
    # Check prerequisites
    if ! command -v mongodump &> /dev/null; then
        log_error "mongodump not found. Please install MongoDB tools."
        exit 1
    fi
    
    if ! command -v redis-cli &> /dev/null; then
        log_error "redis-cli not found. Please install Redis tools."
        exit 1
    fi
    
    # Start backup process
    local start_time=$(date +%s)
    
    create_backup_dir
    
    # Backup databases
    if ! backup_mongodb; then
        send_notification "failed" "MongoDB backup failed"
        exit 1
    fi
    
    if ! backup_redis; then
        send_notification "failed" "Redis backup failed"
        exit 1
    fi
    
    # Backup application files
    backup_app_files
    
    # Create metadata
    create_metadata
    
    # Compress backup
    if ! compress_backup; then
        send_notification "failed" "Backup compression failed"
        exit 1
    fi
    
    # Verify backup
    if ! verify_backup; then
        send_notification "failed" "Backup verification failed"
        exit 1
    fi
    
    # Upload to S3
    upload_to_s3
    
    # Cleanup old backups
    cleanup_old_backups
    
    # Calculate duration
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    local backup_size=$(du -sh "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" | cut -f1)
    local success_message="Backup completed successfully in ${duration}s. Size: $backup_size"
    
    send_notification "success" "$success_message"
    
    log_success "Backup process completed!"
    log_info "Backup location: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
    log_info "Duration: ${duration} seconds"
}

# Handle script termination
cleanup() {
    log_warning "Backup process interrupted"
    
    # Remove incomplete backup
    if [ -d "${BACKUP_DIR}/${BACKUP_NAME}" ]; then
        rm -rf "${BACKUP_DIR}/${BACKUP_NAME}"
    fi
    
    if [ -f "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" ]; then
        rm -f "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
    fi
    
    exit 1
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Run main function
main "$@"
