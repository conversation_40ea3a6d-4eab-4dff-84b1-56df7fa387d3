'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  UserPlusIcon,
  ArrowLeftIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  IdentificationIcon,
  CalendarIcon,
  BriefcaseIcon,
  AcademicCapIcon,
  HeartIcon,
  ExclamationCircleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import type { CreateMemberForm, MembershipType, MembershipCategory } from '@/types/member';

export default function AddMemberPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState<CreateMemberForm>({
    personalInfo: {
      firstName: '',
      lastName: '',
      fullNameArabic: '',
      fullNameEnglish: '',
      dateOfBirth: '',
      gender: 'male',
      nationality: '',
      nationalId: '',
      passportNumber: '',
      maritalStatus: 'single',
      profession: '',
      employer: '',
      education: ''
    },
    contactInfo: {
      email: '',
      phone: '',
      alternativePhone: '',
      whatsapp: '',
      address: {
        street: '',
        city: '',
        state: '',
        postalCode: '',
        country: 'السعودية'
      },
      socialMedia: {
        twitter: '',
        linkedin: '',
        instagram: '',
        facebook: ''
      }
    },
    membershipInfo: {
      type: 'basic',
      category: 'individual',
      sponsoredBy: '',
      referredBy: ''
    },
    emergencyContact: {
      name: '',
      relationship: '',
      phone: '',
      email: '',
      address: ''
    }
  });

  const steps = [
    { id: 1, title: 'المعلومات الشخصية', icon: UserIcon },
    { id: 2, title: 'معلومات الاتصال', icon: PhoneIcon },
    { id: 3, title: 'معلومات العضوية', icon: IdentificationIcon },
    { id: 4, title: 'جهة الاتصال الطارئة', icon: HeartIcon }
  ];

  const membershipTypes: { value: MembershipType; label: string; price: string; benefits: string[] }[] = [
    {
      value: 'basic',
      label: 'عضوية أساسية',
      price: 'مجانية',
      benefits: ['الوصول للفعاليات العامة', 'النشرة الإخبارية', 'خصم 5% على الخدمات']
    },
    {
      value: 'premium',
      label: 'عضوية مميزة',
      price: '500 ريال/سنة',
      benefits: ['جميع مزايا العضوية الأساسية', 'دعوات للفعاليات الخاصة', 'خصم 15% على الخدمات', 'استشارات مجانية']
    },
    {
      value: 'vip',
      label: 'عضوية VIP',
      price: '1000 ريال/سنة',
      benefits: ['جميع المزايا السابقة', 'خدمة عملاء مخصصة', 'خصم 25% على الخدمات', 'أولوية في الحجوزات']
    },
    {
      value: 'student',
      label: 'عضوية طلابية',
      price: '200 ريال/سنة',
      benefits: ['خصم خاص للطلاب', 'ورش تدريبية مجانية', 'خصم 10% على الخدمات']
    }
  ];

  const handleInputChange = (section: keyof CreateMemberForm, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));

    // Clear error when user starts typing
    const errorKey = `${section}.${field}`;
    if (errors[errorKey]) {
      setErrors(prev => ({ ...prev, [errorKey]: '' }));
    }
  };

  const handleNestedInputChange = (section: keyof CreateMemberForm, nestedSection: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [nestedSection]: {
          ...(prev[section] as any)[nestedSection],
          [field]: value
        }
      }
    }));
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.personalInfo.firstName.trim()) {
          newErrors['personalInfo.firstName'] = 'الاسم الأول مطلوب';
        }
        if (!formData.personalInfo.lastName.trim()) {
          newErrors['personalInfo.lastName'] = 'اسم العائلة مطلوب';
        }
        if (!formData.personalInfo.fullNameArabic.trim()) {
          newErrors['personalInfo.fullNameArabic'] = 'الاسم الكامل بالعربية مطلوب';
        }
        if (!formData.personalInfo.dateOfBirth) {
          newErrors['personalInfo.dateOfBirth'] = 'تاريخ الميلاد مطلوب';
        }
        if (!formData.personalInfo.nationality.trim()) {
          newErrors['personalInfo.nationality'] = 'الجنسية مطلوبة';
        }
        if (!formData.personalInfo.nationalId.trim()) {
          newErrors['personalInfo.nationalId'] = 'رقم الهوية مطلوب';
        }
        break;

      case 2:
        if (!formData.contactInfo.email.trim()) {
          newErrors['contactInfo.email'] = 'البريد الإلكتروني مطلوب';
        } else if (!/\S+@\S+\.\S+/.test(formData.contactInfo.email)) {
          newErrors['contactInfo.email'] = 'البريد الإلكتروني غير صحيح';
        }
        if (!formData.contactInfo.phone.trim()) {
          newErrors['contactInfo.phone'] = 'رقم الهاتف مطلوب';
        }
        if (!formData.contactInfo.address.street.trim()) {
          newErrors['contactInfo.address.street'] = 'الشارع مطلوب';
        }
        if (!formData.contactInfo.address.city.trim()) {
          newErrors['contactInfo.address.city'] = 'المدينة مطلوبة';
        }
        break;

      case 4:
        if (!formData.emergencyContact.name.trim()) {
          newErrors['emergencyContact.name'] = 'اسم جهة الاتصال الطارئة مطلوب';
        }
        if (!formData.emergencyContact.relationship.trim()) {
          newErrors['emergencyContact.relationship'] = 'صلة القرابة مطلوبة';
        }
        if (!formData.emergencyContact.phone.trim()) {
          newErrors['emergencyContact.phone'] = 'رقم الهاتف مطلوب';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(4, prev + 1));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(1, prev - 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      return;
    }

    setIsLoading(true);
    try {
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // في التطبيق الحقيقي، سيتم إرسال البيانات للخادم
      console.log('Member data:', formData);

      router.push('/members?success=added');
    } catch (error) {
      setErrors({ general: 'حدث خطأ أثناء إضافة العضو. يرجى المحاولة مرة أخرى.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProtectedRoute requiredPermissions={['members', 'write']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">الجمعية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/members">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    العودة للأعضاء
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="flex items-center">
              <UserPlusIcon className="h-8 w-8 text-blue-600 ml-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">إضافة عضو جديد</h1>
                <p className="text-gray-600">املأ المعلومات المطلوبة لإضافة عضو جديد للجمعية</p>
              </div>
            </div>
          </motion.div>

          {/* Progress Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    currentStep >= step.id
                      ? 'bg-blue-600 border-blue-600 text-white'
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {currentStep > step.id ? (
                      <CheckCircleIcon className="h-6 w-6" />
                    ) : (
                      <step.icon className="h-5 w-5" />
                    )}
                  </div>
                  <div className="mr-3">
                    <p className={`text-sm font-medium ${
                      currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                    }`}>
                      الخطوة {step.id}
                    </p>
                    <p className={`text-xs ${
                      currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`flex-1 h-0.5 mx-4 ${
                      currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </motion.div>

          {/* Form Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            {/* General Error */}
            {errors.general && (
              <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
                <ExclamationCircleIcon className="h-5 w-5 text-red-500 ml-3" />
                <span className="text-red-700 text-sm">{errors.general}</span>
              </div>
            )}

            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الشخصية</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم الأول *
                    </label>
                    <input
                      type="text"
                      value={formData.personalInfo.firstName}
                      onChange={(e) => handleInputChange('personalInfo', 'firstName', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors['personalInfo.firstName'] ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="أدخل الاسم الأول"
                    />
                    {errors['personalInfo.firstName'] && (
                      <p className="mt-1 text-sm text-red-600">{errors['personalInfo.firstName']}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اسم العائلة *
                    </label>
                    <input
                      type="text"
                      value={formData.personalInfo.lastName}
                      onChange={(e) => handleInputChange('personalInfo', 'lastName', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors['personalInfo.lastName'] ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="أدخل اسم العائلة"
                    />
                    {errors['personalInfo.lastName'] && (
                      <p className="mt-1 text-sm text-red-600">{errors['personalInfo.lastName']}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم الكامل بالعربية *
                    </label>
                    <input
                      type="text"
                      value={formData.personalInfo.fullNameArabic}
                      onChange={(e) => handleInputChange('personalInfo', 'fullNameArabic', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors['personalInfo.fullNameArabic'] ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="أدخل الاسم الكامل بالعربية"
                    />
                    {errors['personalInfo.fullNameArabic'] && (
                      <p className="mt-1 text-sm text-red-600">{errors['personalInfo.fullNameArabic']}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم الكامل بالإنجليزية
                    </label>
                    <input
                      type="text"
                      value={formData.personalInfo.fullNameEnglish || ''}
                      onChange={(e) => handleInputChange('personalInfo', 'fullNameEnglish', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter full name in English"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تاريخ الميلاد *
                    </label>
                    <input
                      type="date"
                      value={formData.personalInfo.dateOfBirth}
                      onChange={(e) => handleInputChange('personalInfo', 'dateOfBirth', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors['personalInfo.dateOfBirth'] ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                    {errors['personalInfo.dateOfBirth'] && (
                      <p className="mt-1 text-sm text-red-600">{errors['personalInfo.dateOfBirth']}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الجنس *
                    </label>
                    <select
                      value={formData.personalInfo.gender}
                      onChange={(e) => handleInputChange('personalInfo', 'gender', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="male">ذكر</option>
                      <option value="female">أنثى</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الجنسية *
                    </label>
                    <input
                      type="text"
                      value={formData.personalInfo.nationality}
                      onChange={(e) => handleInputChange('personalInfo', 'nationality', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors['personalInfo.nationality'] ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="أدخل الجنسية"
                    />
                    {errors['personalInfo.nationality'] && (
                      <p className="mt-1 text-sm text-red-600">{errors['personalInfo.nationality']}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهوية الوطنية *
                    </label>
                    <input
                      type="text"
                      value={formData.personalInfo.nationalId}
                      onChange={(e) => handleInputChange('personalInfo', 'nationalId', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors['personalInfo.nationalId'] ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="أدخل رقم الهوية الوطنية"
                    />
                    {errors['personalInfo.nationalId'] && (
                      <p className="mt-1 text-sm text-red-600">{errors['personalInfo.nationalId']}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Placeholder for other steps */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات الاتصال</h3>
                <div className="text-center py-12">
                  <EnvelopeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">سيتم إضافة نموذج معلومات الاتصال هنا</p>
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات العضوية</h3>
                <div className="text-center py-12">
                  <IdentificationIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">سيتم إضافة نموذج معلومات العضوية هنا</p>
                </div>
              </div>
            )}

            {currentStep === 4 && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">جهة الاتصال الطارئة</h3>
                <div className="text-center py-12">
                  <HeartIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">سيتم إضافة نموذج جهة الاتصال الطارئة هنا</p>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6 border-t border-gray-200">
              <button
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                السابق
              </button>

              <div className="flex space-x-3 space-x-reverse">
                {currentStep < 4 ? (
                  <button
                    onClick={handleNext}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    التالي
                  </button>
                ) : (
                  <button
                    onClick={handleSubmit}
                    disabled={isLoading}
                    className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري الحفظ...
                      </>
                    ) : (
                      'حفظ العضو'
                    )}
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}