import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  UsersIcon, 
  DocumentTextIcon, 
  CreditCardIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const SimpleHomePage = () => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const features = [
    {
      icon: ChartBarIcon,
      title: 'إدارة مالية شاملة',
      description: 'تتبع مالي شامل والميزانية والتقارير مع رؤى فورية وعمليات آلية.',
      color: 'bg-blue-500'
    },
    {
      icon: UsersIcon,
      title: 'إدارة الأعضاء',
      description: 'قاعدة بيانات شاملة للأعضاء مع التسجيل والملفات الشخصية وأدوات التواصل.',
      color: 'bg-green-500'
    },
    {
      icon: DocumentTextIcon,
      title: 'إدارة الوثائق',
      description: 'تخزين آمن للوثائق وتحكم في الإصدارات والتوقيعات الرقمية.',
      color: 'bg-purple-500'
    },
    {
      icon: CreditCardIcon,
      title: 'معالجة المدفوعات',
      description: 'بوابة دفع متكاملة تدعم طرق دفع متعددة مع فوترة آلية.',
      color: 'bg-orange-500'
    },
    {
      icon: ShieldCheckIcon,
      title: 'أمان المؤسسات',
      description: 'أمان بمستوى البنوك مع التشفير ومسارات التدقيق والتحكم في الوصول.',
      color: 'bg-red-500'
    },
    {
      icon: GlobeAltIcon,
      title: 'دعم متعدد اللغات',
      description: 'دعم كامل للعربية والإنجليزية مع تخطيط من اليمين إلى اليسار.',
      color: 'bg-indigo-500'
    }
  ];

  const stats = [
    { label: 'الأعضاء النشطون', value: '2,500+' },
    { label: 'المعاملات المعالجة', value: '50,000+' },
    { label: 'الوثائق المدارة', value: '10,000+' },
    { label: 'وقت تشغيل النظام', value: '99.9%' }
  ];

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <Head>
        <title>الجمعية - منصة إدارة الجمعيات الشاملة</title>
        <meta name="description" content="منصة شاملة لإدارة الجمعيات مع التتبع المالي وإدارة الأعضاء ومعالجة الوثائق" />
      </Head>

      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">ج</span>
                </div>
              </div>
              <div className="mr-3">
                <span className="text-xl font-bold text-gray-900">الجمعية</span>
              </div>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              <Link href="/dashboard">
                <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                  لوحة التحكم
                </button>
              </Link>
            </div>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              منصة إدارة الجمعيات الشاملة
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
              قم بتبسيط عملياتك المالية وإدارة الأعضاء والمهام الإدارية باستخدام منصتنا القوية والآمنة
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard">
                <button className="bg-white text-blue-900 hover:bg-blue-50 px-8 py-3 rounded-lg font-semibold flex items-center justify-center">
                  ابدأ الآن
                  <ArrowRightIcon className="mr-2 h-5 w-5" />
                </button>
              </Link>
              <button className="border-2 border-white text-white hover:bg-white hover:text-blue-900 px-8 py-3 rounded-lg font-semibold transition-colors">
                شاهد العرض التوضيحي
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-blue-900 mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              كل ما تحتاجه لإدارة جمعيتك
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              توفر منصتنا الشاملة جميع الأدوات التي تحتاجها لإدارة عمليات جمعيتك بكفاءة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow duration-300"
              >
                <div className={`w-12 h-12 ${feature.color} rounded-lg flex items-center justify-center mb-4`}>
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                حوّل إدارة جمعيتك
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                انضم إلى آلاف الجمعيات التي قامت بتبسيط عملياتها وتحسين رضا الأعضاء
              </p>
              
              <div className="space-y-4">
                {[
                  'عمليات مالية آلية وتقارير',
                  'لوحات معلومات وتحليلات فورية',
                  'معالجة دفع آمنة وحماية البيانات',
                  'تصميم متجاوب للوصول من أي مكان',
                  'ميزات امتثال وتدقيق مدمجة'
                ].map((benefit, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircleIcon className="h-6 w-6 text-green-500 ml-3 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-white rounded-lg shadow-xl p-8">
                <div className="text-center">
                  <div className="text-4xl font-bold text-blue-900 mb-2">
                    تحسن الكفاءة بنسبة 80%
                  </div>
                  <div className="text-gray-600 mb-6">
                    متوسط توفير الوقت الذي أبلغ عنه عملاؤنا
                  </div>
                  <Link href="/dashboard">
                    <button className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">
                      ابدأ تجربتك المجانية
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">ج</span>
              </div>
              <span className="mr-3 text-xl font-bold">الجمعية</span>
            </div>
            <p className="text-gray-300 mb-4">
              منصة إدارة الجمعيات الرائدة الموثوقة من قبل آلاف المنظمات حول العالم
            </p>
            <p className="text-gray-500">
              © {new Date().getFullYear()} الجمعية. جميع الحقوق محفوظة.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SimpleHomePage;
