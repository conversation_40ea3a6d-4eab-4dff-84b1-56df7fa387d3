import axios from 'axios';
import { logger } from '@/utils/logger';

// SMS configuration interface
interface SMSConfig {
  to: string | string[];
  message: string;
  sender?: string;
  priority?: 'low' | 'normal' | 'high';
}

// SMS templates in Arabic
const smsTemplates = {
  emailVerification: (data: any) => 
    `مرحباً ${data.name}، رمز التحقق الخاص بك هو: ${data.code}. صالح لمدة 10 دقائق.`,

  passwordReset: (data: any) => 
    `رمز إعادة تعيين كلمة المرور: ${data.code}. لا تشارك هذا الرمز مع أحد. صالح لمدة 10 دقائق.`,

  paymentReminder: (data: any) => 
    `تذكير: دفعة جمعية ${data.jamiyaName} بمبلغ ${data.amount} ريال مستحقة في ${data.dueDate}. ادفع الآن لتجنب الرسوم الإضافية.`,

  paymentConfirmation: (data: any) => 
    `تم استلام دفعتك بنجاح! المبلغ: ${data.amount} ريال للجمعية ${data.jamiyaName}. رقم العملية: ${data.transactionId}`,

  jamiyaInvitation: (data: any) => 
    `تمت دعوتك للانضمام إلى جمعية ${data.jamiyaName}. رمز الدعوة: ${data.inviteCode}. انضم الآن عبر التطبيق.`,

  accountLocked: (data: any) => 
    `تم قفل حسابك مؤقتاً بسبب محاولات دخول متعددة فاشلة. سيتم إلغاء القفل تلقائياً خلال ${data.lockDuration} دقيقة.`,

  twoFactorAuth: (data: any) => 
    `رمز التحقق بخطوتين: ${data.code}. لا تشارك هذا الرمز مع أحد. صالح لمدة 5 دقائق.`,

  jamiyaStarted: (data: any) => 
    `بدأت جمعية ${data.jamiyaName}! أول دفعة مستحقة في ${data.firstPaymentDate}. مبلغ الدفعة: ${data.monthlyAmount} ريال.`,

  jamiyaCompleted: (data: any) => 
    `تهانينا! اكتملت جمعية ${data.jamiyaName} بنجاح. إجمالي المبلغ المحصل: ${data.totalAmount} ريال.`,

  latePaymentWarning: (data: any) => 
    `تحذير: دفعة جمعية ${data.jamiyaName} متأخرة ${data.daysLate} أيام. ادفع الآن لتجنب رسوم التأخير.`,
};

// SMS service class
class SMSService {
  private apiUrl: string;
  private apiKey: string;
  private defaultSender: string;

  constructor() {
    this.apiUrl = process.env.SMS_PROVIDER_URL || '';
    this.apiKey = process.env.SMS_PROVIDER_KEY || '';
    this.defaultSender = process.env.SMS_PROVIDER_SENDER || 'ALJAMEIA';
  }

  // Send single SMS
  async sendSMS(config: SMSConfig): Promise<void> {
    try {
      if (!this.apiUrl || !this.apiKey) {
        throw new Error('SMS service not configured');
      }

      const recipients = Array.isArray(config.to) ? config.to : [config.to];
      
      // Validate phone numbers
      const validRecipients = recipients.filter(phone => this.validatePhoneNumber(phone));
      
      if (validRecipients.length === 0) {
        throw new Error('No valid phone numbers provided');
      }

      // Prepare SMS data
      const smsData = {
        sender: config.sender || this.defaultSender,
        recipients: validRecipients,
        message: config.message,
        priority: config.priority || 'normal',
        unicode: true, // Support Arabic text
      };

      // Send SMS via API
      const response = await axios.post(this.apiUrl, smsData, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });

      if (response.data.success) {
        logger.info('SMS sent successfully', {
          recipients: validRecipients,
          messageLength: config.message.length,
          messageId: response.data.messageId,
        });
      } else {
        throw new Error(response.data.message || 'SMS sending failed');
      }

    } catch (error) {
      logger.error('Failed to send SMS', {
        recipients: config.to,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  // Send SMS using template
  async sendTemplateSMS(
    template: keyof typeof smsTemplates,
    to: string | string[],
    data: any
  ): Promise<void> {
    const templateFunction = smsTemplates[template];
    if (!templateFunction) {
      throw new Error(`SMS template '${template}' not found`);
    }

    const message = templateFunction(data);
    
    await this.sendSMS({
      to,
      message,
      priority: this.getTemplatePriority(template),
    });
  }

  // Send bulk SMS
  async sendBulkSMS(messages: SMSConfig[]): Promise<void> {
    const results = await Promise.allSettled(
      messages.map(sms => this.sendSMS(sms))
    );

    const failed = results.filter(result => result.status === 'rejected');
    
    if (failed.length > 0) {
      logger.warn(`${failed.length} out of ${messages.length} SMS messages failed to send`);
    }

    logger.info(`Bulk SMS completed: ${messages.length - failed.length} sent, ${failed.length} failed`);
  }

  // Validate phone number (Saudi format)
  private validatePhoneNumber(phone: string): boolean {
    // Remove any non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');
    
    // Check Saudi phone number formats
    const saudiPatterns = [
      /^966[5][0-9]{8}$/, // +966 5XXXXXXXX
      /^05[0-9]{8}$/,     // 05XXXXXXXX
      /^5[0-9]{8}$/,      // 5XXXXXXXX
    ];

    return saudiPatterns.some(pattern => pattern.test(cleanPhone));
  }

  // Normalize phone number to international format
  private normalizePhoneNumber(phone: string): string {
    const cleanPhone = phone.replace(/\D/g, '');
    
    if (cleanPhone.startsWith('966')) {
      return `+${cleanPhone}`;
    } else if (cleanPhone.startsWith('05')) {
      return `+966${cleanPhone.substring(1)}`;
    } else if (cleanPhone.startsWith('5') && cleanPhone.length === 9) {
      return `+966${cleanPhone}`;
    }
    
    return phone; // Return original if can't normalize
  }

  // Get template priority
  private getTemplatePriority(template: keyof typeof smsTemplates): 'low' | 'normal' | 'high' {
    const highPriorityTemplates = ['twoFactorAuth', 'accountLocked', 'passwordReset'];
    const normalPriorityTemplates = ['paymentReminder', 'latePaymentWarning'];
    
    if (highPriorityTemplates.includes(template)) {
      return 'high';
    } else if (normalPriorityTemplates.includes(template)) {
      return 'normal';
    }
    
    return 'low';
  }

  // Check SMS service status
  async checkServiceStatus(): Promise<boolean> {
    try {
      if (!this.apiUrl || !this.apiKey) {
        return false;
      }

      const response = await axios.get(`${this.apiUrl}/status`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        timeout: 10000,
      });

      return response.data.status === 'active';
    } catch (error) {
      logger.error('SMS service status check failed:', error);
      return false;
    }
  }

  // Get SMS balance/credits
  async getBalance(): Promise<number> {
    try {
      if (!this.apiUrl || !this.apiKey) {
        throw new Error('SMS service not configured');
      }

      const response = await axios.get(`${this.apiUrl}/balance`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        timeout: 10000,
      });

      return response.data.balance || 0;
    } catch (error) {
      logger.error('Failed to get SMS balance:', error);
      return 0;
    }
  }

  // Get SMS delivery report
  async getDeliveryReport(messageId: string): Promise<any> {
    try {
      if (!this.apiUrl || !this.apiKey) {
        throw new Error('SMS service not configured');
      }

      const response = await axios.get(`${this.apiUrl}/reports/${messageId}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        timeout: 10000,
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to get SMS delivery report:', error);
      throw error;
    }
  }
}

// Create SMS service instance
const smsService = new SMSService();

// Export convenience functions
export const sendSMS = (config: SMSConfig) => smsService.sendSMS(config);

export const sendTemplateSMS = (
  template: keyof typeof smsTemplates,
  to: string | string[],
  data: any
) => smsService.sendTemplateSMS(template, to, data);

export const sendBulkSMS = (messages: SMSConfig[]) => smsService.sendBulkSMS(messages);

export const checkSMSServiceStatus = () => smsService.checkServiceStatus();

export const getSMSBalance = () => smsService.getBalance();

export const getSMSDeliveryReport = (messageId: string) => smsService.getDeliveryReport(messageId);

// SMS notification helpers
export const smsHelpers = {
  // Send verification code
  sendVerificationCode: async (phone: string, name: string, code: string) => {
    await sendTemplateSMS('emailVerification', phone, { name, code });
  },

  // Send password reset code
  sendPasswordResetCode: async (phone: string, code: string) => {
    await sendTemplateSMS('passwordReset', phone, { code });
  },

  // Send payment reminder
  sendPaymentReminder: async (phone: string, jamiyaName: string, amount: number, dueDate: string) => {
    await sendTemplateSMS('paymentReminder', phone, { jamiyaName, amount, dueDate });
  },

  // Send payment confirmation
  sendPaymentConfirmation: async (
    phone: string,
    amount: number,
    jamiyaName: string,
    transactionId: string
  ) => {
    await sendTemplateSMS('paymentConfirmation', phone, {
      amount,
      jamiyaName,
      transactionId,
    });
  },

  // Send jamiya invitation
  sendJamiyaInvitation: async (phone: string, jamiyaName: string, inviteCode: string) => {
    await sendTemplateSMS('jamiyaInvitation', phone, { jamiyaName, inviteCode });
  },

  // Send account locked notification
  sendAccountLocked: async (phone: string, lockDuration: number) => {
    await sendTemplateSMS('accountLocked', phone, { lockDuration });
  },

  // Send 2FA code
  send2FACode: async (phone: string, code: string) => {
    await sendTemplateSMS('twoFactorAuth', phone, { code });
  },
};

export default smsService;
