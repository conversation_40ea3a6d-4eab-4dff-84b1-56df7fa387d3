apply plugin: 'com.android.application'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'kotlin-kapt'
apply plugin: 'dagger.hilt.android.plugin'
apply plugin: 'kotlin-parcelize'
apply plugin: 'androidx.navigation.safeargs.kotlin'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.firebase-perf'
apply plugin: 'com.google.android.libraries.mapsplatform.secrets-gradle-plugin'

configurations.all {
    resolutionStrategy {
        force 'org.bouncycastle:bcprov-jdk15to18:1.72'
        force 'org.bouncycastle:bcpkix-jdk15to18:1.72'
    }
    exclude group: 'org.bouncycastle', module: 'bcprov-jdk15on'
    exclude group: 'org.bouncycastle', module: 'bcpkix-jdk15on'
}

android {
    namespace 'com.aljameia.app'
    compileSdk 34

    defaultConfig {
        applicationId "com.aljameia.app"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }

        // Room schema export
        kapt {
            arguments {
                arg("room.schemaLocation", "$projectDir/schemas")
            }
        }

        // Build config fields
        buildConfigField "String", "API_BASE_URL", "\"https://api.aljameia.com/v1/\""
        buildConfigField "String", "WS_BASE_URL", "\"wss://api.aljameia.com/ws/\""
        buildConfigField "boolean", "ENABLE_LOGGING", "true"
        buildConfigField "boolean", "ENABLE_ANALYTICS", "true"
        buildConfigField "boolean", "ENABLE_CRASHLYTICS", "true"

        // Manifest placeholders
        manifestPlaceholders = [
            appName: "@string/app_name",
            appIcon: "@mipmap/ic_launcher"
        ]

        // Proguard files
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

        // NDK
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86_64'
        }
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }

        release {
            storeFile file('release.keystore')
            storePassword System.getenv("KEYSTORE_PASSWORD")
            keyAlias System.getenv("KEY_ALIAS")
            keyPassword System.getenv("KEY_PASSWORD")
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            shrinkResources false
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
            
            buildConfigField "String", "API_BASE_URL", "\"https://api-dev.aljameia.com/v1/\""
            buildConfigField "boolean", "ENABLE_LOGGING", "true"
            buildConfigField "boolean", "ENABLE_ANALYTICS", "false"
            buildConfigField "boolean", "ENABLE_CRASHLYTICS", "false"
            
            manifestPlaceholders = [
                appName: "@string/app_name_debug",
                appIcon: "@mipmap/ic_launcher_debug"
            ]
        }

        staging {
            debuggable false
            minifyEnabled true
            shrinkResources true
            applicationIdSuffix ".staging"
            versionNameSuffix "-staging"
            
            buildConfigField "String", "API_BASE_URL", "\"https://api-staging.aljameia.com/v1/\""
            buildConfigField "boolean", "ENABLE_LOGGING", "true"
            buildConfigField "boolean", "ENABLE_ANALYTICS", "true"
            buildConfigField "boolean", "ENABLE_CRASHLYTICS", "true"
            
            manifestPlaceholders = [
                appName: "@string/app_name_staging",
                appIcon: "@mipmap/ic_launcher_staging"
            ]
            
            signingConfig signingConfigs.release
        }

        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            
            buildConfigField "String", "API_BASE_URL", "\"https://api.aljameia.com/v1/\""
            buildConfigField "boolean", "ENABLE_LOGGING", "false"
            buildConfigField "boolean", "ENABLE_ANALYTICS", "true"
            buildConfigField "boolean", "ENABLE_CRASHLYTICS", "true"
            
            manifestPlaceholders = [
                appName: "@string/app_name",
                appIcon: "@mipmap/ic_launcher"
            ]
            
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = '1.8'
        freeCompilerArgs += [
            "-opt-in=kotlin.RequiresOptIn",
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api"
        ]
    }

    buildFeatures {
        buildConfig true
        viewBinding true
        dataBinding true
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion '1.5.4'
    }

    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
            excludes += '/META-INF/DEPENDENCIES'
            excludes += '/META-INF/LICENSE'
            excludes += '/META-INF/LICENSE.txt'
            excludes += '/META-INF/NOTICE'
            excludes += '/META-INF/NOTICE.txt'
        }
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
        animationsDisabled = true
    }

    lint {
        checkReleaseBuilds false
        abortOnError false
        disable 'MissingTranslation'
        disable 'ExtraTranslation'
    }

    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }
}

dependencies {
    // Core Android
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // Compose BOM
    implementation platform('androidx.compose:compose-bom:2023.10.01')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.material:material-icons-extended'
    implementation 'androidx.activity:activity-compose:1.8.2'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'
    implementation 'androidx.navigation:navigation-compose:2.7.6'

    // Architecture Components
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.6'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.6'
    implementation 'androidx.work:work-runtime-ktx:2.9.0'

    // Room Database
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'

    // Dependency Injection - Hilt
    implementation 'com.google.dagger:hilt-android:2.48.1'
    kapt 'com.google.dagger:hilt-compiler:2.48.1'
    implementation 'androidx.hilt:hilt-work:1.1.0'
    kapt 'androidx.hilt:hilt-compiler:1.1.0'
    implementation 'androidx.hilt:hilt-navigation-compose:1.1.0'

    // Networking
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'com.jakewharton.retrofit:retrofit2-kotlin-coroutines-adapter:0.9.2'

    // Image Loading
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    kapt 'com.github.bumptech.glide:compiler:4.16.0'
    implementation 'io.coil-kt:coil:2.5.0'
    implementation 'io.coil-kt:coil-compose:2.5.0'

    // JSON
    implementation 'com.google.code.gson:gson:2.10.1'

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3'

    // Security
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'
    implementation 'androidx.biometric:biometric:1.1.0'

    // Firebase
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-perf-ktx'
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation 'com.google.firebase:firebase-config-ktx'
    implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.google.firebase:firebase-firestore-ktx'
    implementation 'com.google.firebase:firebase-storage-ktx'

    // Google Play Services
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
    implementation 'com.google.android.gms:play-services-maps:18.2.0'
    implementation 'com.google.android.gms:play-services-location:21.0.1'

    // Charts
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

    // QR Code
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    implementation 'com.google.zxing:core:3.5.2'

    // PDF
    implementation 'com.itextpdf:itext7-core:7.2.5'
    implementation('com.tom-roush:pdfbox-android:2.0.27.0') {
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15on'
        exclude group: 'org.bouncycastle', module: 'bcpkix-jdk15on'
    }

    // Date and Time
    implementation 'com.jakewharton.threetenabp:threetenabp:1.4.6'

    // Permissions
    implementation 'com.karumi:dexter:6.2.3'

    // Logging
    implementation 'com.jakewharton.timber:timber:5.0.1'

    // Leak Detection (Debug only)
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.12'

    // Desugaring
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.7.0'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.1'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
    testImplementation 'com.google.truth:truth:1.1.4'
    testImplementation 'androidx.room:room-testing:2.6.1'
    testImplementation 'com.squareup.okhttp3:mockwebserver:4.12.0'

    // Android Testing
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-contrib:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-intents:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    androidTestImplementation 'androidx.navigation:navigation-testing:2.7.6'
    androidTestImplementation 'com.google.dagger:hilt-android-testing:2.48.1'
    kaptAndroidTest 'com.google.dagger:hilt-compiler:2.48.1'

    // Compose Testing
    androidTestImplementation platform('androidx.compose:compose-bom:2023.10.01')
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
}
