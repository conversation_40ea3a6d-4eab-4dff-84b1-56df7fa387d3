import { Router, Request, Response } from 'express';
import { checkDatabaseHealth, getDatabaseStats } from '@/config/database';
import { CacheManager } from '@/config/redis';
import { logger } from '@/utils/logger';
import mongoose from 'mongoose';

const router = Router();

// Basic health check
router.get('/', async (req: Request, res: Response) => {
  try {
    const healthCheck = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      services: {
        api: 'healthy',
        database: 'checking',
        cache: 'checking',
      },
    };

    // Check database health
    try {
      const dbHealthy = await checkDatabaseHealth();
      healthCheck.services.database = dbHealthy ? 'healthy' : 'unhealthy';
    } catch (error) {
      healthCheck.services.database = 'unhealthy';
      logger.error('Database health check failed:', error);
    }

    // Check Redis health
    try {
      const cacheHealthy = await CacheManager.ping();
      healthCheck.services.cache = cacheHealthy ? 'healthy' : 'unhealthy';
    } catch (error) {
      healthCheck.services.cache = 'unhealthy';
      logger.error('Cache health check failed:', error);
    }

    // Determine overall status
    const allServicesHealthy = Object.values(healthCheck.services).every(
      status => status === 'healthy'
    );

    if (!allServicesHealthy) {
      healthCheck.status = 'DEGRADED';
      res.status(503);
    }

    res.json(healthCheck);
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      message: 'Health check failed',
    });
  }
});

// Detailed health check
router.get('/detailed', async (req: Request, res: Response) => {
  try {
    const detailedHealth = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      system: {
        memory: {
          used: process.memoryUsage().heapUsed,
          total: process.memoryUsage().heapTotal,
          external: process.memoryUsage().external,
          rss: process.memoryUsage().rss,
        },
        cpu: {
          usage: process.cpuUsage(),
        },
        platform: process.platform,
        nodeVersion: process.version,
      },
      services: {
        api: {
          status: 'healthy',
          responseTime: 0,
        },
        database: {
          status: 'checking',
          connectionState: mongoose.connection.readyState,
          responseTime: 0,
        },
        cache: {
          status: 'checking',
          responseTime: 0,
        },
      },
      metrics: {
        requests: {
          total: 0,
          errors: 0,
          averageResponseTime: 0,
        },
        database: {
          connections: 0,
          queries: 0,
        },
      },
    };

    // Check database health with timing
    const dbStart = Date.now();
    try {
      const dbHealthy = await checkDatabaseHealth();
      const dbResponseTime = Date.now() - dbStart;
      
      detailedHealth.services.database = {
        status: dbHealthy ? 'healthy' : 'unhealthy',
        connectionState: mongoose.connection.readyState,
        responseTime: dbResponseTime,
      };

      // Get database stats if healthy
      if (dbHealthy) {
        try {
          const dbStats = await getDatabaseStats();
          (detailedHealth.services.database as any).stats = dbStats;
        } catch (error) {
          logger.error('Failed to get database stats:', error);
        }
      }
    } catch (error) {
      detailedHealth.services.database.status = 'unhealthy';
      logger.error('Database health check failed:', error);
    }

    // Check Redis health with timing
    const cacheStart = Date.now();
    try {
      const cacheHealthy = await CacheManager.ping();
      const cacheResponseTime = Date.now() - cacheStart;
      
      detailedHealth.services.cache = {
        status: cacheHealthy ? 'healthy' : 'unhealthy',
        responseTime: cacheResponseTime,
      };

      // Get cache info if healthy
      if (cacheHealthy) {
        try {
          const cacheInfo = await CacheManager.info();
          (detailedHealth.services.cache as any).info = cacheInfo;
        } catch (error) {
          logger.error('Failed to get cache info:', error);
        }
      }
    } catch (error) {
      detailedHealth.services.cache.status = 'unhealthy';
      logger.error('Cache health check failed:', error);
    }

    // Determine overall status
    const allServicesHealthy = Object.values(detailedHealth.services).every(
      service => service.status === 'healthy'
    );

    if (!allServicesHealthy) {
      detailedHealth.status = 'DEGRADED';
      res.status(503);
    }

    res.json(detailedHealth);
  } catch (error) {
    logger.error('Detailed health check failed:', error);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      message: 'Detailed health check failed',
    });
  }
});

// Readiness probe (for Kubernetes)
router.get('/ready', async (req: Request, res: Response) => {
  try {
    // Check if all critical services are ready
    const dbHealthy = await checkDatabaseHealth();
    const cacheHealthy = await CacheManager.ping();

    if (dbHealthy && cacheHealthy) {
      res.status(200).json({
        status: 'READY',
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(503).json({
        status: 'NOT_READY',
        timestamp: new Date().toISOString(),
        services: {
          database: dbHealthy ? 'ready' : 'not_ready',
          cache: cacheHealthy ? 'ready' : 'not_ready',
        },
      });
    }
  } catch (error) {
    logger.error('Readiness check failed:', error);
    res.status(503).json({
      status: 'NOT_READY',
      timestamp: new Date().toISOString(),
      error: 'Readiness check failed',
    });
  }
});

// Liveness probe (for Kubernetes)
router.get('/live', (req: Request, res: Response) => {
  // Simple liveness check - if the process is running, it's alive
  res.status(200).json({
    status: 'ALIVE',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// Database-specific health check
router.get('/database', async (req: Request, res: Response) => {
  try {
    const dbHealthy = await checkDatabaseHealth();
    
    if (dbHealthy) {
      const stats = await getDatabaseStats();
      res.json({
        status: 'healthy',
        connectionState: mongoose.connection.readyState,
        stats,
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(503).json({
        status: 'unhealthy',
        connectionState: mongoose.connection.readyState,
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    logger.error('Database health check failed:', error);
    res.status(503).json({
      status: 'error',
      message: 'Database health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

// Cache-specific health check
router.get('/cache', async (req: Request, res: Response) => {
  try {
    const cacheHealthy = await CacheManager.ping();
    
    if (cacheHealthy) {
      const info = await CacheManager.info();
      res.json({
        status: 'healthy',
        info,
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    logger.error('Cache health check failed:', error);
    res.status(503).json({
      status: 'error',
      message: 'Cache health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

// System metrics
router.get('/metrics', (req: Request, res: Response) => {
  try {
    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      platform: process.platform,
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development',
      pid: process.pid,
    };

    res.json(metrics);
  } catch (error) {
    logger.error('Metrics collection failed:', error);
    res.status(500).json({
      status: 'error',
      message: 'Metrics collection failed',
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
