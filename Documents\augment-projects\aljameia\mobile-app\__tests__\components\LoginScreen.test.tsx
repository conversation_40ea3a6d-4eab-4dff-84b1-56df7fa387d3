import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import LoginScreen from '../../app/auth/login';
import { useAuth } from '../../contexts/AuthContext';

// Mock dependencies
jest.mock('../../contexts/AuthContext');
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
}));

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockAlert = Alert.alert as jest.MockedFunction<typeof Alert.alert>;

describe('LoginScreen', () => {
  const mockLogin = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: false,
      isAuthenticated: false,
      isBiometricEnabled: false,
      login: mockLogin,
      loginWithBiometric: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      refreshUser: jest.fn(),
      updateProfile: jest.fn(),
      enableBiometric: jest.fn(),
      disableBiometric: jest.fn(),
    });
  });

  it('should render login form correctly', () => {
    const { getByText, getByPlaceholderText } = render(<LoginScreen />);

    expect(getByText('الجمعية')).toBeTruthy();
    expect(getByText('منصة إدارة الجمعيات المالية')).toBeTruthy();
    expect(getByText('تسجيل الدخول')).toBeTruthy();
    expect(getByPlaceholderText('أدخل بريدك الإلكتروني')).toBeTruthy();
    expect(getByPlaceholderText('أدخل كلمة المرور')).toBeTruthy();
  });

  it('should show validation errors for empty fields', async () => {
    const { getByText } = render(<LoginScreen />);
    
    const loginButton = getByText('تسجيل الدخول');
    fireEvent.press(loginButton);

    await waitFor(() => {
      expect(getByText('البريد الإلكتروني مطلوب')).toBeTruthy();
      expect(getByText('كلمة المرور مطلوبة')).toBeTruthy();
    });
  });

  it('should show validation error for invalid email', async () => {
    const { getByPlaceholderText, getByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('أدخل بريدك الإلكتروني');
    const loginButton = getByText('تسجيل الدخول');

    fireEvent.changeText(emailInput, 'invalid-email');
    fireEvent.press(loginButton);

    await waitFor(() => {
      expect(getByText('البريد الإلكتروني غير صحيح')).toBeTruthy();
    });
  });

  it('should show validation error for short password', async () => {
    const { getByPlaceholderText, getByText } = render(<LoginScreen />);
    
    const passwordInput = getByPlaceholderText('أدخل كلمة المرور');
    const loginButton = getByText('تسجيل الدخول');

    fireEvent.changeText(passwordInput, '123');
    fireEvent.press(loginButton);

    await waitFor(() => {
      expect(getByText('كلمة المرور يجب أن تكون 6 أحرف على الأقل')).toBeTruthy();
    });
  });

  it('should call login with valid credentials', async () => {
    const { getByPlaceholderText, getByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('أدخل بريدك الإلكتروني');
    const passwordInput = getByPlaceholderText('أدخل كلمة المرور');
    const loginButton = getByText('تسجيل الدخول');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(loginButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });
  });

  it('should toggle password visibility', () => {
    const { getByPlaceholderText, getByTestId } = render(<LoginScreen />);
    
    const passwordInput = getByPlaceholderText('أدخل كلمة المرور');
    
    // Initially password should be hidden
    expect(passwordInput.props.secureTextEntry).toBe(true);
    
    // Toggle password visibility
    const toggleButton = getByTestId('password-toggle');
    fireEvent.press(toggleButton);
    
    expect(passwordInput.props.secureTextEntry).toBe(false);
  });

  it('should disable form when loading', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: true,
      isAuthenticated: false,
      isBiometricEnabled: false,
      login: mockLogin,
      loginWithBiometric: jest.fn(),
      register: jest.fn(),
      logout: jest.fn(),
      refreshUser: jest.fn(),
      updateProfile: jest.fn(),
      enableBiometric: jest.fn(),
      disableBiometric: jest.fn(),
    });

    const { getByPlaceholderText, getByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('أدخل بريدك الإلكتروني');
    const passwordInput = getByPlaceholderText('أدخل كلمة المرور');
    const loginButton = getByText('جاري تسجيل الدخول...');

    expect(emailInput.props.editable).toBe(false);
    expect(passwordInput.props.editable).toBe(false);
    expect(loginButton.props.disabled).toBe(true);
  });

  it('should clear error when user starts typing', async () => {
    const { getByPlaceholderText, getByText, queryByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('أدخل بريدك الإلكتروني');
    const loginButton = getByText('تسجيل الدخول');

    // Trigger validation error
    fireEvent.press(loginButton);
    
    await waitFor(() => {
      expect(getByText('البريد الإلكتروني مطلوب')).toBeTruthy();
    });

    // Start typing to clear error
    fireEvent.changeText(emailInput, 'test');
    
    await waitFor(() => {
      expect(queryByText('البريد الإلكتروني مطلوب')).toBeNull();
    });
  });

  it('should handle login error gracefully', async () => {
    mockLogin.mockRejectedValue(new Error('Login failed'));

    const { getByPlaceholderText, getByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('أدخل بريدك الإلكتروني');
    const passwordInput = getByPlaceholderText('أدخل كلمة المرور');
    const loginButton = getByText('تسجيل الدخول');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(loginButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalled();
    });

    // Error should be handled by the auth context, not the component
    expect(mockAlert).not.toHaveBeenCalled();
  });

  it('should show forgot password link', () => {
    const { getByText } = render(<LoginScreen />);
    
    expect(getByText('نسيت كلمة المرور؟')).toBeTruthy();
  });

  it('should show social login options', () => {
    const { getByText } = render(<LoginScreen />);
    
    expect(getByText('تسجيل الدخول بـ Google')).toBeTruthy();
    expect(getByText('تسجيل الدخول بـ Apple')).toBeTruthy();
  });

  it('should show register link', () => {
    const { getByText } = render(<LoginScreen />);
    
    expect(getByText('ليس لديك حساب؟')).toBeTruthy();
    expect(getByText('إنشاء حساب جديد')).toBeTruthy();
  });
});
