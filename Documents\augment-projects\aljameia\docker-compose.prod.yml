version: '3.8'

# Production overrides for docker-compose.yml
services:
  # MongoDB Production Configuration
  mongodb:
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE}
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - mongodb_logs:/var/log/mongodb
      - ./backups:/data/backups
    command: mongod --auth --bind_ip_all --logpath /var/log/mongodb/mongod.log
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Redis Production Configuration
  redis:
    restart: always
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD} --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - redis_logs:/var/log/redis
      - ./backend/config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Backend Production Configuration
  backend:
    image: ${DOCKER_REGISTRY:-aljameia}/aljameia-backend:${VERSION:-latest}
    restart: always
    environment:
      NODE_ENV: production
      PORT: 3000
      MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME}:${MONGO_ROOT_PASSWORD}@mongodb:27017/${MONGO_DATABASE}?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      
      # Security
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      
      # URLs
      API_BASE_URL: ${API_BASE_URL}
      FRONTEND_URL: ${FRONTEND_URL}
      
      # Payment Gateway
      PAYMENT_GATEWAY_URL: ${PAYMENT_GATEWAY_URL}
      PAYMENT_GATEWAY_KEY: ${PAYMENT_GATEWAY_KEY}
      PAYMENT_GATEWAY_SECRET: ${PAYMENT_GATEWAY_SECRET}
      
      # Email
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      
      # Push Notifications
      FCM_SERVER_KEY: ${FCM_SERVER_KEY}
      EXPO_ACCESS_TOKEN: ${EXPO_ACCESS_TOKEN}
      
      # File Storage
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
      
      # Monitoring
      SENTRY_DSN: ${SENTRY_DSN}
      
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Admin Dashboard Production Configuration
  admin-dashboard:
    image: ${DOCKER_REGISTRY:-aljameia}/aljameia-admin:${VERSION:-latest}
    restart: always
    environment:
      NODE_ENV: production
      REACT_APP_API_URL: ${API_BASE_URL}
      REACT_APP_WS_URL: ${WS_URL}
      GENERATE_SOURCEMAP: false
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Nginx Production Configuration
  nginx:
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
      - backend_uploads:/var/www/uploads:ro
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # Monitoring Services (Production)
  prometheus:
    image: prom/prometheus:latest
    restart: always
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  grafana:
    image: grafana/grafana:latest
    restart: always
    ports:
      - "3002:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_SECURITY_DISABLE_GRAVATAR: true
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_ANALYTICS_CHECK_FOR_UPDATES: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

  # Log Management (Production)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    restart: always
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
      - xpack.monitoring.collection.enabled=true
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    restart: always
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - ./logging/patterns:/usr/share/logstash/patterns:ro
      - backend_logs:/var/log/backend:ro
      - nginx_logs:/var/log/nginx:ro
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    restart: always
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
      SERVER_NAME: kibana.aljameia.com
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Backup Service
  backup:
    image: alpine:latest
    restart: "no"
    volumes:
      - mongodb_data:/data/mongodb:ro
      - redis_data:/data/redis:ro
      - backend_uploads:/data/uploads:ro
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    command: /backup.sh
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  mongodb_logs:
    driver: local
  redis_data:
    driver: local
  redis_logs:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
