import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { httpClient } from './api';

// Notification Types
export interface NotificationData {
  id: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  type: 'payment' | 'jamiya' | 'general' | 'reminder';
  priority: 'low' | 'normal' | 'high';
  scheduledFor?: string;
}

export interface NotificationSettings {
  enabled: boolean;
  paymentReminders: boolean;
  jamiyaUpdates: boolean;
  generalNotifications: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  reminderDays: number;
}

// Storage Keys
const STORAGE_KEYS = {
  PUSH_TOKEN: 'push_token',
  NOTIFICATION_SETTINGS: 'notification_settings',
  PENDING_NOTIFICATIONS: 'pending_notifications',
};

// Default Settings
const DEFAULT_SETTINGS: NotificationSettings = {
  enabled: true,
  paymentReminders: true,
  jamiyaUpdates: true,
  generalNotifications: true,
  soundEnabled: true,
  vibrationEnabled: true,
  reminderDays: 3,
};

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Notification Service Class
class NotificationService {
  private pushToken: string | null = null;
  private settings: NotificationSettings = DEFAULT_SETTINGS;

  /**
   * Initialize notification service
   */
  async initialize(): Promise<void> {
    try {
      // Load settings
      await this.loadSettings();
      
      // Request permissions and get token
      if (this.settings.enabled) {
        await this.requestPermissions();
        await this.registerForPushNotifications();
      }
      
      // Set up notification listeners
      this.setupNotificationListeners();
      
      // Schedule pending notifications
      await this.schedulePendingNotifications();
    } catch (error) {
      console.error('Failed to initialize notifications:', error);
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      if (!Device.isDevice) {
        console.log('Must use physical device for Push Notifications');
        return false;
      }

      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Permission request error:', error);
      return false;
    }
  }

  /**
   * Register for push notifications
   */
  async registerForPushNotifications(): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return null;
      }

      const token = (await Notifications.getExpoPushTokenAsync()).data;
      this.pushToken = token;
      
      // Store token locally
      await AsyncStorage.setItem(STORAGE_KEYS.PUSH_TOKEN, token);
      
      // Send token to server
      await this.sendTokenToServer(token);
      
      return token;
    } catch (error) {
      console.error('Push notification registration error:', error);
      return null;
    }
  }

  /**
   * Send push token to server
   */
  private async sendTokenToServer(token: string): Promise<void> {
    try {
      await httpClient.post('/notifications/register', {
        pushToken: token,
        platform: Platform.OS,
        deviceId: Device.osInternalBuildId,
      });
    } catch (error) {
      console.error('Failed to send token to server:', error);
    }
  }

  /**
   * Setup notification listeners
   */
  private setupNotificationListeners(): void {
    // Handle notification received while app is in foreground
    Notifications.addNotificationReceivedListener((notification) => {
      console.log('Notification received:', notification);
      this.handleNotificationReceived(notification);
    });

    // Handle notification response (user tapped notification)
    Notifications.addNotificationResponseReceivedListener((response) => {
      console.log('Notification response:', response);
      this.handleNotificationResponse(response);
    });
  }

  /**
   * Handle notification received
   */
  private handleNotificationReceived(notification: Notifications.Notification): void {
    // You can add custom logic here
    // For example, update app state, show in-app notification, etc.
  }

  /**
   * Handle notification response
   */
  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const { notification } = response;
    const { data } = notification.request.content;
    
    // Navigate to appropriate screen based on notification data
    if (data?.screen) {
      // You can implement navigation logic here
      console.log('Navigate to:', data.screen);
    }
  }

  /**
   * Schedule local notification
   */
  async scheduleNotification(notificationData: NotificationData): Promise<string | null> {
    try {
      const settings = await this.getSettings();
      if (!settings.enabled) {
        return null;
      }

      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: notificationData.title,
          body: notificationData.body,
          data: notificationData.data || {},
          sound: settings.soundEnabled ? 'default' : undefined,
        },
        trigger: notificationData.scheduledFor 
          ? { date: new Date(notificationData.scheduledFor) }
          : null,
      });

      return identifier;
    } catch (error) {
      console.error('Schedule notification error:', error);
      return null;
    }
  }

  /**
   * Cancel notification
   */
  async cancelNotification(identifier: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
    } catch (error) {
      console.error('Cancel notification error:', error);
    }
  }

  /**
   * Cancel all notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Cancel all notifications error:', error);
    }
  }

  /**
   * Get notification settings
   */
  async getSettings(): Promise<NotificationSettings> {
    return this.settings;
  }

  /**
   * Update notification settings
   */
  async updateSettings(newSettings: Partial<NotificationSettings>): Promise<void> {
    try {
      this.settings = { ...this.settings, ...newSettings };
      await AsyncStorage.setItem(
        STORAGE_KEYS.NOTIFICATION_SETTINGS,
        JSON.stringify(this.settings)
      );

      // If notifications were disabled, cancel all scheduled notifications
      if (!this.settings.enabled) {
        await this.cancelAllNotifications();
      }
    } catch (error) {
      console.error('Update settings error:', error);
    }
  }

  /**
   * Load settings from storage
   */
  private async loadSettings(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.NOTIFICATION_SETTINGS);
      if (stored) {
        this.settings = { ...DEFAULT_SETTINGS, ...JSON.parse(stored) };
      }
    } catch (error) {
      console.error('Load settings error:', error);
      this.settings = DEFAULT_SETTINGS;
    }
  }

  /**
   * Schedule payment reminder
   */
  async schedulePaymentReminder(
    jamiyaName: string,
    amount: number,
    dueDate: string
  ): Promise<string | null> {
    try {
      const settings = await this.getSettings();
      if (!settings.paymentReminders) {
        return null;
      }

      const reminderDate = new Date(dueDate);
      reminderDate.setDate(reminderDate.getDate() - settings.reminderDays);

      return await this.scheduleNotification({
        id: `payment_reminder_${Date.now()}`,
        title: 'تذكير دفعة',
        body: `موعد دفعة ${jamiyaName} خلال ${settings.reminderDays} أيام - ${amount} ريال`,
        type: 'payment',
        priority: 'high',
        scheduledFor: reminderDate.toISOString(),
        data: {
          type: 'payment_reminder',
          jamiyaName,
          amount,
          dueDate,
        },
      });
    } catch (error) {
      console.error('Schedule payment reminder error:', error);
      return null;
    }
  }

  /**
   * Schedule jamiya update notification
   */
  async scheduleJamiyaUpdate(
    jamiyaName: string,
    message: string
  ): Promise<string | null> {
    try {
      const settings = await this.getSettings();
      if (!settings.jamiyaUpdates) {
        return null;
      }

      return await this.scheduleNotification({
        id: `jamiya_update_${Date.now()}`,
        title: `تحديث ${jamiyaName}`,
        body: message,
        type: 'jamiya',
        priority: 'normal',
        data: {
          type: 'jamiya_update',
          jamiyaName,
        },
      });
    } catch (error) {
      console.error('Schedule jamiya update error:', error);
      return null;
    }
  }

  /**
   * Schedule pending notifications
   */
  private async schedulePendingNotifications(): Promise<void> {
    try {
      const pending = await AsyncStorage.getItem(STORAGE_KEYS.PENDING_NOTIFICATIONS);
      if (pending) {
        const notifications: NotificationData[] = JSON.parse(pending);
        
        for (const notification of notifications) {
          await this.scheduleNotification(notification);
        }
        
        // Clear pending notifications
        await AsyncStorage.removeItem(STORAGE_KEYS.PENDING_NOTIFICATIONS);
      }
    } catch (error) {
      console.error('Schedule pending notifications error:', error);
    }
  }

  /**
   * Get push token
   */
  getPushToken(): string | null {
    return this.pushToken;
  }

  /**
   * Check if notifications are enabled
   */
  async areNotificationsEnabled(): Promise<boolean> {
    const { status } = await Notifications.getPermissionsAsync();
    return status === 'granted' && this.settings.enabled;
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
export default notificationService;
