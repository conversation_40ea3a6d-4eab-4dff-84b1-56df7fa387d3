package com.aljameia.app.ui.dashboard

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.aljameia.app.ui.theme.AlJameiaTheme
import java.text.NumberFormat
import java.util.*

// @AndroidEntryPoint
class DashboardActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            AlJameiaTheme {
                DashboardScreen()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DashboardScreen() {
    val financialCards = getFinancialSummaryCards()
    val quickActions = getQuickActions()
    val recentTransactions = getRecentTransactions()

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        "Financial Dashboard",
                        style = MaterialTheme.typography.headlineSmall
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Financial Summary Cards
            item {
                Text(
                    text = "Financial Overview",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            item {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(financialCards) { card ->
                        FinancialSummaryCard(card)
                    }
                }
            }

            // Quick Actions
            item {
                Text(
                    text = "Quick Actions",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            item {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(quickActions) { action ->
                        QuickActionCard(action)
                    }
                }
            }

            // Recent Transactions
            item {
                Text(
                    text = "Recent Transactions",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            items(recentTransactions) { transaction ->
                TransactionItem(transaction)
            }
        }
    }
}

@Composable
fun FinancialSummaryCard(card: FinancialSummaryCardData) {
    Card(
        modifier = Modifier
            .width(160.dp)
            .height(120.dp),
        colors = CardDefaults.cardColors(
            containerColor = card.backgroundColor
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Icon(
                imageVector = card.icon,
                contentDescription = card.title,
                tint = card.iconColor,
                modifier = Modifier.size(24.dp)
            )
            
            Column {
                Text(
                    text = card.amount,
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = card.textColor
                )
                Text(
                    text = card.title,
                    style = MaterialTheme.typography.bodySmall,
                    color = card.textColor.copy(alpha = 0.8f)
                )
            }
        }
    }
}

@Composable
fun QuickActionCard(action: QuickActionData) {
    Card(
        modifier = Modifier
            .width(120.dp)
            .height(100.dp),
        onClick = { /* Handle click */ }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = action.icon,
                contentDescription = action.title,
                modifier = Modifier.size(32.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = action.title,
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
fun TransactionItem(transaction: TransactionItemData) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = transaction.icon,
                    contentDescription = transaction.type,
                    modifier = Modifier.size(24.dp),
                    tint = transaction.iconColor
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = transaction.description,
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = transaction.date,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Text(
                text = transaction.amount,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Bold,
                color = transaction.amountColor
            )
        }
    }
}

// Data classes and mock data
data class FinancialSummaryCardData(
    val title: String,
    val amount: String,
    val icon: ImageVector,
    val backgroundColor: androidx.compose.ui.graphics.Color,
    val textColor: androidx.compose.ui.graphics.Color,
    val iconColor: androidx.compose.ui.graphics.Color
)

data class QuickActionData(
    val title: String,
    val icon: ImageVector
)

data class TransactionItemData(
    val description: String,
    val type: String,
    val amount: String,
    val date: String,
    val icon: ImageVector,
    val iconColor: androidx.compose.ui.graphics.Color,
    val amountColor: androidx.compose.ui.graphics.Color
)

fun getFinancialSummaryCards(): List<FinancialSummaryCardData> {
    // val currencyFormat = NumberFormat.getCurrencyInstance(Locale("ar", "SA"))
    
    return listOf(
        FinancialSummaryCardData(
            title = "Total Balance",
            amount = "15,750 SAR",
            icon = Icons.Default.AccountBalance,
            backgroundColor = Color(0xFFE3F2FD),
            textColor = Color(0xFF1565C0),
            iconColor = Color(0xFF1976D2)
        ),
        FinancialSummaryCardData(
            title = "This Month",
            amount = "2,500 SAR",
            icon = Icons.Default.TrendingUp,
            backgroundColor = Color(0xFFE8F5E8),
            textColor = Color(0xFF2E7D32),
            iconColor = Color(0xFF4CAF50)
        ),
        FinancialSummaryCardData(
            title = "Pending",
            amount = "850 SAR",
            icon = Icons.Default.Schedule,
            backgroundColor = Color(0xFFFFF3E0),
            textColor = Color(0xFFE65100),
            iconColor = Color(0xFFFF9800)
        )
    )
}

fun getQuickActions(): List<QuickActionData> {
    return listOf(
        QuickActionData("Pay Fees", Icons.Default.Payment),
        QuickActionData("Transfer", Icons.Default.SwapHoriz),
        QuickActionData("History", Icons.Default.History),
        QuickActionData("Reports", Icons.Default.Assessment)
    )
}

fun getRecentTransactions(): List<TransactionItemData> {
    return listOf(
        TransactionItemData(
            description = "Membership Fee - December",
            type = "MEMBERSHIP",
            amount = "-500 SAR",
            date = "Dec 15, 2024",
            icon = Icons.Default.CardMembership,
            iconColor = Color(0xFF1976D2),
            amountColor = Color(0xFFD32F2F)
        ),
        TransactionItemData(
            description = "Event Registration",
            type = "EVENT",
            amount = "-150 SAR",
            date = "Dec 12, 2024",
            icon = Icons.Default.Event,
            iconColor = Color(0xFF7B1FA2),
            amountColor = Color(0xFFD32F2F)
        ),
        TransactionItemData(
            description = "Donation Received",
            type = "DONATION",
            amount = "+1,000 SAR",
            date = "Dec 10, 2024",
            icon = Icons.Default.Favorite,
            iconColor = Color(0xFF388E3C),
            amountColor = Color(0xFF1976D2)
        )
    )
}
