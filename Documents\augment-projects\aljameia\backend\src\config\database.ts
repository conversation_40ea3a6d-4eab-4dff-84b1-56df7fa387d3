import mongoose from 'mongoose';
import { logger } from '@/utils/logger';

// MongoDB connection options
const mongoOptions: mongoose.ConnectOptions = {
  maxPoolSize: parseInt(process.env.DB_POOL_SIZE || '10'),
  serverSelectionTimeoutMS: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'),
  socketTimeoutMS: 45000,
  bufferMaxEntries: 0,
  retryWrites: true,
  w: 'majority',
};

// Database connection
export const connectDatabase = async (): Promise<void> => {
  try {
    const mongoUri = process.env.MONGODB_URI;

    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is not defined');
    }

    // Connect to MongoDB
    await mongoose.connect(mongoUri, mongoOptions);

    logger.info('✅ Connected to MongoDB successfully');

    // Log database name
    const dbName = mongoose.connection.db?.databaseName;
    logger.info(`📊 Database: ${dbName}`);

  } catch (error) {
    logger.error('❌ MongoDB connection error:', error);
    throw error;
  }
};

// Database event listeners
mongoose.connection.on('connected', () => {
  logger.info('🔗 MongoDB connected');
});

mongoose.connection.on('error', (error) => {
  logger.error('❌ MongoDB connection error:', error);
});

mongoose.connection.on('disconnected', () => {
  logger.warn('⚠️ MongoDB disconnected');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  try {
    await mongoose.connection.close();
    logger.info('📴 MongoDB connection closed through app termination');
    process.exit(0);
  } catch (error) {
    logger.error('Error closing MongoDB connection:', error);
    process.exit(1);
  }
});

// Database health check
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    const state = mongoose.connection.readyState;

    // 0 = disconnected, 1 = connected, 2 = connecting, 3 = disconnecting
    if (state === 1) {
      // Ping the database
      await mongoose.connection.db?.admin().ping();
      return true;
    }

    return false;
  } catch (error) {
    logger.error('Database health check failed:', error);
    return false;
  }
};

// Get database statistics
export const getDatabaseStats = async () => {
  try {
    const db = mongoose.connection.db;
    if (!db) {
      throw new Error('Database not connected');
    }

    const stats = await db.stats();
    const collections = await db.listCollections().toArray();

    return {
      database: db.databaseName,
      collections: collections.length,
      dataSize: stats.dataSize,
      storageSize: stats.storageSize,
      indexSize: stats.indexSize,
      objects: stats.objects,
      avgObjSize: stats.avgObjSize,
      indexes: stats.indexes,
    };
  } catch (error) {
    logger.error('Failed to get database stats:', error);
    throw error;
  }
};

export default mongoose;
