package com.aljameia.app.ui.main

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
// import androidx.activity.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
// import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
// import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.aljameia.app.R
import com.aljameia.app.ui.theme.AlJameiaTheme
// import dagger.hilt.android.AndroidEntryPoint

// @AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private lateinit var viewModel: MainViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d("AlJameia", "MainActivity onCreate() called")

        try {
            // Initialize ViewModel
            viewModel = ViewModelProvider(this)[MainViewModel::class.java]

            setContent {
                AlJameiaTheme {
                    MainScreen(viewModel = viewModel)
                }
            }

            Log.d("AlJameia", "MainActivity setup completed")
        } catch (e: Exception) {
            Log.e("AlJameia", "Error in MainActivity onCreate", e)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(viewModel: MainViewModel) {
    val uiState by viewModel.uiState.collectAsState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.app_name)) }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            when (uiState) {
                is MainUiState.Loading -> {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text("Loading...")
                }
                is MainUiState.Success -> {
                    Text(
                        text = "Welcome to Al Jameia",
                        style = MaterialTheme.typography.headlineMedium,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Your comprehensive association management platform",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(32.dp))
                    
                    // Quick action buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        ElevatedButton(
                            onClick = {
                                // TODO: Navigate to payments screen
                                viewModel.showMessage("Payments feature coming soon!")
                            }
                        ) {
                            Text("💳 Payments")
                        }
                        ElevatedButton(
                            onClick = {
                                // TODO: Navigate to documents screen
                                viewModel.showMessage("Documents feature coming soon!")
                            }
                        ) {
                            Text("📄 Documents")
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        ElevatedButton(
                            onClick = {
                                // TODO: Navigate to profile screen
                                viewModel.showMessage("Profile feature coming soon!")
                            }
                        ) {
                            Text("👤 Profile")
                        }
                        ElevatedButton(
                            onClick = {
                                // TODO: Navigate to settings screen
                                viewModel.showMessage("Settings feature coming soon!")
                            }
                        ) {
                            Text("⚙️ Settings")
                        }
                    }

                    // Show message if any
                    (uiState as? MainUiState.Success)?.message?.let { message ->
                        Spacer(modifier = Modifier.height(16.dp))
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.primaryContainer
                            )
                        ) {
                            Text(
                                text = message,
                                modifier = Modifier.padding(16.dp),
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                    }
                }
                is MainUiState.Error -> {
                    Text(
                        text = "Error: ${(uiState as MainUiState.Error).message}",
                        color = MaterialTheme.colorScheme.error,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(
                        onClick = { viewModel.retry() }
                    ) {
                        Text("Retry")
                    }
                }
            }
        }
    }
}
