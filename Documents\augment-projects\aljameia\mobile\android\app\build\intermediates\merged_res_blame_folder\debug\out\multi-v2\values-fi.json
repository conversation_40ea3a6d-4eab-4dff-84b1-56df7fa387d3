{"logs": [{"outputFile": "com.aljameia.app-mergeDebugResources-104:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\21cc0a6be925d3c2a0c747bb469e8733\\transformed\\appcompat-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,18845", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,18921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d2a56f6da0e2e4ab4da1b326f38c566a\\transformed\\material-1.11.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1025,1118,1193,1258,1346,1411,1477,1535,1606,1672,1726,1836,1896,1960,2014,2087,2203,2287,2368,2501,2586,2671,2804,2894,2968,3020,3071,3137,3214,3296,3380,3454,3528,3607,3684,3756,3863,3952,4028,4119,4214,4288,4361,4455,4509,4583,4655,4741,4827,4889,4953,5016,5087,5188,5291,5386,5486,5542,5597", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "263,339,413,496,585,667,763,871,955,1020,1113,1188,1253,1341,1406,1472,1530,1601,1667,1721,1831,1891,1955,2009,2082,2198,2282,2363,2496,2581,2666,2799,2889,2963,3015,3066,3132,3209,3291,3375,3449,3523,3602,3679,3751,3858,3947,4023,4114,4209,4283,4356,4450,4504,4578,4650,4736,4822,4884,4948,5011,5082,5183,5286,5381,5481,5537,5592,5671"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3424,3500,3574,3657,3746,4562,4658,4766,11503,11647,13338,13583,13648,13736,13801,13867,13925,13996,14062,14116,14226,14286,14350,14404,14477,14593,14677,14758,14891,14976,15061,15194,15284,15358,15410,15461,15527,15604,15686,15770,15844,15918,15997,16074,16146,16253,16342,16418,16509,16604,16678,16751,16845,16899,16973,17045,17131,17217,17279,17343,17406,17477,17578,17681,17776,17876,17932,18523", "endLines": "5,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "313,3495,3569,3652,3741,3823,4653,4761,4845,11563,11735,13408,13643,13731,13796,13862,13920,13991,14057,14111,14221,14281,14345,14399,14472,14588,14672,14753,14886,14971,15056,15189,15279,15353,15405,15456,15522,15599,15681,15765,15839,15913,15992,16069,16141,16248,16337,16413,16504,16599,16673,16746,16840,16894,16968,17040,17126,17212,17274,17338,17401,17472,17573,17676,17771,17871,17927,17982,18597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c514f303830aa824ccabc1313adde6e2\\transformed\\material3-1.1.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,276,381,491,571,668,783,916,1034,1182,1268,1360,1454,1552,1666,1785,1882,2010,2138,2263,2426,2544,2664,2788,2907,3002,3097,3214,3332,3431,3538,3642,3776,3916,4020,4121,4200,4279,4359,4441,4537,4613,4694,4787,4886,4979,5077,5163,5267,5366,5469,5583,5659,5760", "endColumns": "111,108,104,109,79,96,114,132,117,147,85,91,93,97,113,118,96,127,127,124,162,117,119,123,118,94,94,116,117,98,106,103,133,139,103,100,78,78,79,81,95,75,80,92,98,92,97,85,103,98,102,113,75,100,94", "endOffsets": "162,271,376,486,566,663,778,911,1029,1177,1263,1355,1449,1547,1661,1780,1877,2005,2133,2258,2421,2539,2659,2783,2902,2997,3092,3209,3327,3426,3533,3637,3771,3911,4015,4116,4195,4274,4354,4436,4532,4608,4689,4782,4881,4974,5072,5158,5262,5361,5464,5578,5654,5755,5850"}, "to": {"startLines": "33,34,35,36,54,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,109,112,195,198,200,204,205,206,207,208,209,210,211,212,213,214,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2988,3100,3209,3314,5029,7492,7589,7704,7837,7955,8103,8189,8281,8375,8473,8587,8706,8803,8931,9059,9184,9347,9465,9585,9709,9828,9923,10018,10135,10253,10352,10459,10563,10697,10837,10941,11335,11568,18765,18998,19181,19548,19624,19705,19798,19897,19990,20088,20174,20278,20377,20480,20594,20670,20771", "endColumns": "111,108,104,109,79,96,114,132,117,147,85,91,93,97,113,118,96,127,127,124,162,117,119,123,118,94,94,116,117,98,106,103,133,139,103,100,78,78,79,81,95,75,80,92,98,92,97,85,103,98,102,113,75,100,94", "endOffsets": "3095,3204,3309,3419,5104,7584,7699,7832,7950,8098,8184,8276,8370,8468,8582,8701,8798,8926,9054,9179,9342,9460,9580,9704,9823,9918,10013,10130,10248,10347,10454,10558,10692,10832,10936,11037,11409,11642,18840,19075,19272,19619,19700,19793,19892,19985,20083,20169,20273,20372,20475,20589,20665,20766,20861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\523b9fad2b83d89c2b74860689d8fb91\\transformed\\biometric-1.1.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,257,378,515,648,797,918,1048,1148,1291,1429", "endColumns": "108,92,120,136,132,148,120,129,99,142,137,116", "endOffsets": "159,252,373,510,643,792,913,1043,1143,1286,1424,1541"}, "to": {"startLines": "73,107,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7280,11141,12049,12170,12307,12440,12589,12710,12840,12940,13083,13221", "endColumns": "108,92,120,136,132,148,120,129,99,142,137,116", "endOffsets": "7384,11229,12165,12302,12435,12584,12705,12835,12935,13078,13216,13333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1c0b3a48f4d4c74d31971bddddc3d282\\transformed\\play-services-base-18.1.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5109,5220,5373,5504,5610,5753,5879,5995,6252,6393,6499,6648,6774,6922,7061,7127,7197", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "5215,5368,5499,5605,5748,5874,5990,6097,6388,6494,6643,6769,6917,7056,7122,7192,7275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b3e74a7425b14daa5ccd7c3c1ea8df2a\\transformed\\browser-1.4.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "74,114,115,116", "startColumns": "4,4,4,4", "startOffsets": "7389,11740,11841,11950", "endColumns": "102,100,108,98", "endOffsets": "7487,11836,11945,12044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\cdc55fe2502a063bcee343c082439e6f\\transformed\\navigation-ui-2.7.6\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,122", "endOffsets": "156,279"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "17987,18093", "endColumns": "105,122", "endOffsets": "18088,18211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f1a3dac0af0a85f165dc53a6762b4a81\\transformed\\play-services-basement-18.2.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6102", "endColumns": "149", "endOffsets": "6247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\85fae80ff7869a8da6251422548fb462\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "52,53,106,108,110,128,129,188,189,190,191,193,194,197,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4850,4944,11042,11234,11414,13413,13490,18216,18307,18389,18455,18602,18683,18926,19277,19354,19426", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "4939,5024,11136,11330,11498,13485,13578,18302,18384,18450,18518,18678,18760,18993,19349,19421,19543"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5081b8eacd451e5b41f2b566f41c579e\\transformed\\core-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "42,43,44,45,46,47,48,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3828,3924,4026,4124,4229,4334,4446,19080", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3919,4021,4119,4224,4329,4441,4557,19176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\403ef3e018096a306df17d01c38df724\\transformed\\zxing-android-embedded-4.3.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,321", "endColumns": "58,46,159,103", "endOffsets": "109,156,316,420"}, "to": {"startLines": "218,219,220,221", "startColumns": "4,4,4,4", "startOffsets": "20866,20925,20972,21132", "endColumns": "58,46,159,103", "endOffsets": "20920,20967,21127,21231"}}]}]}