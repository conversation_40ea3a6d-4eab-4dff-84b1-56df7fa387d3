// Jamiya (Financial Association) Management System Types

export interface Jamiya {
  id: string;
  name: string;
  description?: string;
  type: JamiyaType;
  status: JamiyaStatus;
  
  // Basic Configuration
  totalMembers: number;
  monthlyAmount: number;
  currency: string;
  duration: number; // in months
  
  // Dates
  startDate: string;
  endDate: string;
  createdAt: string;
  updatedAt: string;
  
  // Financial Rules
  rules: JamiyaRules;
  
  // Members and Cycles
  members: JamiyaMember[];
  cycles: JamiyaCycle[];
  contributions: Contribution[];
  
  // Management
  organizer: JamiyaOrganizer;
  guarantors: Guarantor[];
  
  // Statistics
  statistics: JamiyaStatistics;
  
  // Settings
  settings: JamiyaSettings;
}

export interface JamiyaRules {
  // Basic Rules
  allowEarlyWithdrawal: boolean;
  allowLateJoining: boolean;
  requireGuarantor: boolean;
  
  // Financial Rules
  lateFee: number; // penalty for late payment
  earlyWithdrawalDiscount: number; // discount for early cycles
  lateWithdrawalBonus: number; // bonus for late cycles
  
  // Bidding Rules (for auction type)
  allowBidding: boolean;
  minimumBid: number;
  biddingDeadline: number; // hours before cycle date
  
  // Payment Rules
  gracePeriod: number; // days allowed for late payment
  maxMissedPayments: number; // before suspension
  autoSuspension: boolean;
  
  // Withdrawal Rules
  advanceNotice: number; // days notice required for withdrawal
  withdrawalPenalty: number;
}

export interface JamiyaMember {
  id: string;
  userId: string;
  jamiyaId: string;
  
  // Personal Info
  name: string;
  email: string;
  phone: string;
  nationalId: string;
  
  // Membership Info
  joinDate: string;
  memberNumber: number;
  status: MemberStatus;
  
  // Financial Info
  assignedCycle: number;
  totalPaid: number;
  totalReceived: number;
  balance: number;
  
  // Cycle Management
  cyclePreferences: number[]; // preferred cycle numbers
  hasBidOnCycle: boolean;
  currentBid?: Bid;
  
  // Guarantees
  guarantorId?: string;
  guaranteedMembers: string[]; // members this person guarantees
  
  // Payment History
  paymentHistory: Payment[];
  missedPayments: number;
  lastPaymentDate?: string;
  
  // Status Tracking
  isActive: boolean;
  suspensionDate?: string;
  suspensionReason?: string;
  withdrawalDate?: string;
  withdrawalReason?: string;
}

export interface JamiyaCycle {
  id: string;
  jamiyaId: string;
  cycleNumber: number;
  
  // Dates
  dueDate: string;
  completedDate?: string;
  
  // Financial
  totalAmount: number;
  collectedAmount: number;
  distributedAmount: number;
  
  // Recipient
  recipientId?: string;
  recipientName?: string;
  
  // Bidding (for auction type)
  bids: Bid[];
  winningBid?: Bid;
  
  // Contributions for this cycle
  contributions: Contribution[];
  
  // Status
  status: CycleStatus;
  
  // Distribution
  distributionDate?: string;
  distributionMethod: DistributionMethod;
  distributionReference?: string;
}

export interface Contribution {
  id: string;
  jamiyaId: string;
  memberId: string;
  cycleId: string;
  
  // Amount Details
  amount: number;
  currency: string;
  
  // Payment Info
  paymentDate: string;
  dueDate: string;
  
  // Status
  status: ContributionStatus;
  
  // Late Payment
  isLate: boolean;
  daysLate?: number;
  lateFee?: number;
  
  // Payment Method
  paymentMethod: PaymentMethod;
  paymentReference?: string;
  
  // Verification
  verifiedBy?: string;
  verificationDate?: string;
  
  // Notes
  notes?: string;
}

export interface Bid {
  id: string;
  jamiyaId: string;
  cycleId: string;
  memberId: string;
  
  // Bid Details
  bidAmount: number;
  bidDate: string;
  
  // Status
  status: BidStatus;
  isWinning: boolean;
  
  // Auto-bid settings
  isAutoBid: boolean;
  maxAutoBidAmount?: number;
}

export interface Payment {
  id: string;
  jamiyaId: string;
  memberId: string;
  contributionId?: string;
  
  // Payment Details
  amount: number;
  currency: string;
  paymentDate: string;
  
  // Type
  type: PaymentType;
  
  // Method
  method: PaymentMethod;
  reference?: string;
  
  // Status
  status: PaymentStatus;
  
  // Verification
  verifiedBy?: string;
  verificationDate?: string;
  
  // Receipt
  receiptUrl?: string;
  
  // Notes
  notes?: string;
}

export interface JamiyaOrganizer {
  id: string;
  name: string;
  email: string;
  phone: string;
  
  // Credentials
  nationalId: string;
  bankAccount?: BankAccount;
  
  // Experience
  organizingExperience: number; // years
  successfulJamiyas: number;
  rating: number;
  
  // Verification
  isVerified: boolean;
  verificationDate?: string;
  verificationDocuments: string[];
}

export interface Guarantor {
  id: string;
  name: string;
  email: string;
  phone: string;
  nationalId: string;
  
  // Guarantee Details
  guaranteedMembers: string[];
  maxGuaranteeAmount: number;
  currentGuaranteeAmount: number;
  
  // Verification
  isVerified: boolean;
  verificationDocuments: string[];
  
  // Bank Info
  bankAccount?: BankAccount;
}

export interface BankAccount {
  bankName: string;
  accountNumber: string;
  iban: string;
  accountHolderName: string;
  isVerified: boolean;
}

export interface JamiyaStatistics {
  // Financial Stats
  totalCollected: number;
  totalDistributed: number;
  totalPending: number;
  
  // Member Stats
  activeMembers: number;
  suspendedMembers: number;
  completedMembers: number;
  
  // Payment Stats
  onTimePayments: number;
  latePayments: number;
  missedPayments: number;
  
  // Cycle Stats
  completedCycles: number;
  pendingCycles: number;
  
  // Performance
  collectionRate: number; // percentage
  onTimeRate: number; // percentage
  completionRate: number; // percentage
}

export interface JamiyaSettings {
  // Notifications
  enableEmailNotifications: boolean;
  enableSmsNotifications: boolean;
  enablePushNotifications: boolean;
  
  // Reminders
  paymentReminderDays: number[];
  cycleReminderDays: number[];
  
  // Automation
  autoProcessPayments: boolean;
  autoDistributeFunds: boolean;
  autoSuspendDefaulters: boolean;
  
  // Privacy
  showMemberDetails: boolean;
  showPaymentHistory: boolean;
  allowMemberCommunication: boolean;
  
  // Security
  requireTwoFactorAuth: boolean;
  requirePaymentVerification: boolean;
  logAllActivities: boolean;
}

// Enums
export type JamiyaType = 
  | 'simple'        // Traditional fixed amount
  | 'auction'       // With bidding system
  | 'discount'      // Early discount, late bonus
  | 'flexible';     // Variable amounts

export type JamiyaStatus = 
  | 'planning'      // Being set up
  | 'recruiting'    // Looking for members
  | 'active'        // Running
  | 'completed'     // Finished successfully
  | 'suspended'     // Temporarily stopped
  | 'cancelled';    // Terminated

export type MemberStatus = 
  | 'active'        // Good standing
  | 'late'          // Late payment
  | 'suspended'     // Suspended for non-payment
  | 'completed'     // Received their cycle
  | 'withdrawn';    // Left the jamiya

export type CycleStatus = 
  | 'upcoming'      // Not yet due
  | 'collecting'    // Collecting contributions
  | 'bidding'       // Bidding in progress
  | 'ready'         // Ready for distribution
  | 'distributed'   // Money distributed
  | 'completed';    // Fully processed

export type ContributionStatus = 
  | 'pending'       // Not yet paid
  | 'paid'          // Paid on time
  | 'late'          // Paid late
  | 'missed'        // Not paid
  | 'waived';       // Forgiven

export type BidStatus = 
  | 'active'        // Current bid
  | 'outbid'        // Superseded
  | 'winning'       // Highest bid
  | 'withdrawn';    // Cancelled

export type PaymentType = 
  | 'contribution'  // Regular monthly payment
  | 'late_fee'      // Penalty payment
  | 'bid_amount'    // Auction bid payment
  | 'guarantee'     // Guarantor payment
  | 'refund';       // Money returned

export type PaymentMethod = 
  | 'cash'
  | 'bank_transfer'
  | 'online_payment'
  | 'mobile_payment'
  | 'check'
  | 'credit_card';

export type PaymentStatus = 
  | 'pending'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'refunded';

export type DistributionMethod = 
  | 'cash'
  | 'bank_transfer'
  | 'check'
  | 'mobile_payment';

// Form Types
export interface CreateJamiyaForm {
  name: string;
  description?: string;
  type: JamiyaType;
  totalMembers: number;
  monthlyAmount: number;
  currency: string;
  startDate: string;
  rules: Partial<JamiyaRules>;
  settings: Partial<JamiyaSettings>;
}

export interface JoinJamiyaForm {
  jamiyaId: string;
  personalInfo: {
    name: string;
    email: string;
    phone: string;
    nationalId: string;
  };
  guarantorInfo?: {
    guarantorId: string;
    guarantorName: string;
  };
  cyclePreferences: number[];
  agreesToTerms: boolean;
}

export interface ContributionForm {
  jamiyaId: string;
  memberId: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentReference?: string;
  notes?: string;
}

// API Response Types
export interface JamiyaListResponse {
  jamiyas: Jamiya[];
  total: number;
  page: number;
  limit: number;
}

export interface JamiyaResponse {
  jamiya: Jamiya;
  success: boolean;
  message?: string;
}

export interface MemberResponse {
  member: JamiyaMember;
  success: boolean;
  message?: string;
}

// Monthly Subscription Types
export interface MonthlySubscription {
  id: string;
  jamiyaId: string;
  memberId: string;
  memberName: string;
  memberEmail: string;
  memberPhone: string;
  sharesCount: number; // عدد الأسهم
  pricePerShare: number; // سعر السهم الواحد
  monthlyAmount: number; // المبلغ الإجمالي الشهري (sharesCount * pricePerShare)
  currency: string;
  startDate: string;
  endDate?: string;
  status: 'active' | 'paused' | 'cancelled' | 'completed';
  paymentMethod: 'cash' | 'bank_transfer' | 'online_payment' | 'check' | 'auto_debit';
  bankAccount?: string;
  autoRenewal: boolean;
  reminderDays: number[];
  totalPaid: number;
  totalDue: number;
  missedPayments: number;
  lastPaymentDate?: string;
  nextPaymentDate: string;
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionPayment {
  id: string;
  subscriptionId: string;
  jamiyaId: string;
  memberId: string;
  amount: number;
  currency: string;
  dueDate: string;
  paidDate?: string;
  status: 'pending' | 'paid' | 'late' | 'missed' | 'cancelled';
  paymentMethod?: 'cash' | 'bank_transfer' | 'online_payment' | 'check' | 'auto_debit';
  reference?: string;
  lateFee?: number;
  notes?: string;
  remindersSent: number;
  lastReminderDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionForm {
  jamiyaId: string;
  personalInfo: {
    name: string;
    email: string;
    phone: string;
    nationalId: string;
  };
  subscriptionDetails: {
    sharesCount: number;
    pricePerShare: number;
    monthlyAmount: number;
    paymentMethod: PaymentMethod;
    bankAccount?: string;
    autoRenewal: boolean;
    reminderDays: number[];
  };
  agreesToTerms: boolean;
}

export interface SubscriptionResponse {
  subscription: MonthlySubscription;
  success: boolean;
  message?: string;
}
