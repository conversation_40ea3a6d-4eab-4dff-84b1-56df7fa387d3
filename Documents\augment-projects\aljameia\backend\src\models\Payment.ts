import mongoose, { Document, Schema } from 'mongoose';

// Payment interface
export interface IPayment extends Document {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  subscriptionId: mongoose.Types.ObjectId;
  amount: number;
  currency: 'SAR' | 'USD' | 'EUR';
  method: 'mada' | 'stc_pay' | 'apple_pay' | 'bank_transfer' | 'cash';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  transactionId?: string;
  gatewayTransactionId?: string;
  gatewayResponse?: any;
  description?: string;
  metadata: {
    jamiyaId: mongoose.Types.ObjectId;
    jamiyaName: string;
    paymentMonth: string;
    paymentYear: number;
    isLatePayment: boolean;
    lateFee?: number;
  };
  paymentDetails: {
    cardLast4?: string;
    cardBrand?: string;
    bankName?: string;
    accountNumber?: string;
    referenceNumber?: string;
  };
  fees: {
    processingFee: number;
    lateFee: number;
    totalFees: number;
  };
  refund?: {
    refundId: string;
    refundAmount: number;
    refundReason: string;
    refundDate: Date;
    refundStatus: 'pending' | 'completed' | 'failed';
  };
  attempts: {
    attemptNumber: number;
    attemptDate: Date;
    status: string;
    errorMessage?: string;
    gatewayResponse?: any;
  }[];
  scheduledDate?: Date;
  completedAt?: Date;
  failedAt?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Methods
  markAsCompleted(transactionId: string, gatewayResponse?: any): Promise<void>;
  markAsFailed(errorMessage: string, gatewayResponse?: any): Promise<void>;
  addAttempt(status: string, errorMessage?: string, gatewayResponse?: any): Promise<void>;
  processRefund(amount: number, reason: string): Promise<void>;
  calculateFees(): void;
}

// Payment schema
const paymentSchema = new Schema<IPayment>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
  },
  subscriptionId: {
    type: Schema.Types.ObjectId,
    ref: 'Subscription',
    required: [true, 'Subscription ID is required'],
  },
  amount: {
    type: Number,
    required: [true, 'Payment amount is required'],
    min: [1, 'Payment amount must be positive'],
  },
  currency: {
    type: String,
    enum: ['SAR', 'USD', 'EUR'],
    default: 'SAR',
  },
  method: {
    type: String,
    enum: ['mada', 'stc_pay', 'apple_pay', 'bank_transfer', 'cash'],
    required: [true, 'Payment method is required'],
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
    default: 'pending',
  },
  transactionId: {
    type: String,
    unique: true,
    sparse: true,
  },
  gatewayTransactionId: {
    type: String,
    sparse: true,
  },
  gatewayResponse: {
    type: Schema.Types.Mixed,
  },
  description: {
    type: String,
    maxlength: [200, 'Description cannot exceed 200 characters'],
  },
  metadata: {
    jamiyaId: {
      type: Schema.Types.ObjectId,
      ref: 'Jamiya',
      required: true,
    },
    jamiyaName: {
      type: String,
      required: true,
    },
    paymentMonth: {
      type: String,
      required: true,
    },
    paymentYear: {
      type: Number,
      required: true,
    },
    isLatePayment: {
      type: Boolean,
      default: false,
    },
    lateFee: {
      type: Number,
      default: 0,
      min: [0, 'Late fee cannot be negative'],
    },
  },
  paymentDetails: {
    cardLast4: String,
    cardBrand: String,
    bankName: String,
    accountNumber: String,
    referenceNumber: String,
  },
  fees: {
    processingFee: {
      type: Number,
      default: 0,
      min: [0, 'Processing fee cannot be negative'],
    },
    lateFee: {
      type: Number,
      default: 0,
      min: [0, 'Late fee cannot be negative'],
    },
    totalFees: {
      type: Number,
      default: 0,
      min: [0, 'Total fees cannot be negative'],
    },
  },
  refund: {
    refundId: String,
    refundAmount: {
      type: Number,
      min: [0, 'Refund amount cannot be negative'],
    },
    refundReason: String,
    refundDate: Date,
    refundStatus: {
      type: String,
      enum: ['pending', 'completed', 'failed'],
    },
  },
  attempts: [{
    attemptNumber: {
      type: Number,
      required: true,
      min: [1, 'Attempt number must be at least 1'],
    },
    attemptDate: {
      type: Date,
      required: true,
      default: Date.now,
    },
    status: {
      type: String,
      required: true,
    },
    errorMessage: String,
    gatewayResponse: Schema.Types.Mixed,
  }],
  scheduledDate: {
    type: Date,
  },
  completedAt: {
    type: Date,
  },
  failedAt: {
    type: Date,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
paymentSchema.index({ userId: 1 });
paymentSchema.index({ subscriptionId: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ method: 1 });
paymentSchema.index({ createdAt: 1 });
paymentSchema.index({ transactionId: 1 }, { unique: true, sparse: true });
paymentSchema.index({ 'metadata.jamiyaId': 1 });
paymentSchema.index({ scheduledDate: 1 });
paymentSchema.index({ completedAt: 1 });

// Virtual for net amount (amount minus fees)
paymentSchema.virtual('netAmount').get(function() {
  return this.amount - this.fees.totalFees;
});

// Virtual for is overdue
paymentSchema.virtual('isOverdue').get(function() {
  if (!this.scheduledDate) return false;
  return this.scheduledDate < new Date() && this.status === 'pending';
});

// Virtual for processing time
paymentSchema.virtual('processingTime').get(function() {
  if (!this.completedAt) return null;
  return this.completedAt.getTime() - this.createdAt.getTime();
});

// Pre-save middleware
paymentSchema.pre('save', function(next) {
  // Calculate fees
  this.calculateFees();
  
  // Generate transaction ID if not exists
  if (!this.transactionId && this.status !== 'pending') {
    this.transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  next();
});

// Instance method to calculate fees
paymentSchema.methods.calculateFees = function(): void {
  let processingFee = 0;
  
  // Calculate processing fee based on payment method
  switch (this.method) {
    case 'mada':
      processingFee = Math.min(this.amount * 0.02, 10); // 2% max 10 SAR
      break;
    case 'stc_pay':
      processingFee = this.amount * 0.015; // 1.5%
      break;
    case 'apple_pay':
      processingFee = this.amount * 0.025; // 2.5%
      break;
    case 'bank_transfer':
      processingFee = 5; // Fixed 5 SAR
      break;
    case 'cash':
      processingFee = 0; // No fee for cash
      break;
  }
  
  this.fees.processingFee = Math.round(processingFee * 100) / 100;
  this.fees.lateFee = this.metadata.lateFee || 0;
  this.fees.totalFees = this.fees.processingFee + this.fees.lateFee;
};

// Instance method to mark as completed
paymentSchema.methods.markAsCompleted = async function(
  transactionId: string,
  gatewayResponse?: any
): Promise<void> {
  this.status = 'completed';
  this.transactionId = transactionId;
  this.gatewayResponse = gatewayResponse;
  this.completedAt = new Date();
  
  await this.save();
  
  // Update subscription payment history
  const Subscription = mongoose.model('Subscription');
  const subscription = await Subscription.findById(this.subscriptionId);
  if (subscription) {
    await subscription.updatePaymentHistory(this._id, this.amount, 'completed');
  }
};

// Instance method to mark as failed
paymentSchema.methods.markAsFailed = async function(
  errorMessage: string,
  gatewayResponse?: any
): Promise<void> {
  this.status = 'failed';
  this.gatewayResponse = gatewayResponse;
  this.failedAt = new Date();
  
  // Add failed attempt
  await this.addAttempt('failed', errorMessage, gatewayResponse);
  
  await this.save();
  
  // Update subscription payment history
  const Subscription = mongoose.model('Subscription');
  const subscription = await Subscription.findById(this.subscriptionId);
  if (subscription) {
    await subscription.updatePaymentHistory(this._id, this.amount, 'failed');
  }
};

// Instance method to add attempt
paymentSchema.methods.addAttempt = async function(
  status: string,
  errorMessage?: string,
  gatewayResponse?: any
): Promise<void> {
  this.attempts.push({
    attemptNumber: this.attempts.length + 1,
    attemptDate: new Date(),
    status,
    errorMessage,
    gatewayResponse,
  });
  
  await this.save();
};

// Instance method to process refund
paymentSchema.methods.processRefund = async function(
  amount: number,
  reason: string
): Promise<void> {
  if (this.status !== 'completed') {
    throw new Error('Can only refund completed payments');
  }
  
  if (amount > this.amount) {
    throw new Error('Refund amount cannot exceed payment amount');
  }
  
  this.refund = {
    refundId: `REF_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    refundAmount: amount,
    refundReason: reason,
    refundDate: new Date(),
    refundStatus: 'pending',
  };
  
  if (amount === this.amount) {
    this.status = 'refunded';
  }
  
  await this.save();
};

// Static method to find by transaction ID
paymentSchema.statics.findByTransactionId = function(transactionId: string) {
  return this.findOne({ transactionId });
};

// Static method to find pending payments
paymentSchema.statics.findPending = function() {
  return this.find({ status: 'pending' });
};

// Static method to find overdue payments
paymentSchema.statics.findOverdue = function() {
  return this.find({
    status: 'pending',
    scheduledDate: { $lt: new Date() },
  });
};

// Static method to get payment statistics
paymentSchema.statics.getPaymentStats = async function(filters: any = {}) {
  const stats = await this.aggregate([
    { $match: filters },
    {
      $group: {
        _id: null,
        totalPayments: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        completedPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        failedPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        pendingPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        },
        totalFees: { $sum: '$fees.totalFees' },
        averageAmount: { $avg: '$amount' },
        averageProcessingTime: { $avg: '$processingTime' },
      }
    }
  ]);
  
  return stats[0] || {
    totalPayments: 0,
    totalAmount: 0,
    completedPayments: 0,
    failedPayments: 0,
    pendingPayments: 0,
    totalFees: 0,
    averageAmount: 0,
    averageProcessingTime: 0,
  };
};

// Static method to get payment methods distribution
paymentSchema.statics.getPaymentMethodsStats = async function() {
  return await this.aggregate([
    { $match: { status: 'completed' } },
    {
      $group: {
        _id: '$method',
        count: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        averageAmount: { $avg: '$amount' },
      }
    },
    { $sort: { count: -1 } }
  ]);
};

// Create and export the model
export const Payment = mongoose.model<IPayment>('Payment', paymentSchema);
export default Payment;
