package com.aljameia.app.ui.settings

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.aljameia.app.ui.theme.AlJameiaTheme
// import dagger.hilt.android.AndroidEntryPoint

// @AndroidEntryPoint
class SettingsActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            AlJameiaTheme {
                SettingsScreen()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen() {
    var selectedLanguage by remember { mutableStateOf("English") }
    var notificationsEnabled by remember { mutableStateOf(true) }
    var biometricEnabled by remember { mutableStateOf(false) }
    var darkModeEnabled by remember { mutableStateOf(false) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        if (selectedLanguage == "العربية") "الإعدادات" else "Settings",
                        style = MaterialTheme.typography.headlineSmall
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Language Settings
            item {
                SettingsSectionHeader(
                    title = if (selectedLanguage == "العربية") "اللغة والمنطقة" else "Language & Region"
                )
            }

            item {
                LanguageSettingItem(
                    selectedLanguage = selectedLanguage,
                    onLanguageChanged = { selectedLanguage = it }
                )
            }

            // Notifications
            item {
                SettingsSectionHeader(
                    title = if (selectedLanguage == "العربية") "الإشعارات" else "Notifications"
                )
            }

            item {
                SwitchSettingItem(
                    title = if (selectedLanguage == "العربية") "تفعيل الإشعارات" else "Enable Notifications",
                    subtitle = if (selectedLanguage == "العربية") "استقبال إشعارات التطبيق" else "Receive app notifications",
                    icon = Icons.Default.Notifications,
                    checked = notificationsEnabled,
                    onCheckedChange = { notificationsEnabled = it }
                )
            }

            // Security
            item {
                SettingsSectionHeader(
                    title = if (selectedLanguage == "العربية") "الأمان" else "Security"
                )
            }

            item {
                SwitchSettingItem(
                    title = if (selectedLanguage == "العربية") "المصادقة البيومترية" else "Biometric Authentication",
                    subtitle = if (selectedLanguage == "العربية") "استخدام بصمة الإصبع أو الوجه" else "Use fingerprint or face recognition",
                    icon = Icons.Default.Fingerprint,
                    checked = biometricEnabled,
                    onCheckedChange = { biometricEnabled = it }
                )
            }

            // Appearance
            item {
                SettingsSectionHeader(
                    title = if (selectedLanguage == "العربية") "المظهر" else "Appearance"
                )
            }

            item {
                SwitchSettingItem(
                    title = if (selectedLanguage == "العربية") "الوضع الليلي" else "Dark Mode",
                    subtitle = if (selectedLanguage == "العربية") "استخدام المظهر الداكن" else "Use dark theme",
                    icon = Icons.Default.DarkMode,
                    checked = darkModeEnabled,
                    onCheckedChange = { darkModeEnabled = it }
                )
            }

            // Account
            item {
                SettingsSectionHeader(
                    title = if (selectedLanguage == "العربية") "الحساب" else "Account"
                )
            }

            items(getAccountSettings(selectedLanguage)) { setting ->
                ClickableSettingItem(setting)
            }

            // About
            item {
                SettingsSectionHeader(
                    title = if (selectedLanguage == "العربية") "حول التطبيق" else "About"
                )
            }

            items(getAboutSettings(selectedLanguage)) { setting ->
                ClickableSettingItem(setting)
            }
        }
    }
}

@Composable
fun SettingsSectionHeader(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.Bold,
        color = MaterialTheme.colorScheme.primary,
        modifier = Modifier.padding(vertical = 8.dp)
    )
}

@Composable
fun LanguageSettingItem(
    selectedLanguage: String,
    onLanguageChanged: (String) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    val languages = listOf("English", "العربية")

    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        imageVector = Icons.Default.Language,
                        contentDescription = "Language",
                        modifier = Modifier.size(24.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Column {
                        Text(
                            text = if (selectedLanguage == "العربية") "اللغة" else "Language",
                            style = MaterialTheme.typography.bodyLarge,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = selectedLanguage,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                IconButton(onClick = { expanded = !expanded }) {
                    Icon(
                        imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = "Expand"
                    )
                }
            }

            if (expanded) {
                Spacer(modifier = Modifier.height(8.dp))
                languages.forEach { language ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedLanguage == language,
                            onClick = {
                                onLanguageChanged(language)
                                expanded = false
                            }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = language,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SwitchSettingItem(
    title: String,
    subtitle: String,
    icon: ImageVector,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Switch(
                checked = checked,
                onCheckedChange = onCheckedChange
            )
        }
    }
}

@Composable
fun ClickableSettingItem(setting: SettingItem) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = { /* Handle click */ }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = setting.icon,
                    contentDescription = setting.title,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = setting.title,
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium
                    )
                    setting.subtitle?.let {
                        Text(
                            text = it,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "Navigate",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

data class SettingItem(
    val title: String,
    val subtitle: String? = null,
    val icon: ImageVector
)

fun getAccountSettings(language: String): List<SettingItem> {
    return if (language == "العربية") {
        listOf(
            SettingItem("الملف الشخصي", "عرض وتعديل المعلومات الشخصية", Icons.Default.Person),
            SettingItem("كلمة المرور", "تغيير كلمة المرور", Icons.Default.Lock),
            SettingItem("تسجيل الخروج", "الخروج من الحساب", Icons.Default.Logout)
        )
    } else {
        listOf(
            SettingItem("Profile", "View and edit personal information", Icons.Default.Person),
            SettingItem("Password", "Change your password", Icons.Default.Lock),
            SettingItem("Sign Out", "Sign out of your account", Icons.Default.Logout)
        )
    }
}

fun getAboutSettings(language: String): List<SettingItem> {
    return if (language == "العربية") {
        listOf(
            SettingItem("إصدار التطبيق", "1.0.0", Icons.Default.Info),
            SettingItem("الشروط والأحكام", null, Icons.Default.Description),
            SettingItem("سياسة الخصوصية", null, Icons.Default.PrivacyTip),
            SettingItem("اتصل بنا", "<EMAIL>", Icons.Default.ContactSupport)
        )
    } else {
        listOf(
            SettingItem("App Version", "1.0.0", Icons.Default.Info),
            SettingItem("Terms & Conditions", null, Icons.Default.Description),
            SettingItem("Privacy Policy", null, Icons.Default.PrivacyTip),
            SettingItem("Contact Us", "<EMAIL>", Icons.Default.ContactSupport)
        )
    }
}
