package com.aljameia.app.data.dao

import androidx.room.*
import com.aljameia.app.data.entities.User
import kotlinx.coroutines.flow.Flow

@Dao
interface UserDao {
    
    @Query("SELECT * FROM users WHERE id = :userId")
    suspend fun getUserById(userId: String): User?
    
    @Query("SELECT * FROM users WHERE email = :email")
    suspend fun getUserByEmail(email: String): User?
    
    @Query("SELECT * FROM users WHERE membershipNumber = :membershipNumber")
    suspend fun getUserByMembershipNumber(membershipNumber: String): User?
    
    @Query("SELECT * FROM users WHERE isActive = 1")
    fun getActiveUsers(): Flow<List<User>>
    
    @Query("SELECT * FROM users WHERE membershipType = :type")
    fun getUsersByMembershipType(type: String): Flow<List<User>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: User)
    
    @Update
    suspend fun updateUser(user: User)
    
    @Delete
    suspend fun deleteUser(user: User)
    
    @Query("UPDATE users SET lastLoginDate = :loginDate WHERE id = :userId")
    suspend fun updateLastLoginDate(userId: String, loginDate: java.util.Date)
    
    @Query("UPDATE users SET notificationsEnabled = :enabled WHERE id = :userId")
    suspend fun updateNotificationSettings(userId: String, enabled: Boolean)
    
    @Query("UPDATE users SET biometricEnabled = :enabled WHERE id = :userId")
    suspend fun updateBiometricSettings(userId: String, enabled: Boolean)
    
    @Query("UPDATE users SET preferredLanguage = :language WHERE id = :userId")
    suspend fun updatePreferredLanguage(userId: String, language: String)
    
    @Query("DELETE FROM users")
    suspend fun deleteAllUsers()
}
