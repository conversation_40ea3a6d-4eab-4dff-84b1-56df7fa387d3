# دليل النشر - منصة الجمعية

## نظرة عامة

هذا الدليل يوضح كيفية نشر منصة الجمعية في بيئات مختلفة من التطوير إلى الإنتاج.

## متطلبات النظام

### الحد الأدنى للمتطلبات
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **Network**: 100 Mbps

### المتطلبات الموصى بها للإنتاج
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **Network**: 1 Gbps
- **Load Balancer**: Nginx/HAProxy
- **CDN**: CloudFlare/AWS CloudFront

## البيئات المختلفة

### 1. بيئة التطوير (Development)

#### الإعداد السريع
```bash
# استنساخ المشروع
git clone https://github.com/aljameia/platform.git
cd platform

# إعداد البيئة
make dev-setup

# تحرير متغيرات البيئة
nano .env

# بدء التشغيل
make dev-up
```

#### متغيرات البيئة للتطوير
```env
NODE_ENV=development
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=password123
REDIS_PASSWORD=redis_dev_password
JWT_SECRET=dev-jwt-secret-not-for-production
SMTP_HOST=mailhog
SMTP_PORT=1025
```

#### الخدمات المتاحة
- Backend API: http://localhost:3000
- Admin Dashboard: http://localhost:3001
- MailHog: http://localhost:8025
- Redis Commander: http://localhost:8081
- Mongo Express: http://localhost:8082

### 2. بيئة الاختبار (Staging)

#### إعداد بيئة الاختبار
```bash
# إنشاء ملف البيئة للاختبار
cp .env.example .env.staging

# تحديث المتغيرات
nano .env.staging

# بدء بيئة الاختبار
docker-compose --env-file .env.staging -f docker-compose.yml -f docker-compose.staging.yml up -d
```

#### متغيرات البيئة للاختبار
```env
NODE_ENV=staging
MONGO_ROOT_USERNAME=staging_admin
MONGO_ROOT_PASSWORD=secure_staging_password
REDIS_PASSWORD=secure_redis_staging_password
JWT_SECRET=staging-jwt-secret-change-this
FRONTEND_URL=https://staging.aljameia.com
ADMIN_URL=https://staging-admin.aljameia.com
```

### 3. بيئة الإنتاج (Production)

#### الإعداد المسبق

##### 1. إعداد الخادم
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# تثبيت Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# إنشاء مستخدم للتطبيق
sudo useradd -m -s /bin/bash aljameia
sudo usermod -aG docker aljameia
```

##### 2. إعداد SSL
```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx -y

# الحصول على شهادات SSL
sudo certbot --nginx -d aljameia.com -d www.aljameia.com -d api.aljameia.com -d admin.aljameia.com
```

##### 3. إعداد Firewall
```bash
# تفعيل UFW
sudo ufw enable

# السماح بالمنافذ الضرورية
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS

# منع المنافذ الداخلية من الخارج
sudo ufw deny 3000/tcp   # Backend
sudo ufw deny 3001/tcp   # Admin
sudo ufw deny 27017/tcp  # MongoDB
sudo ufw deny 6379/tcp   # Redis
```

#### النشر في الإنتاج

##### 1. إعداد متغيرات البيئة
```bash
# إنشاء ملف البيئة
sudo -u aljameia cp .env.example .env.production

# تحديث المتغيرات (استخدم كلمات مرور قوية)
sudo -u aljameia nano .env.production
```

##### متغيرات البيئة الحرجة للإنتاج
```env
NODE_ENV=production
MONGO_ROOT_USERNAME=prod_admin_user
MONGO_ROOT_PASSWORD=very_secure_mongodb_password_2024
REDIS_PASSWORD=very_secure_redis_password_2024
JWT_SECRET=super-secure-jwt-secret-256-bit-key-production
JWT_REFRESH_SECRET=super-secure-refresh-secret-256-bit-key-production

# URLs
FRONTEND_URL=https://aljameia.com
ADMIN_URL=https://admin.aljameia.com
API_URL=https://api.aljameia.com

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=app_specific_password

# SMS
SMS_PROVIDER_URL=https://api.sms-provider.com/v1/send
SMS_PROVIDER_KEY=production_sms_api_key

# Payment Gateways
MADA_MERCHANT_ID=your_mada_merchant_id
MADA_SECRET_KEY=your_mada_secret_key
STC_PAY_MERCHANT_ID=your_stc_pay_merchant_id
STC_PAY_SECRET_KEY=your_stc_pay_secret_key

# AWS S3
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET=aljameia-files-production
S3_BACKUP_BUCKET=aljameia-backups-production

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
```

##### 2. بناء ونشر التطبيق
```bash
# التبديل للمستخدم
sudo su - aljameia

# استنساخ المشروع
git clone https://github.com/aljameia/platform.git
cd platform

# بناء صور الإنتاج
make prod-build

# بدء الخدمات
make prod-up

# التحقق من الحالة
make health
```

##### 3. إعداد النسخ الاحتياطي التلقائي
```bash
# إضافة مهمة cron للنسخ الاحتياطي
crontab -e

# إضافة السطر التالي للنسخ الاحتياطي اليومي في الساعة 2 صباحاً
0 2 * * * cd /home/<USER>/platform && make db-backup
```

## إعداد Load Balancer

### استخدام Nginx كـ Load Balancer

```nginx
upstream backend_servers {
    least_conn;
    server ***********:3000 max_fails=3 fail_timeout=30s;
    server ***********:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 443 ssl http2;
    server_name api.aljameia.com;
    
    location / {
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## المراقبة والصيانة

### 1. مراقبة الخدمات
```bash
# التحقق من حالة الخدمات
make status

# مراقبة السجلات
make logs

# فحص صحة النظام
make health
```

### 2. النسخ الاحتياطي والاستعادة
```bash
# إنشاء نسخة احتياطية
make db-backup

# استعادة من نسخة احتياطية
make db-restore BACKUP_FILE=backup_20240101_020000.tar.gz
```

### 3. التحديثات
```bash
# تحديث الكود
git pull origin main

# إعادة بناء الصور
make prod-build

# إعادة تشغيل الخدمات
make prod-restart
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. فشل الاتصال بقاعدة البيانات
```bash
# التحقق من حالة MongoDB
docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')"

# إعادة تشغيل MongoDB
docker-compose restart mongodb
```

#### 2. مشاكل الذاكرة
```bash
# مراقبة استخدام الذاكرة
docker stats

# تنظيف الذاكرة
make clean
```

#### 3. مشاكل SSL
```bash
# تجديد شهادات SSL
sudo certbot renew

# إعادة تحميل Nginx
docker-compose exec nginx nginx -s reload
```

## الأمان في الإنتاج

### 1. تأمين قاعدة البيانات
- استخدام كلمات مرور قوية
- تفعيل المصادقة
- تقييد الوصول للشبكة
- تشفير البيانات

### 2. تأمين Redis
- تفعيل كلمة المرور
- تقييد الوصول للشبكة
- تشفير الاتصالات

### 3. تأمين التطبيق
- استخدام HTTPS فقط
- تفعيل HSTS
- إعداد CSP headers
- تحديث الاعتمادات بانتظام

### 4. مراقبة الأمان
- مراجعة السجلات بانتظام
- إعداد تنبيهات الأمان
- فحص الثغرات الأمنية
- النسخ الاحتياطي المنتظم

## الأداء والتحسين

### 1. تحسين قاعدة البيانات
- إنشاء فهارس مناسبة
- تحسين الاستعلامات
- مراقبة الأداء
- تنظيف البيانات القديمة

### 2. تحسين التخزين المؤقت
- إعداد Redis بشكل صحيح
- استخدام CDN
- ضغط الاستجابات
- تحسين الصور

### 3. مراقبة الأداء
- استخدام أدوات المراقبة
- تتبع المقاييس الرئيسية
- إعداد التنبيهات
- تحليل الأداء بانتظام

## الدعم والمساعدة

للحصول على المساعدة:
- **الوثائق**: [docs.aljameia.com](https://docs.aljameia.com)
- **الدعم الفني**: [<EMAIL>](mailto:<EMAIL>)
- **المجتمع**: [community.aljameia.com](https://community.aljameia.com)
- **التقارير الأمنية**: [<EMAIL>](mailto:<EMAIL>)
