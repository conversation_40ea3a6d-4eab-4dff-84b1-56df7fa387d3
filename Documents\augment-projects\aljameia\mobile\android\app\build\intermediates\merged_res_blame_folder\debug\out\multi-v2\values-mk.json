{"logs": [{"outputFile": "com.aljameia.app-mergeDebugResources-104:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\85fae80ff7869a8da6251422548fb462\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,997,1067,1151,1240,1312,1393,1464", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,992,1062,1146,1235,1307,1388,1459,1580"}, "to": {"startLines": "52,53,106,108,110,128,129,188,189,190,191,193,194,197,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4946,5050,11280,11469,11645,13586,13663,18496,18588,18672,18743,18898,18982,19244,19609,19690,19761", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "5045,5135,11371,11567,11725,13658,13748,18583,18667,18738,18808,18977,19066,19311,19685,19756,19877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\523b9fad2b83d89c2b74860689d8fb91\\transformed\\biometric-1.1.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,265,386,516,640,769,887,1022,1116,1246,1373", "endColumns": "116,92,120,129,123,128,117,134,93,129,126,122", "endOffsets": "167,260,381,511,635,764,882,1017,1111,1241,1368,1491"}, "to": {"startLines": "73,107,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7459,11376,12285,12406,12536,12660,12789,12907,13042,13136,13266,13393", "endColumns": "116,92,120,129,123,128,117,134,93,129,126,122", "endOffsets": "7571,11464,12401,12531,12655,12784,12902,13037,13131,13261,13388,13511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c514f303830aa824ccabc1313adde6e2\\transformed\\material3-1.1.2\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,284,396,510,586,677,786,915,1032,1176,1257,1352,1442,1534,1645,1767,1867,2008,2148,2286,2454,2581,2698,2822,2942,3033,3127,3249,3380,3476,3574,3683,3821,3968,4080,4177,4250,4327,4415,4497,4607,4691,4770,4867,4965,5058,5151,5235,5338,5434,5531,5660,5742,5849", "endColumns": "114,113,111,113,75,90,108,128,116,143,80,94,89,91,110,121,99,140,139,137,167,126,116,123,119,90,93,121,130,95,97,108,137,146,111,96,72,76,87,81,109,83,78,96,97,92,92,83,102,95,96,128,81,106,98", "endOffsets": "165,279,391,505,581,672,781,910,1027,1171,1252,1347,1437,1529,1640,1762,1862,2003,2143,2281,2449,2576,2693,2817,2937,3028,3122,3244,3375,3471,3569,3678,3816,3963,4075,4172,4245,4322,4410,4492,4602,4686,4765,4862,4960,5053,5146,5230,5333,5429,5526,5655,5737,5844,5943"}, "to": {"startLines": "33,34,35,36,54,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,109,112,195,198,200,204,205,206,207,208,209,210,211,212,213,214,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3036,3151,3265,3377,5140,7689,7780,7889,8018,8135,8279,8360,8455,8545,8637,8748,8870,8970,9111,9251,9389,9557,9684,9801,9925,10045,10136,10230,10352,10483,10579,10677,10786,10924,11071,11183,11572,11796,19071,19316,19499,19882,19966,20045,20142,20240,20333,20426,20510,20613,20709,20806,20935,21017,21124", "endColumns": "114,113,111,113,75,90,108,128,116,143,80,94,89,91,110,121,99,140,139,137,167,126,116,123,119,90,93,121,130,95,97,108,137,146,111,96,72,76,87,81,109,83,78,96,97,92,92,83,102,95,96,128,81,106,98", "endOffsets": "3146,3260,3372,3486,5211,7775,7884,8013,8130,8274,8355,8450,8540,8632,8743,8865,8965,9106,9246,9384,9552,9679,9796,9920,10040,10131,10225,10347,10478,10574,10672,10781,10919,11066,11178,11275,11640,11868,19154,19393,19604,19961,20040,20137,20235,20328,20421,20505,20608,20704,20801,20930,21012,21119,21218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d2a56f6da0e2e4ab4da1b326f38c566a\\transformed\\material-1.11.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,611,700,796,920,1007,1073,1164,1234,1298,1401,1464,1529,1589,1657,1720,1775,1903,1960,2022,2077,2152,2292,2379,2462,2595,2677,2762,2908,2995,3072,3126,3181,3247,3320,3396,3485,3563,3636,3712,3787,3857,3966,4054,4129,4221,4313,4387,4461,4553,4606,4688,4755,4838,4925,4987,5051,5114,5184,5298,5413,5515,5627,5685,5744", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,145,86,76,53,54,65,72,75,88,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84", "endOffsets": "266,347,427,509,606,695,791,915,1002,1068,1159,1229,1293,1396,1459,1524,1584,1652,1715,1770,1898,1955,2017,2072,2147,2287,2374,2457,2590,2672,2757,2903,2990,3067,3121,3176,3242,3315,3391,3480,3558,3631,3707,3782,3852,3961,4049,4124,4216,4308,4382,4456,4548,4601,4683,4750,4833,4920,4982,5046,5109,5179,5293,5408,5510,5622,5680,5739,5824"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3491,3572,3652,3734,3831,4639,4735,4859,11730,11873,13516,13753,13817,13920,13983,14048,14108,14176,14239,14294,14422,14479,14541,14596,14671,14811,14898,14981,15114,15196,15281,15427,15514,15591,15645,15700,15766,15839,15915,16004,16082,16155,16231,16306,16376,16485,16573,16648,16740,16832,16906,16980,17072,17125,17207,17274,17357,17444,17506,17570,17633,17703,17817,17932,18034,18146,18204,18813", "endLines": "5,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,145,86,76,53,54,65,72,75,88,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84", "endOffsets": "316,3567,3647,3729,3826,3915,4730,4854,4941,11791,11959,13581,13812,13915,13978,14043,14103,14171,14234,14289,14417,14474,14536,14591,14666,14806,14893,14976,15109,15191,15276,15422,15509,15586,15640,15695,15761,15834,15910,15999,16077,16150,16226,16301,16371,16480,16568,16643,16735,16827,16901,16975,17067,17120,17202,17269,17352,17439,17501,17565,17628,17698,17812,17927,18029,18141,18199,18258,18893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5081b8eacd451e5b41f2b566f41c579e\\transformed\\core-1.12.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "42,43,44,45,46,47,48,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3920,4018,4120,4217,4315,4420,4523,19398", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "4013,4115,4212,4310,4415,4518,4634,19494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1c0b3a48f4d4c74d31971bddddc3d282\\transformed\\play-services-base-18.1.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,688,829,958,1074,1180,1333,1436,1598,1727,1876,2031,2096,2156", "endColumns": "102,156,128,105,140,128,115,105,152,102,161,128,148,154,64,59,74", "endOffsets": "295,452,581,687,828,957,1073,1179,1332,1435,1597,1726,1875,2030,2095,2155,2230"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5216,5323,5484,5617,5727,5872,6005,6125,6372,6529,6636,6802,6935,7088,7247,7316,7380", "endColumns": "106,160,132,109,144,132,119,109,156,106,165,132,152,158,68,63,78", "endOffsets": "5318,5479,5612,5722,5867,6000,6120,6230,6524,6631,6797,6930,7083,7242,7311,7375,7454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\21cc0a6be925d3c2a0c747bb469e8733\\transformed\\appcompat-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,533,641,727,835,954,1038,1119,1210,1303,1399,1493,1593,1686,1781,1877,1968,2059,2146,2252,2358,2459,2566,2678,2782,2938,19159", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "424,528,636,722,830,949,1033,1114,1205,1298,1394,1488,1588,1681,1776,1872,1963,2054,2141,2247,2353,2454,2561,2673,2777,2933,3031,19239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\cdc55fe2502a063bcee343c082439e6f\\transformed\\navigation-ui-2.7.6\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,127", "endOffsets": "155,283"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "18263,18368", "endColumns": "104,127", "endOffsets": "18363,18491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f1a3dac0af0a85f165dc53a6762b4a81\\transformed\\play-services-basement-18.2.0\\res\\values-mk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "132", "endOffsets": "327"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6235", "endColumns": "136", "endOffsets": "6367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b3e74a7425b14daa5ccd7c3c1ea8df2a\\transformed\\browser-1.4.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,273,388", "endColumns": "112,104,114,100", "endOffsets": "163,268,383,484"}, "to": {"startLines": "74,114,115,116", "startColumns": "4,4,4,4", "startOffsets": "7576,11964,12069,12184", "endColumns": "112,104,114,100", "endOffsets": "7684,12064,12179,12280"}}]}]}