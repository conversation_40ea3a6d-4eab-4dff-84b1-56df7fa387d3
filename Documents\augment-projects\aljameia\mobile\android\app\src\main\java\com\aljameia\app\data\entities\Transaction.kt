package com.aljameia.app.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey
import java.math.BigDecimal
import java.util.Date

@Entity(
    tableName = "transactions",
    foreignKeys = [
        ForeignKey(
            entity = User::class,
            parentColumns = ["id"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
data class Transaction(
    @PrimaryKey
    val id: String,
    val userId: String,
    val type: TransactionType,
    val category: String,
    val amount: BigDecimal,
    val currency: String = "SAR", // Saudi Riyal
    val description: String,
    val status: TransactionStatus,
    val paymentMethod: PaymentMethod?,
    val referenceNumber: String?,
    val receiptUrl: String?,
    val dueDate: Date?,
    val paidDate: Date?,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)

enum class TransactionType {
    MEMBERSHIP_FEE,
    DONATION,
    EVENT_PAYMENT,
    SERVICE_FEE,
    PENALTY,
    REFUND,
    OTHER
}

enum class TransactionStatus {
    PENDING,
    PROCESSING,
    COMPLETED,
    FAILED,
    CANCELLED,
    REFUNDED
}

enum class PaymentMethod {
    CASH,
    BANK_TRANSFER,
    CREDIT_CARD,
    DEBIT_CARD,
    DIGITAL_WALLET,
    CHECK
}
