import React from 'react';
import { Text } from 'react-native';

// Mock Ionicons component
export const Ionicons = ({ name, size, color, style, ...props }) => {
  return (
    <Text
      style={[{ fontSize: size, color }, style]}
      testID={`icon-${name}`}
      {...props}
    >
      {name}
    </Text>
  );
};

// Mock other icon families if needed
export const MaterialIcons = Ionicons;
export const FontAwesome = Ionicons;
export const AntDesign = Ionicons;
export const Entypo = Ionicons;
export const EvilIcons = Ionicons;
export const Feather = Ionicons;
export const Foundation = Ionicons;
export const MaterialCommunityIcons = Ionicons;
export const Octicons = Ionicons;
export const SimpleLineIcons = Ionicons;
export const Zocial = Ionicons;

export default {
  Ionicons,
  MaterialIcons,
  FontAwesome,
  AntDesign,
  Entypo,
  EvilIcons,
  Feather,
  Foundation,
  MaterialCommunityIcons,
  Octicons,
  SimpleLineIcons,
  Zocial,
};
