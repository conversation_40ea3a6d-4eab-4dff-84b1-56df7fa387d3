import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useApp } from '../../contexts/AppContext';
import { paymentService, formatCurrency } from '../../services';

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  type: 'card' | 'wallet' | 'bank';
  fees: number;
  processingTime: string;
}

export default function PaymentScreen() {
  const { createPayment } = useApp();
  const [amount, setAmount] = useState('');
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [description, setDescription] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);

  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    try {
      const methods = await paymentService.getPaymentMethods();
      setPaymentMethods(methods);
    } catch (error) {
      console.error('Failed to load payment methods:', error);
      // Use fallback data
      setPaymentMethods([
        {
          id: 'mada',
          name: 'مدى',
          icon: 'card-outline',
          type: 'card',
          fees: 0,
          processingTime: 'فوري',
        },
        {
          id: 'stc_pay',
          name: 'STC Pay',
          icon: 'phone-portrait-outline',
          type: 'wallet',
          fees: 0,
          processingTime: 'فوري',
        },
        {
          id: 'apple_pay',
          name: 'Apple Pay',
          icon: 'logo-apple',
          type: 'wallet',
          fees: 0,
          processingTime: 'فوري',
        },
        {
          id: 'bank_transfer',
          name: 'تحويل بنكي',
          icon: 'business-outline',
          type: 'bank',
          fees: 0,
          processingTime: '1-3 أيام',
        },
      ]);
    }
  };





  const calculateTotal = () => {
    const baseAmount = parseFloat(amount) || 0;
    const selectedMethodData = paymentMethods.find(m => m.id === selectedMethod);
    const fees = selectedMethodData ? (baseAmount * selectedMethodData.fees / 100) : 0;
    return baseAmount + fees;
  };

  const handlePayment = async () => {
    if (!selectedMethod || !amount || parseFloat(amount) <= 0) {
      Alert.alert('خطأ', 'يرجى اختيار طريقة الدفع وإدخال المبلغ');
      return;
    }

    try {
      setIsProcessing(true);

      await createPayment({
        amount: parseFloat(amount),
        currency: 'SAR',
        method: selectedMethod,
        description: description || 'دفعة عامة',
      });

      setShowSuccess(true);

      // Reset form after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
        setAmount('');
        setDescription('');
        setSelectedMethod('');
      }, 3000);
    } catch (error) {
      Alert.alert('خطأ', 'فشل في معالجة الدفعة');
    } finally {
      setIsProcessing(false);
    }
  };

  const renderPaymentMethod = (method: PaymentMethod) => (
    <TouchableOpacity
      key={method.id}
      style={[
        styles.methodCard,
        selectedMethod === method.id && styles.selectedMethod
      ]}
      onPress={() => setSelectedMethod(method.id)}
    >
      <View style={styles.methodInfo}>
        <Ionicons 
          name={method.icon as any} 
          size={24} 
          color={selectedMethod === method.id ? '#3B82F6' : '#6B7280'} 
        />
        <View style={styles.methodDetails}>
          <Text style={[
            styles.methodName,
            selectedMethod === method.id && styles.selectedText
          ]}>
            {method.name}
          </Text>
          <Text style={styles.methodMeta}>
            {method.fees > 0 ? `${method.fees}% رسوم` : 'بدون رسوم'} • {method.processingTime}
          </Text>
        </View>
      </View>
      {selectedMethod === method.id && (
        <Ionicons name="checkmark-circle" size={24} color="#3B82F6" />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>دفع مساهمة</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Amount Input */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>المبلغ (ريال سعودي)</Text>
          <View style={styles.amountContainer}>
            <TextInput
              style={styles.amountInput}
              value={amount}
              onChangeText={setAmount}
              placeholder="0"
              keyboardType="numeric"
              textAlign="center"
            />
            <Text style={styles.currencyLabel}>ريال</Text>
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>وصف الدفعة</Text>
          <TextInput
            style={styles.descriptionInput}
            value={description}
            onChangeText={setDescription}
            placeholder="مثال: دفعة جمعية الأصدقاء - يونيو 2024"
            multiline
          />
        </View>

        {/* Payment Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>طريقة الدفع</Text>
          <View style={styles.methodsContainer}>
            {paymentMethods.map(renderPaymentMethod)}
          </View>
        </View>

        {/* Payment Summary */}
        {amount && selectedMethod && (
          <View style={styles.summaryContainer}>
            <Text style={styles.summaryTitle}>ملخص الدفعة</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>المبلغ الأساسي:</Text>
              <Text style={styles.summaryValue}>{formatCurrency(amount)}</Text>
            </View>
            {calculateTotal() - parseFloat(amount) > 0 && (
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>الرسوم:</Text>
                <Text style={styles.summaryValue}>
                  {formatCurrency((calculateTotal() - parseFloat(amount)).toString())}
                </Text>
              </View>
            )}
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>المجموع:</Text>
              <Text style={styles.totalValue}>{formatCurrency(calculateTotal().toString())}</Text>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Pay Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.payButton,
            (!selectedMethod || !amount || parseFloat(amount) <= 0) && styles.disabledButton
          ]}
          onPress={handlePayment}
          disabled={!selectedMethod || !amount || parseFloat(amount) <= 0 || isProcessing}
        >
          {isProcessing ? (
            <View style={styles.processingContainer}>
              <Text style={styles.processingText}>جاري المعالجة...</Text>
            </View>
          ) : (
            <>
              <Ionicons name="card-outline" size={20} color="white" />
              <Text style={styles.payButtonText}>
                ادفع {amount ? formatCurrency(calculateTotal().toString()) : ''}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Success Modal */}
      <Modal
        visible={showSuccess}
        transparent
        animationType="fade"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.successModal}>
            <Ionicons name="checkmark-circle" size={64} color="#10B981" />
            <Text style={styles.successTitle}>تم الدفع بنجاح!</Text>
            <Text style={styles.successMessage}>
              تم معالجة دفعتك وستظهر في سجل المعاملات
            </Text>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#3B82F6',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
    textAlign: 'right',
  },
  amountContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  amountInput: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    minWidth: 100,
  },
  currencyLabel: {
    fontSize: 18,
    color: '#6B7280',
    marginLeft: 10,
  },
  descriptionInput: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    textAlignVertical: 'top',
    minHeight: 80,
  },
  methodsContainer: {
    gap: 12,
  },
  methodCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedMethod: {
    borderColor: '#3B82F6',
    backgroundColor: '#EFF6FF',
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  methodDetails: {
    marginLeft: 12,
  },
  methodName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  selectedText: {
    color: '#3B82F6',
  },
  methodMeta: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
  },
  summaryContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginTop: 8,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
    textAlign: 'right',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 12,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3B82F6',
  },
  footer: {
    padding: 20,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  payButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#9CA3AF',
  },
  payButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  processingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  processingText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  successModal: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 40,
    alignItems: 'center',
    marginHorizontal: 40,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
  },
  successMessage: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});
