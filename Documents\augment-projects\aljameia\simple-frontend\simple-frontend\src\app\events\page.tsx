'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import {
  CalendarIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  UserGroupIcon,
  MapPinIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  VideoCameraIcon,
  BuildingOfficeIcon,
  GlobeAltIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import type { Event, EventType, EventCategory, EventStatus } from '@/types/event';

export default function EventsPage() {
  const { user } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<EventType | 'all'>('all');
  const [selectedCategory, setSelectedCategory] = useState<EventCategory | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = useState<EventStatus | 'all'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  const eventsPerPage = 10;

  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const mockEvents: Event[] = [
    {
      id: '1',
      title: 'الاجتماع الشهري لمجلس الإدارة',
      description: 'اجتماع شهري لمناقشة أعمال الجمعية والقرارات المهمة',
      type: 'meeting',
      category: 'board_meeting',
      status: 'published',
      priority: 'high',
      startDate: '2024-06-25',
      endDate: '2024-06-25',
      startTime: '10:00',
      endTime: '12:00',
      timezone: 'Asia/Riyadh',
      isAllDay: false,
      isRecurring: true,
      location: {
        type: 'physical',
        name: 'قاعة المؤتمرات الرئيسية',
        address: 'شارع الملك فهد، حي العليا',
        city: 'الرياض',
        state: 'الرياض',
        country: 'السعودية',
        room: 'القاعة الكبرى',
        floor: 'الطابق الثاني',
        capacity: 50,
        facilities: ['بروجكتور', 'نظام صوتي', 'واي فاي', 'مكيف']
      },
      organizer: {
        id: 'admin',
        name: 'المدير العام',
        email: '<EMAIL>',
        phone: '+966501234567',
        organization: 'الجمعية',
        title: 'المدير العام'
      },
      speakers: [],
      attendees: [
        {
          id: '1',
          name: 'أحمد الراشد',
          email: '<EMAIL>',
          registrationDate: '2024-06-20T10:00:00Z',
          attendanceStatus: 'confirmed'
        },
        {
          id: '2',
          name: 'سارة العتيبي',
          email: '<EMAIL>',
          registrationDate: '2024-06-21T14:30:00Z',
          attendanceStatus: 'registered'
        }
      ],
      maxAttendees: 15,
      registrationRequired: true,
      registrationDeadline: '2024-06-24',
      registrationFee: 0,
      currency: 'SAR',
      agenda: [
        {
          id: '1',
          title: 'افتتاح الاجتماع',
          type: 'presentation',
          startTime: '10:00',
          endTime: '10:15',
          duration: 15,
          isBreak: false,
          isOptional: false,
          registeredAttendees: []
        },
        {
          id: '2',
          title: 'مراجعة التقرير المالي',
          type: 'discussion',
          startTime: '10:15',
          endTime: '11:00',
          duration: 45,
          isBreak: false,
          isOptional: false,
          registeredAttendees: []
        }
      ],
      materials: [],
      requirements: ['إحضار الهوية الشخصية'],
      tags: ['اجتماع', 'مجلس إدارة', 'شهري'],
      visibility: 'members_only',
      permissions: {
        isPublic: false,
        allowedUsers: [],
        allowedRoles: ['admin', 'board_member'],
        allowedGroups: [],
        permissions: {
          view: true,
          register: true,
          edit: false,
          delete: false,
          manage: false,
          moderate: false
        }
      },
      notifications: [],
      feedback: [],
      photos: [],
      videos: [],
      documents: [],
      createdBy: 'admin',
      createdByName: 'المدير العام',
      createdAt: '2024-06-15T09:00:00Z',
      lastModified: '2024-06-18T16:30:00Z',
      lastModifiedBy: 'admin',
      lastModifiedByName: 'المدير العام',
      publishedAt: '2024-06-18T16:30:00Z'
    },
    {
      id: '2',
      title: 'ورشة تطوير المهارات القيادية',
      description: 'ورشة تدريبية لتطوير المهارات القيادية لأعضاء الجمعية',
      type: 'workshop',
      category: 'professional_development',
      status: 'registration_open',
      priority: 'medium',
      startDate: '2024-07-05',
      endDate: '2024-07-06',
      startTime: '09:00',
      endTime: '17:00',
      timezone: 'Asia/Riyadh',
      isAllDay: false,
      isRecurring: false,
      location: {
        type: 'hybrid',
        name: 'قاعة التدريب + منصة زووم',
        address: 'شارع الأمير سلطان، حي الملز',
        city: 'الرياض',
        state: 'الرياض',
        country: 'السعودية',
        room: 'قاعة التدريب الذكية',
        capacity: 30,
        facilities: ['بروجكتور', 'نظام صوتي', 'واي فاي', 'كاميرات'],
        virtualLink: 'https://zoom.us/j/123456789',
        virtualPlatform: 'Zoom',
        virtualPassword: 'leadership2024'
      },
      organizer: {
        id: 'trainer',
        name: 'د. محمد الخبير',
        email: '<EMAIL>',
        phone: '+966502345678',
        organization: 'معهد التطوير المهني',
        title: 'مدرب معتمد'
      },
      speakers: [
        {
          id: '1',
          name: 'د. فاطمة الاستشارية',
          title: 'خبيرة التطوير القيادي',
          organization: 'مركز القيادة المتقدمة',
          bio: 'خبيرة في التطوير القيادي مع أكثر من 15 سنة خبرة',
          topics: ['القيادة', 'التطوير المهني', 'إدارة الفرق'],
          sessionTitle: 'أساسيات القيادة الفعالة',
          sessionStartTime: '10:00',
          sessionEndTime: '12:00'
        }
      ],
      attendees: [],
      maxAttendees: 25,
      registrationRequired: true,
      registrationDeadline: '2024-07-01',
      registrationFee: 200,
      currency: 'SAR',
      agenda: [],
      materials: [],
      requirements: ['خبرة إدارية لا تقل عن سنتين'],
      tags: ['ورشة', 'تدريب', 'قيادة', 'تطوير'],
      visibility: 'public',
      permissions: {
        isPublic: true,
        allowedUsers: [],
        allowedRoles: [],
        allowedGroups: [],
        permissions: {
          view: true,
          register: true,
          edit: false,
          delete: false,
          manage: false,
          moderate: false
        }
      },
      notifications: [],
      feedback: [],
      photos: [],
      videos: [],
      documents: [],
      createdBy: 'admin',
      createdByName: 'المدير العام',
      createdAt: '2024-06-10T11:00:00Z',
      lastModified: '2024-06-18T14:20:00Z',
      lastModifiedBy: 'admin',
      lastModifiedByName: 'المدير العام',
      publishedAt: '2024-06-18T14:20:00Z'
    },
    {
      id: '3',
      title: 'المؤتمر السنوي للجمعية',
      description: 'المؤتمر السنوي لعرض إنجازات الجمعية ومناقشة الخطط المستقبلية',
      type: 'conference',
      category: 'annual_conference',
      status: 'draft',
      priority: 'high',
      startDate: '2024-09-15',
      endDate: '2024-09-16',
      startTime: '08:00',
      endTime: '18:00',
      timezone: 'Asia/Riyadh',
      isAllDay: false,
      isRecurring: false,
      location: {
        type: 'physical',
        name: 'فندق الريتز كارلتون',
        address: 'شارع الملك عبدالعزيز، حي المربع',
        city: 'الرياض',
        state: 'الرياض',
        country: 'السعودية',
        room: 'القاعة الكبرى',
        capacity: 500,
        facilities: ['مسرح', 'نظام صوتي متقدم', 'إضاءة احترافية', 'خدمة ضيافة']
      },
      organizer: {
        id: 'admin',
        name: 'المدير العام',
        email: '<EMAIL>',
        phone: '+966501234567',
        organization: 'الجمعية',
        title: 'المدير العام'
      },
      speakers: [],
      attendees: [],
      maxAttendees: 400,
      registrationRequired: true,
      registrationDeadline: '2024-09-01',
      registrationFee: 150,
      currency: 'SAR',
      agenda: [],
      materials: [],
      requirements: ['عضوية سارية المفعول'],
      tags: ['مؤتمر', 'سنوي', 'إنجازات', 'مستقبل'],
      visibility: 'members_only',
      permissions: {
        isPublic: false,
        allowedUsers: [],
        allowedRoles: ['member'],
        allowedGroups: [],
        permissions: {
          view: true,
          register: true,
          edit: false,
          delete: false,
          manage: false,
          moderate: false
        }
      },
      notifications: [],
      feedback: [],
      photos: [],
      videos: [],
      documents: [],
      createdBy: 'admin',
      createdByName: 'المدير العام',
      createdAt: '2024-06-01T08:00:00Z',
      lastModified: '2024-06-15T12:00:00Z',
      lastModifiedBy: 'admin',
      lastModifiedByName: 'المدير العام'
    }
  ];

  useEffect(() => {
    const loadEvents = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setEvents(mockEvents);
      setIsLoading(false);
    };

    loadEvents();
  }, []);

  // تصفية الفعاليات
  const filteredEvents = events.filter(event => {
    const matchesSearch = searchTerm === '' || 
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = selectedType === 'all' || event.type === selectedType;
    const matchesCategory = selectedCategory === 'all' || event.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || event.status === selectedStatus;
    
    return matchesSearch && matchesType && matchesCategory && matchesStatus;
  });

  // تقسيم الصفحات
  const totalPages = Math.ceil(filteredEvents.length / eventsPerPage);
  const startIndex = (currentPage - 1) * eventsPerPage;
  const paginatedEvents = filteredEvents.slice(startIndex, startIndex + eventsPerPage);

  const getStatusIcon = (status: EventStatus) => {
    switch (status) {
      case 'published':
      case 'registration_open':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-blue-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      case 'postponed':
        return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />;
      case 'draft':
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: EventStatus) => {
    switch (status) {
      case 'draft': return 'مسودة';
      case 'published': return 'منشور';
      case 'registration_open': return 'التسجيل مفتوح';
      case 'registration_closed': return 'التسجيل مغلق';
      case 'in_progress': return 'جاري';
      case 'completed': return 'مكتمل';
      case 'cancelled': return 'ملغي';
      case 'postponed': return 'مؤجل';
      default: return status;
    }
  };

  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'physical':
        return <BuildingOfficeIcon className="h-4 w-4 text-blue-500" />;
      case 'virtual':
        return <VideoCameraIcon className="h-4 w-4 text-green-500" />;
      case 'hybrid':
        return <GlobeAltIcon className="h-4 w-4 text-purple-500" />;
      default:
        return <MapPinIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute requiredPermissions={['events']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الفعاليات...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredPermissions={['events']}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <Link href="/">
                  <div className="flex items-center cursor-pointer">
                    <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">ج</span>
                    </div>
                    <span className="mr-3 text-xl font-bold text-gray-900">الجمعية</span>
                  </div>
                </Link>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <Link href="/dashboard">
                  <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                    <ArrowLeftIcon className="h-4 w-4 ml-2" />
                    لوحة التحكم
                  </button>
                </Link>
                <div className="text-sm text-gray-600">
                  مرحباً، {user?.name}
                </div>
              </div>
            </div>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CalendarIcon className="h-8 w-8 text-blue-600 ml-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">إدارة الفعاليات</h1>
                  <p className="text-gray-600">تنظيم وإدارة جميع فعاليات الجمعية</p>
                </div>
              </div>

              <Link href="/events/create">
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                  <PlusIcon className="h-5 w-5 ml-2" />
                  إنشاء فعالية جديدة
                </button>
              </Link>
            </div>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CalendarIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">{events.length}</p>
                  <p className="text-sm text-gray-600">إجمالي الفعاليات</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircleIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {events.filter(e => e.status === 'registration_open' || e.status === 'published').length}
                  </p>
                  <p className="text-sm text-gray-600">فعاليات نشطة</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <UserGroupIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {events.reduce((sum, e) => sum + e.attendees.length, 0)}
                  </p>
                  <p className="text-sm text-gray-600">إجمالي المسجلين</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <ClockIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {events.filter(e => e.status === 'draft').length}
                  </p>
                  <p className="text-sm text-gray-600">مسودات</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Events List */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            {paginatedEvents.map((event) => (
              <div
                key={event.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 ml-3">{event.title}</h3>
                      <div className="flex items-center">
                        {getStatusIcon(event.status)}
                        <span className="mr-1 text-sm text-gray-600">
                          {getStatusText(event.status)}
                        </span>
                      </div>
                    </div>

                    <p className="text-gray-600 mb-4">{event.description}</p>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <CalendarIcon className="h-4 w-4 ml-2" />
                        <span>
                          {new Date(event.startDate).toLocaleDateString('ar-SA')}
                          {event.startDate !== event.endDate && (
                            <> - {new Date(event.endDate).toLocaleDateString('ar-SA')}</>
                          )}
                        </span>
                      </div>

                      <div className="flex items-center text-sm text-gray-500">
                        <ClockIcon className="h-4 w-4 ml-2" />
                        <span>{event.startTime} - {event.endTime}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-500">
                        {getLocationIcon(event.location.type)}
                        <span className="mr-1">{event.location.name}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                        <div className="flex items-center">
                          <UserGroupIcon className="h-4 w-4 ml-1" />
                          <span>{event.attendees.length}</span>
                          {event.maxAttendees && (
                            <span>/{event.maxAttendees}</span>
                          )}
                        </div>

                        {event.registrationFee && event.registrationFee > 0 && (
                          <div className="text-green-600 font-medium">
                            {event.registrationFee} {event.currency}
                          </div>
                        )}

                        {event.registrationFee === 0 && (
                          <div className="text-blue-600 font-medium">مجاني</div>
                        )}
                      </div>

                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Link href={`/events/${event.id}`}>
                          <button className="text-blue-600 hover:text-blue-900 p-2 rounded-lg hover:bg-blue-50">
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        </Link>
                        <Link href={`/events/${event.id}/edit`}>
                          <button className="text-green-600 hover:text-green-900 p-2 rounded-lg hover:bg-green-50">
                            <PencilIcon className="h-4 w-4" />
                          </button>
                        </Link>
                        <button className="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50">
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
