import { useTranslation } from 'next-i18next';
import Link from 'next/link';
import { 
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  UsersIcon,
  EyeIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  type: 'meeting' | 'workshop' | 'conference' | 'social' | 'training';
  registeredCount: number;
  maxCapacity: number;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  isRegistrationOpen: boolean;
}

const UpcomingEvents = () => {
  const { t } = useTranslation('dashboard');

  const events: Event[] = [
    {
      id: 'EVT-001',
      title: 'Annual General Meeting',
      description: 'Review of annual performance and strategic planning for next year',
      date: '2024-06-25',
      time: '14:00',
      location: 'Main Conference Hall',
      type: 'meeting',
      registeredCount: 45,
      maxCapacity: 100,
      status: 'upcoming',
      isRegistrationOpen: true
    },
    {
      id: 'EVT-002',
      title: 'Financial Management Workshop',
      description: 'Learn best practices for personal and organizational financial management',
      date: '2024-06-28',
      time: '10:00',
      location: 'Training Room A',
      type: 'workshop',
      registeredCount: 28,
      maxCapacity: 30,
      status: 'upcoming',
      isRegistrationOpen: true
    },
    {
      id: 'EVT-003',
      title: 'Community Iftar',
      description: 'Join us for a community iftar during Ramadan',
      date: '2024-07-02',
      time: '18:30',
      location: 'Community Center',
      type: 'social',
      registeredCount: 120,
      maxCapacity: 150,
      status: 'upcoming',
      isRegistrationOpen: true
    },
    {
      id: 'EVT-004',
      title: 'Digital Transformation Conference',
      description: 'Exploring the future of digital transformation in associations',
      date: '2024-07-10',
      time: '09:00',
      location: 'Grand Auditorium',
      type: 'conference',
      registeredCount: 85,
      maxCapacity: 200,
      status: 'upcoming',
      isRegistrationOpen: true
    }
  ];

  const getEventTypeColor = (type: Event['type']) => {
    const colors = {
      meeting: 'bg-blue-100 text-blue-800',
      workshop: 'bg-green-100 text-green-800',
      conference: 'bg-purple-100 text-purple-800',
      social: 'bg-orange-100 text-orange-800',
      training: 'bg-indigo-100 text-indigo-800'
    };
    return colors[type] || colors.meeting;
  };

  const getEventTypeLabel = (type: Event['type']) => {
    const labels = {
      meeting: t('events.types.meeting'),
      workshop: t('events.types.workshop'),
      conference: t('events.types.conference'),
      social: t('events.types.social'),
      training: t('events.types.training')
    };
    return labels[type] || labels.meeting;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const getRegistrationStatus = (registered: number, capacity: number) => {
    const percentage = (registered / capacity) * 100;
    if (percentage >= 90) return { color: 'text-red-600', label: t('events.registration.almost_full') };
    if (percentage >= 70) return { color: 'text-yellow-600', label: t('events.registration.filling_up') };
    return { color: 'text-green-600', label: t('events.registration.available') };
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <CalendarIcon className="h-5 w-5 mr-2" />
            {t('events.upcoming_events')}
          </CardTitle>
          <Link href="/events/create">
            <Button size="sm">
              <PlusIcon className="h-4 w-4 mr-1" />
              {t('events.create_event')}
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {events.map((event) => {
            const registrationStatus = getRegistrationStatus(event.registeredCount, event.maxCapacity);
            
            return (
              <div
                key={event.id}
                className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-200"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-medium text-gray-900">{event.title}</h3>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getEventTypeColor(event.type)}`}>
                        {getEventTypeLabel(event.type)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{event.description}</p>
                  </div>
                  <Link href={`/events/${event.id}`}>
                    <Button variant="ghost" size="sm">
                      <EyeIcon className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    <span>{formatDate(event.date)}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <ClockIcon className="h-4 w-4 mr-2" />
                    <span>{event.time}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPinIcon className="h-4 w-4 mr-2" />
                    <span>{event.location}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <UsersIcon className="h-4 w-4 mr-2" />
                    <span>{event.registeredCount}/{event.maxCapacity} {t('events.registered')}</span>
                  </div>
                </div>

                {/* Registration Progress */}
                <div className="mb-3">
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span className="text-gray-600">{t('events.registration_progress')}</span>
                    <span className={registrationStatus.color}>
                      {registrationStatus.label}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(event.registeredCount / event.maxCapacity) * 100}%` }}
                    ></div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    {t('events.event_id')}: {event.id}
                  </div>
                  <div className="flex space-x-2">
                    {event.isRegistrationOpen && (
                      <Link href={`/events/${event.id}/register`}>
                        <Button size="sm" variant="outline">
                          {t('events.register')}
                        </Button>
                      </Link>
                    )}
                    <Link href={`/events/${event.id}/manage`}>
                      <Button size="sm" variant="ghost">
                        {t('events.manage')}
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* View All Events */}
        <div className="mt-6 text-center">
          <Link href="/events">
            <Button variant="outline" className="w-full">
              {t('events.view_all_events')}
            </Button>
          </Link>
        </div>

        {/* Quick Stats */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-lg font-semibold text-gray-900">
                {events.filter(e => e.status === 'upcoming').length}
              </p>
              <p className="text-xs text-gray-500">{t('events.stats.upcoming')}</p>
            </div>
            <div>
              <p className="text-lg font-semibold text-gray-900">
                {events.reduce((sum, e) => sum + e.registeredCount, 0)}
              </p>
              <p className="text-xs text-gray-500">{t('events.stats.total_registrations')}</p>
            </div>
            <div>
              <p className="text-lg font-semibold text-gray-900">
                {Math.round(events.reduce((sum, e) => sum + (e.registeredCount / e.maxCapacity), 0) / events.length * 100)}%
              </p>
              <p className="text-xs text-gray-500">{t('events.stats.avg_capacity')}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default UpcomingEvents;
