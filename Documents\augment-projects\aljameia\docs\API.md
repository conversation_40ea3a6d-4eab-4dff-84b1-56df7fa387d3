# دليل API - منصة الجمعية

## نظرة عامة

API منصة الجمعية هو RESTful API يوفر جميع الوظائف اللازمة لإدارة الجمعيات المالية والمدفوعات والمستخدمين.

## معلومات أساسية

- **Base URL**: `https://api.aljameia.com/api/v1`
- **Authentication**: Bearer <PERSON> (JWT)
- **Content-Type**: `application/json`
- **Rate Limiting**: 100 requests per 15 minutes per IP

## المصادقة

### تسجيل الدخول
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "name": "اسم المستخدم",
      "role": "user"
    },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token"
    }
  }
}
```

### تسجيل مستخدم جديد
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "اسم المستخدم",
  "phone": "+966501234567"
}
```

### تجديد الرمز المميز
```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "jwt_refresh_token"
}
```

### تسجيل الخروج
```http
POST /auth/logout
Authorization: Bearer jwt_access_token
```

## إدارة المستخدمين

### الحصول على بيانات المستخدم الحالي
```http
GET /auth/me
Authorization: Bearer jwt_access_token
```

### تحديث بيانات المستخدم
```http
PUT /users/profile
Authorization: Bearer jwt_access_token
Content-Type: application/json

{
  "name": "الاسم الجديد",
  "phone": "+966501234567",
  "preferences": {
    "language": "ar",
    "currency": "SAR",
    "notifications": {
      "email": true,
      "sms": true,
      "push": true
    }
  }
}
```

### قائمة المستخدمين (للمديرين فقط)
```http
GET /users?page=1&limit=10&search=البحث&role=user
Authorization: Bearer jwt_access_token
```

## إدارة الجمعيات

### قائمة الجمعيات
```http
GET /jamiyas?page=1&limit=10&status=active&category=savings
Authorization: Bearer jwt_access_token
```

**Response:**
```json
{
  "success": true,
  "data": {
    "jamiyas": [
      {
        "id": "jamiya_id",
        "name": "جمعية الأصدقاء",
        "description": "جمعية مالية للأصدقاء",
        "totalAmount": 60000,
        "monthlyAmount": 2500,
        "duration": 24,
        "maxMembers": 24,
        "currentMembers": 15,
        "status": "active",
        "createdBy": "user_id",
        "startDate": "2024-01-01T00:00:00.000Z",
        "endDate": "2025-12-31T23:59:59.999Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "pages": 5
    }
  }
}
```

### إنشاء جمعية جديدة
```http
POST /jamiyas
Authorization: Bearer jwt_access_token
Content-Type: application/json

{
  "name": "جمعية العائلة",
  "description": "جمعية مالية لأفراد العائلة",
  "totalAmount": 120000,
  "monthlyAmount": 5000,
  "duration": 24,
  "maxMembers": 24,
  "categories": ["savings", "emergency"],
  "tags": ["عائلة", "ادخار"],
  "rules": {
    "latePaymentFee": 100,
    "gracePeriodDays": 5,
    "earlyWithdrawalPenalty": 0.03
  },
  "paymentSchedule": {
    "dayOfMonth": 15,
    "reminderDays": 5
  },
  "isPrivate": true
}
```

### الحصول على تفاصيل جمعية
```http
GET /jamiyas/:jamiyaId
Authorization: Bearer jwt_access_token
```

### تحديث جمعية
```http
PUT /jamiyas/:jamiyaId
Authorization: Bearer jwt_access_token
Content-Type: application/json

{
  "description": "وصف محدث للجمعية",
  "rules": {
    "latePaymentFee": 150,
    "gracePeriodDays": 3
  }
}
```

### حذف جمعية
```http
DELETE /jamiyas/:jamiyaId
Authorization: Bearer jwt_access_token
```

## إدارة الاشتراكات

### الانضمام لجمعية
```http
POST /subscriptions
Authorization: Bearer jwt_access_token
Content-Type: application/json

{
  "jamiyaId": "jamiya_id",
  "shares": 1,
  "inviteCode": "FAMILY24"
}
```

### قائمة اشتراكات المستخدم
```http
GET /subscriptions/my?status=active
Authorization: Bearer jwt_access_token
```

### تحديث إعدادات الاشتراك
```http
PUT /subscriptions/:subscriptionId
Authorization: Bearer jwt_access_token
Content-Type: application/json

{
  "settings": {
    "autoPayment": true,
    "reminderDays": 3,
    "preferredPaymentMethod": "mada"
  }
}
```

### إلغاء الاشتراك
```http
DELETE /subscriptions/:subscriptionId
Authorization: Bearer jwt_access_token
```

## إدارة المدفوعات

### إنشاء دفعة جديدة
```http
POST /payments
Authorization: Bearer jwt_access_token
Content-Type: application/json

{
  "subscriptionId": "subscription_id",
  "amount": 2500,
  "method": "mada",
  "description": "دفعة شهرية - يناير 2024"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "payment": {
      "id": "payment_id",
      "amount": 2500,
      "currency": "SAR",
      "method": "mada",
      "status": "pending",
      "transactionId": "TXN_123456789",
      "paymentUrl": "https://payment-gateway.com/pay/123456789"
    }
  }
}
```

### قائمة المدفوعات
```http
GET /payments?page=1&limit=10&status=completed&method=mada
Authorization: Bearer jwt_access_token
```

### تفاصيل دفعة
```http
GET /payments/:paymentId
Authorization: Bearer jwt_access_token
```

### تأكيد الدفعة
```http
POST /payments/:paymentId/confirm
Authorization: Bearer jwt_access_token
Content-Type: application/json

{
  "gatewayTransactionId": "gateway_txn_id",
  "gatewayResponse": {
    "status": "success",
    "reference": "REF123456"
  }
}
```

### طلب استرداد
```http
POST /payments/:paymentId/refund
Authorization: Bearer jwt_access_token
Content-Type: application/json

{
  "amount": 2500,
  "reason": "سبب الاسترداد"
}
```

## الإشعارات

### قائمة الإشعارات
```http
GET /notifications?page=1&limit=20&isRead=false&type=payment
Authorization: Bearer jwt_access_token
```

### تحديد إشعار كمقروء
```http
PUT /notifications/:notificationId/read
Authorization: Bearer jwt_access_token
```

### تحديد جميع الإشعارات كمقروءة
```http
PUT /notifications/mark-all-read
Authorization: Bearer jwt_access_token
```

## التقارير والإحصائيات

### إحصائيات المستخدم
```http
GET /reports/user-stats
Authorization: Bearer jwt_access_token
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalJamiyas": 3,
    "activeSubscriptions": 2,
    "totalPaid": 15000,
    "totalReceived": 60000,
    "successfulPayments": 6,
    "pendingPayments": 1
  }
}
```

### تقرير الجمعية
```http
GET /reports/jamiya/:jamiyaId
Authorization: Bearer jwt_access_token
```

### تقرير المدفوعات
```http
GET /reports/payments?startDate=2024-01-01&endDate=2024-12-31&format=json
Authorization: Bearer jwt_access_token
```

## رفع الملفات

### رفع ملف
```http
POST /uploads
Authorization: Bearer jwt_access_token
Content-Type: multipart/form-data

file: [binary data]
type: "avatar" | "document" | "receipt"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "file": {
      "id": "file_id",
      "filename": "avatar.jpg",
      "originalName": "my-avatar.jpg",
      "mimeType": "image/jpeg",
      "size": 1024000,
      "url": "https://files.aljameia.com/uploads/avatar.jpg"
    }
  }
}
```

## إدارة النظام (للمديرين)

### إحصائيات النظام
```http
GET /admin/stats
Authorization: Bearer jwt_access_token
```

### قائمة المستخدمين
```http
GET /admin/users?page=1&limit=50&role=user&status=active
Authorization: Bearer jwt_access_token
```

### تحديث دور المستخدم
```http
PUT /admin/users/:userId/role
Authorization: Bearer jwt_access_token
Content-Type: application/json

{
  "role": "moderator"
}
```

### تعطيل/تفعيل مستخدم
```http
PUT /admin/users/:userId/status
Authorization: Bearer jwt_access_token
Content-Type: application/json

{
  "isActive": false
}
```

## أكواد الأخطاء

| كود | الوصف |
|-----|--------|
| 200 | نجح الطلب |
| 201 | تم الإنشاء بنجاح |
| 400 | طلب غير صحيح |
| 401 | غير مصرح |
| 403 | ممنوع |
| 404 | غير موجود |
| 409 | تضارب |
| 422 | بيانات غير صحيحة |
| 429 | تجاوز الحد المسموح |
| 500 | خطأ في الخادم |

## أمثلة على الاستخدام

### JavaScript/Node.js
```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'https://api.aljameia.com/api/v1',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${accessToken}`
  }
});

// تسجيل الدخول
const login = async (email, password) => {
  try {
    const response = await api.post('/auth/login', {
      email,
      password
    });
    return response.data;
  } catch (error) {
    console.error('Login failed:', error.response.data);
  }
};

// الحصول على الجمعيات
const getJamiyas = async () => {
  try {
    const response = await api.get('/jamiyas');
    return response.data;
  } catch (error) {
    console.error('Failed to get jamiyas:', error.response.data);
  }
};
```

### Python
```python
import requests

class AljameiaAPI:
    def __init__(self, base_url, access_token=None):
        self.base_url = base_url
        self.session = requests.Session()
        if access_token:
            self.session.headers.update({
                'Authorization': f'Bearer {access_token}'
            })
    
    def login(self, email, password):
        response = self.session.post(
            f'{self.base_url}/auth/login',
            json={'email': email, 'password': password}
        )
        return response.json()
    
    def get_jamiyas(self, page=1, limit=10):
        response = self.session.get(
            f'{self.base_url}/jamiyas',
            params={'page': page, 'limit': limit}
        )
        return response.json()

# الاستخدام
api = AljameiaAPI('https://api.aljameia.com/api/v1')
login_result = api.login('<EMAIL>', 'password123')
```

## WebSocket Events

### الاتصال
```javascript
const socket = io('https://api.aljameia.com', {
  auth: {
    token: accessToken
  }
});

// الأحداث المتاحة
socket.on('payment_status_update', (data) => {
  console.log('Payment status updated:', data);
});

socket.on('jamiya_update', (data) => {
  console.log('Jamiya updated:', data);
});

socket.on('new_notification', (data) => {
  console.log('New notification:', data);
});
```

## معدل الطلبات (Rate Limiting)

- **العام**: 100 طلب كل 15 دقيقة لكل IP
- **المصادقة**: 5 طلبات كل دقيقة لكل IP
- **رفع الملفات**: 2 طلب كل ثانية لكل IP

## الدعم

للحصول على المساعدة مع API:
- **الوثائق**: [docs.aljameia.com/api](https://docs.aljameia.com/api)
- **الدعم الفني**: [<EMAIL>](mailto:<EMAIL>)
