"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/services/apiClient.ts":
/*!***********************************!*\
  !*** ./src/services/apiClient.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: function() { return /* binding */ ApiClient; },\n/* harmony export */   api: function() { return /* binding */ api; },\n/* harmony export */   axiosClient: function() { return /* binding */ axiosClient; },\n/* harmony export */   healthCheck: function() { return /* binding */ healthCheck; },\n/* harmony export */   retryRequest: function() { return /* binding */ retryRequest; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"../node_modules/axios/index.js\");\n\n\n// API configuration\nconst API_BASE_URL = \"http://localhost:3001/api\" || 0;\nconst REQUEST_TIMEOUT = 30000; // 30 seconds\n// Create axios instance\nconst axiosClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: REQUEST_TIMEOUT,\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    },\n    withCredentials: true\n});\n// Request interceptor\naxiosClient.interceptors.request.use((config)=>{\n    // Add authentication token\n    const token = localStorage.getItem(\"auth_token\");\n    if (token && config.headers) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    // Add language header\n    const language = localStorage.getItem(\"language\") || \"en\";\n    if (config.headers) {\n        config.headers[\"Accept-Language\"] = language;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor\naxiosClient.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    var _error_response;\n    // Handle 401 errors (unauthorized)\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        localStorage.removeItem(\"auth_token\");\n        if (true) {\n            window.location.href = \"/auth/login\";\n        }\n    }\n    return Promise.reject(error);\n});\n// API client wrapper with additional methods\nclass ApiClient {\n    // GET request\n    async get(url, config) {\n        return this.client.get(url, config);\n    }\n    // POST request\n    async post(url, data, config) {\n        return this.client.post(url, data, config);\n    }\n    // PUT request\n    async put(url, data, config) {\n        return this.client.put(url, data, config);\n    }\n    // PATCH request\n    async patch(url, data, config) {\n        return this.client.patch(url, data, config);\n    }\n    // DELETE request\n    async delete(url, config) {\n        return this.client.delete(url, config);\n    }\n    // Set authorization header\n    setAuthToken(token) {\n        this.client.defaults.headers.common[\"Authorization\"] = \"Bearer \".concat(token);\n    }\n    // Remove authorization header\n    removeAuthToken() {\n        delete this.client.defaults.headers.common[\"Authorization\"];\n    }\n    constructor(client){\n        (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_1__._)(this, \"client\", void 0);\n        this.client = client;\n    }\n}\n// Create and export API client instance\nconst api = new ApiClient(axiosClient);\n// Export axios instance for direct use if needed\n\n// Health check function\nasync function healthCheck() {\n    try {\n        await apiClient.get(\"/health\");\n        return true;\n    } catch (error) {\n        logger.error(\"Health check failed:\", error);\n        return false;\n    }\n}\n// Retry function for failed requests\nasync function retryRequest(requestFn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await requestFn();\n        } catch (error) {\n            lastError = error;\n            if (i === maxRetries) {\n                break;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n        }\n    }\n    throw lastError;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc2VydmljZXMvYXBpQ2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRGO0FBRTVGLG9CQUFvQjtBQUNwQixNQUFNQyxlQUFlQywyQkFBK0IsSUFBSTtBQUN4RCxNQUFNRyxrQkFBa0IsT0FBTyxhQUFhO0FBRTVDLHdCQUF3QjtBQUN4QixNQUFNQyxjQUE2Qk4sb0RBQVksQ0FBQztJQUM5Q1EsU0FBU1A7SUFDVFEsU0FBU0o7SUFDVEssU0FBUztRQUNQLGdCQUFnQjtRQUNoQixVQUFVO0lBQ1o7SUFDQUMsaUJBQWlCO0FBQ25CO0FBRUEsc0JBQXNCO0FBQ3RCTCxZQUFZTSxZQUFZLENBQUNDLE9BQU8sQ0FBQ0MsR0FBRyxDQUNsQyxDQUFDQztJQUNDLDJCQUEyQjtJQUMzQixNQUFNQyxRQUFRQyxhQUFhQyxPQUFPLENBQUM7SUFDbkMsSUFBSUYsU0FBU0QsT0FBT0wsT0FBTyxFQUFFO1FBQzNCSyxPQUFPTCxPQUFPLENBQUNTLGFBQWEsR0FBRyxVQUFnQixPQUFOSDtJQUMzQztJQUVBLHNCQUFzQjtJQUN0QixNQUFNSSxXQUFXSCxhQUFhQyxPQUFPLENBQUMsZUFBZTtJQUNyRCxJQUFJSCxPQUFPTCxPQUFPLEVBQUU7UUFDbEJLLE9BQU9MLE9BQU8sQ0FBQyxrQkFBa0IsR0FBR1U7SUFDdEM7SUFFQSxPQUFPTDtBQUNULEdBQ0EsQ0FBQ007SUFDQyxPQUFPQyxRQUFRQyxNQUFNLENBQUNGO0FBQ3hCO0FBR0YsdUJBQXVCO0FBQ3ZCZixZQUFZTSxZQUFZLENBQUNZLFFBQVEsQ0FBQ1YsR0FBRyxDQUNuQyxDQUFDVTtJQUNDLE9BQU9BO0FBQ1QsR0FDQSxPQUFPSDtRQUVEQTtJQURKLG1DQUFtQztJQUNuQyxJQUFJQSxFQUFBQSxrQkFBQUEsTUFBTUcsUUFBUSxjQUFkSCxzQ0FBQUEsZ0JBQWdCSSxNQUFNLE1BQUssS0FBSztRQUNsQ1IsYUFBYVMsVUFBVSxDQUFDO1FBQ3hCLElBQUksSUFBa0IsRUFBYTtZQUNqQ0MsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7UUFDekI7SUFDRjtJQUVBLE9BQU9QLFFBQVFDLE1BQU0sQ0FBQ0Y7QUFDeEI7QUFHRiw2Q0FBNkM7QUFDdEMsTUFBTVM7SUFPWCxjQUFjO0lBQ2QsTUFBTUMsSUFBYUMsR0FBVyxFQUFFakIsTUFBMkIsRUFBNkI7UUFDdEYsT0FBTyxJQUFJLENBQUNrQixNQUFNLENBQUNGLEdBQUcsQ0FBSUMsS0FBS2pCO0lBQ2pDO0lBRUEsZUFBZTtJQUNmLE1BQU1tQixLQUFjRixHQUFXLEVBQUVHLElBQVUsRUFBRXBCLE1BQTJCLEVBQTZCO1FBQ25HLE9BQU8sSUFBSSxDQUFDa0IsTUFBTSxDQUFDQyxJQUFJLENBQUlGLEtBQUtHLE1BQU1wQjtJQUN4QztJQUVBLGNBQWM7SUFDZCxNQUFNcUIsSUFBYUosR0FBVyxFQUFFRyxJQUFVLEVBQUVwQixNQUEyQixFQUE2QjtRQUNsRyxPQUFPLElBQUksQ0FBQ2tCLE1BQU0sQ0FBQ0csR0FBRyxDQUFJSixLQUFLRyxNQUFNcEI7SUFDdkM7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBTXNCLE1BQWVMLEdBQVcsRUFBRUcsSUFBVSxFQUFFcEIsTUFBMkIsRUFBNkI7UUFDcEcsT0FBTyxJQUFJLENBQUNrQixNQUFNLENBQUNJLEtBQUssQ0FBSUwsS0FBS0csTUFBTXBCO0lBQ3pDO0lBRUEsaUJBQWlCO0lBQ2pCLE1BQU11QixPQUFnQk4sR0FBVyxFQUFFakIsTUFBMkIsRUFBNkI7UUFDekYsT0FBTyxJQUFJLENBQUNrQixNQUFNLENBQUNLLE1BQU0sQ0FBSU4sS0FBS2pCO0lBQ3BDO0lBRUEsMkJBQTJCO0lBQzNCd0IsYUFBYXZCLEtBQWEsRUFBUTtRQUNoQyxJQUFJLENBQUNpQixNQUFNLENBQUNPLFFBQVEsQ0FBQzlCLE9BQU8sQ0FBQytCLE1BQU0sQ0FBQyxnQkFBZ0IsR0FBRyxVQUFnQixPQUFOekI7SUFDbkU7SUFFQSw4QkFBOEI7SUFDOUIwQixrQkFBd0I7UUFDdEIsT0FBTyxJQUFJLENBQUNULE1BQU0sQ0FBQ08sUUFBUSxDQUFDOUIsT0FBTyxDQUFDK0IsTUFBTSxDQUFDLGdCQUFnQjtJQUM3RDtJQXJDQUUsWUFBWVYsTUFBcUIsQ0FBRTtRQUZuQyxzRUFBUUEsVUFBUjtRQUdFLElBQUksQ0FBQ0EsTUFBTSxHQUFHQTtJQUNoQjtBQW9DRjtBQUVBLHdDQUF3QztBQUNqQyxNQUFNVyxNQUFNLElBQUlkLFVBQVV4QixhQUFhO0FBRTlDLGlEQUFpRDtBQUMxQjtBQUV2Qix3QkFBd0I7QUFDakIsZUFBZXVDO0lBQ3BCLElBQUk7UUFDRixNQUFNQyxVQUFVZixHQUFHLENBQUM7UUFDcEIsT0FBTztJQUNULEVBQUUsT0FBT1YsT0FBTztRQUNkMEIsT0FBTzFCLEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3JDLE9BQU87SUFDVDtBQUNGO0FBRUEscUNBQXFDO0FBQzlCLGVBQWUyQixhQUNwQkMsU0FBMkI7UUFDM0JDLGFBQUFBLGlFQUFxQixHQUNyQkMsUUFBQUEsaUVBQWdCO0lBRWhCLElBQUlDO0lBRUosSUFBSyxJQUFJQyxJQUFJLEdBQUdBLEtBQUtILFlBQVlHLElBQUs7UUFDcEMsSUFBSTtZQUNGLE9BQU8sTUFBTUo7UUFDZixFQUFFLE9BQU81QixPQUFPO1lBQ2QrQixZQUFZL0I7WUFFWixJQUFJZ0MsTUFBTUgsWUFBWTtnQkFDcEI7WUFDRjtZQUVBLHVCQUF1QjtZQUN2QixNQUFNLElBQUk1QixRQUFRZ0MsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBU0gsUUFBUUssS0FBS0MsR0FBRyxDQUFDLEdBQUdKO1FBQ3ZFO0lBQ0Y7SUFFQSxNQUFNRDtBQUNSIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zZXJ2aWNlcy9hcGlDbGllbnQudHM/N2QwMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXhpb3MsIHsgQXhpb3NJbnN0YW5jZSwgQXhpb3NSZXF1ZXN0Q29uZmlnLCBBeGlvc1Jlc3BvbnNlLCBBeGlvc0Vycm9yIH0gZnJvbSAnYXhpb3MnO1xuXG4vLyBBUEkgY29uZmlndXJhdGlvblxuY29uc3QgQVBJX0JBU0VfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAxL2FwaSc7XG5jb25zdCBSRVFVRVNUX1RJTUVPVVQgPSAzMDAwMDsgLy8gMzAgc2Vjb25kc1xuXG4vLyBDcmVhdGUgYXhpb3MgaW5zdGFuY2VcbmNvbnN0IGF4aW9zQ2xpZW50OiBBeGlvc0luc3RhbmNlID0gYXhpb3MuY3JlYXRlKHtcbiAgYmFzZVVSTDogQVBJX0JBU0VfVVJMLFxuICB0aW1lb3V0OiBSRVFVRVNUX1RJTUVPVVQsXG4gIGhlYWRlcnM6IHtcbiAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbicsXG4gIH0sXG4gIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcbn0pO1xuXG4vLyBSZXF1ZXN0IGludGVyY2VwdG9yXG5heGlvc0NsaWVudC5pbnRlcmNlcHRvcnMucmVxdWVzdC51c2UoXG4gIChjb25maWc6IEF4aW9zUmVxdWVzdENvbmZpZykgPT4ge1xuICAgIC8vIEFkZCBhdXRoZW50aWNhdGlvbiB0b2tlblxuICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1dGhfdG9rZW4nKTtcbiAgICBpZiAodG9rZW4gJiYgY29uZmlnLmhlYWRlcnMpIHtcbiAgICAgIGNvbmZpZy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YDtcbiAgICB9XG5cbiAgICAvLyBBZGQgbGFuZ3VhZ2UgaGVhZGVyXG4gICAgY29uc3QgbGFuZ3VhZ2UgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnbGFuZ3VhZ2UnKSB8fCAnZW4nO1xuICAgIGlmIChjb25maWcuaGVhZGVycykge1xuICAgICAgY29uZmlnLmhlYWRlcnNbJ0FjY2VwdC1MYW5ndWFnZSddID0gbGFuZ3VhZ2U7XG4gICAgfVxuXG4gICAgcmV0dXJuIGNvbmZpZztcbiAgfSxcbiAgKGVycm9yOiBBeGlvc0Vycm9yKSA9PiB7XG4gICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcbiAgfVxuKTtcblxuLy8gUmVzcG9uc2UgaW50ZXJjZXB0b3JcbmF4aW9zQ2xpZW50LmludGVyY2VwdG9ycy5yZXNwb25zZS51c2UoXG4gIChyZXNwb25zZTogQXhpb3NSZXNwb25zZSkgPT4ge1xuICAgIHJldHVybiByZXNwb25zZTtcbiAgfSxcbiAgYXN5bmMgKGVycm9yOiBBeGlvc0Vycm9yKSA9PiB7XG4gICAgLy8gSGFuZGxlIDQwMSBlcnJvcnMgKHVuYXV0aG9yaXplZClcbiAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYXV0aF90b2tlbicpO1xuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9hdXRoL2xvZ2luJztcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpO1xuICB9XG4pO1xuXG4vLyBBUEkgY2xpZW50IHdyYXBwZXIgd2l0aCBhZGRpdGlvbmFsIG1ldGhvZHNcbmV4cG9ydCBjbGFzcyBBcGlDbGllbnQge1xuICBwcml2YXRlIGNsaWVudDogQXhpb3NJbnN0YW5jZTtcblxuICBjb25zdHJ1Y3RvcihjbGllbnQ6IEF4aW9zSW5zdGFuY2UpIHtcbiAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgfVxuXG4gIC8vIEdFVCByZXF1ZXN0XG4gIGFzeW5jIGdldDxUID0gYW55Pih1cmw6IHN0cmluZywgY29uZmlnPzogQXhpb3NSZXF1ZXN0Q29uZmlnKTogUHJvbWlzZTxBeGlvc1Jlc3BvbnNlPFQ+PiB7XG4gICAgcmV0dXJuIHRoaXMuY2xpZW50LmdldDxUPih1cmwsIGNvbmZpZyk7XG4gIH1cblxuICAvLyBQT1NUIHJlcXVlc3RcbiAgYXN5bmMgcG9zdDxUID0gYW55Pih1cmw6IHN0cmluZywgZGF0YT86IGFueSwgY29uZmlnPzogQXhpb3NSZXF1ZXN0Q29uZmlnKTogUHJvbWlzZTxBeGlvc1Jlc3BvbnNlPFQ+PiB7XG4gICAgcmV0dXJuIHRoaXMuY2xpZW50LnBvc3Q8VD4odXJsLCBkYXRhLCBjb25maWcpO1xuICB9XG5cbiAgLy8gUFVUIHJlcXVlc3RcbiAgYXN5bmMgcHV0PFQgPSBhbnk+KHVybDogc3RyaW5nLCBkYXRhPzogYW55LCBjb25maWc/OiBBeGlvc1JlcXVlc3RDb25maWcpOiBQcm9taXNlPEF4aW9zUmVzcG9uc2U8VD4+IHtcbiAgICByZXR1cm4gdGhpcy5jbGllbnQucHV0PFQ+KHVybCwgZGF0YSwgY29uZmlnKTtcbiAgfVxuXG4gIC8vIFBBVENIIHJlcXVlc3RcbiAgYXN5bmMgcGF0Y2g8VCA9IGFueT4odXJsOiBzdHJpbmcsIGRhdGE/OiBhbnksIGNvbmZpZz86IEF4aW9zUmVxdWVzdENvbmZpZyk6IFByb21pc2U8QXhpb3NSZXNwb25zZTxUPj4ge1xuICAgIHJldHVybiB0aGlzLmNsaWVudC5wYXRjaDxUPih1cmwsIGRhdGEsIGNvbmZpZyk7XG4gIH1cblxuICAvLyBERUxFVEUgcmVxdWVzdFxuICBhc3luYyBkZWxldGU8VCA9IGFueT4odXJsOiBzdHJpbmcsIGNvbmZpZz86IEF4aW9zUmVxdWVzdENvbmZpZyk6IFByb21pc2U8QXhpb3NSZXNwb25zZTxUPj4ge1xuICAgIHJldHVybiB0aGlzLmNsaWVudC5kZWxldGU8VD4odXJsLCBjb25maWcpO1xuICB9XG5cbiAgLy8gU2V0IGF1dGhvcml6YXRpb24gaGVhZGVyXG4gIHNldEF1dGhUb2tlbih0b2tlbjogc3RyaW5nKTogdm9pZCB7XG4gICAgdGhpcy5jbGllbnQuZGVmYXVsdHMuaGVhZGVycy5jb21tb25bJ0F1dGhvcml6YXRpb24nXSA9IGBCZWFyZXIgJHt0b2tlbn1gO1xuICB9XG5cbiAgLy8gUmVtb3ZlIGF1dGhvcml6YXRpb24gaGVhZGVyXG4gIHJlbW92ZUF1dGhUb2tlbigpOiB2b2lkIHtcbiAgICBkZWxldGUgdGhpcy5jbGllbnQuZGVmYXVsdHMuaGVhZGVycy5jb21tb25bJ0F1dGhvcml6YXRpb24nXTtcbiAgfVxufVxuXG4vLyBDcmVhdGUgYW5kIGV4cG9ydCBBUEkgY2xpZW50IGluc3RhbmNlXG5leHBvcnQgY29uc3QgYXBpID0gbmV3IEFwaUNsaWVudChheGlvc0NsaWVudCk7XG5cbi8vIEV4cG9ydCBheGlvcyBpbnN0YW5jZSBmb3IgZGlyZWN0IHVzZSBpZiBuZWVkZWRcbmV4cG9ydCB7IGF4aW9zQ2xpZW50IH07XG5cbi8vIEhlYWx0aCBjaGVjayBmdW5jdGlvblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGhlYWx0aENoZWNrKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICB0cnkge1xuICAgIGF3YWl0IGFwaUNsaWVudC5nZXQoJy9oZWFsdGgnKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBsb2dnZXIuZXJyb3IoJ0hlYWx0aCBjaGVjayBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufVxuXG4vLyBSZXRyeSBmdW5jdGlvbiBmb3IgZmFpbGVkIHJlcXVlc3RzXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmV0cnlSZXF1ZXN0PFQ+KFxuICByZXF1ZXN0Rm46ICgpID0+IFByb21pc2U8VD4sXG4gIG1heFJldHJpZXM6IG51bWJlciA9IDMsXG4gIGRlbGF5OiBudW1iZXIgPSAxMDAwXG4pOiBQcm9taXNlPFQ+IHtcbiAgbGV0IGxhc3RFcnJvcjogYW55O1xuXG4gIGZvciAobGV0IGkgPSAwOyBpIDw9IG1heFJldHJpZXM7IGkrKykge1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gYXdhaXQgcmVxdWVzdEZuKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGxhc3RFcnJvciA9IGVycm9yO1xuICAgICAgXG4gICAgICBpZiAoaSA9PT0gbWF4UmV0cmllcykge1xuICAgICAgICBicmVhaztcbiAgICAgIH1cblxuICAgICAgLy8gV2FpdCBiZWZvcmUgcmV0cnlpbmdcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCBkZWxheSAqIE1hdGgucG93KDIsIGkpKSk7XG4gICAgfVxuICB9XG5cbiAgdGhyb3cgbGFzdEVycm9yO1xufVxuIl0sIm5hbWVzIjpbImF4aW9zIiwiQVBJX0JBU0VfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJSRVFVRVNUX1RJTUVPVVQiLCJheGlvc0NsaWVudCIsImNyZWF0ZSIsImJhc2VVUkwiLCJ0aW1lb3V0IiwiaGVhZGVycyIsIndpdGhDcmVkZW50aWFscyIsImludGVyY2VwdG9ycyIsInJlcXVlc3QiLCJ1c2UiLCJjb25maWciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJBdXRob3JpemF0aW9uIiwibGFuZ3VhZ2UiLCJlcnJvciIsIlByb21pc2UiLCJyZWplY3QiLCJyZXNwb25zZSIsInN0YXR1cyIsInJlbW92ZUl0ZW0iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJBcGlDbGllbnQiLCJnZXQiLCJ1cmwiLCJjbGllbnQiLCJwb3N0IiwiZGF0YSIsInB1dCIsInBhdGNoIiwiZGVsZXRlIiwic2V0QXV0aFRva2VuIiwiZGVmYXVsdHMiLCJjb21tb24iLCJyZW1vdmVBdXRoVG9rZW4iLCJjb25zdHJ1Y3RvciIsImFwaSIsImhlYWx0aENoZWNrIiwiYXBpQ2xpZW50IiwibG9nZ2VyIiwicmV0cnlSZXF1ZXN0IiwicmVxdWVzdEZuIiwibWF4UmV0cmllcyIsImRlbGF5IiwibGFzdEVycm9yIiwiaSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiTWF0aCIsInBvdyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/services/apiClient.ts\n"));

/***/ })

});