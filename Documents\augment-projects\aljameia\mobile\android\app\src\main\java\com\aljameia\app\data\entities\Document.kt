package com.aljameia.app.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey
import java.util.Date

@Entity(
    tableName = "documents",
    foreignKeys = [
        ForeignKey(
            entity = User::class,
            parentColumns = ["id"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
data class Document(
    @PrimaryKey
    val id: String,
    val userId: String,
    val title: String,
    val description: String?,
    val type: DocumentType,
    val category: String,
    val fileUrl: String,
    val fileName: String,
    val fileSize: Long,
    val mimeType: String,
    val isPublic: Boolean = false,
    val downloadCount: Int = 0,
    val expiryDate: Date?,
    val tags: List<String> = emptyList(),
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)

enum class DocumentType {
    MEMBERSHIP_CERTIFICATE,
    RECEIPT,
    INVOICE,
    STATEMENT,
    FORM,
    POLICY,
    ANNOUNCEMENT,
    REPORT,
    CONTRACT,
    OTHER
}
