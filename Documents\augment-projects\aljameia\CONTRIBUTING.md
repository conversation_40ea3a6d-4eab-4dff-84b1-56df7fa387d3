# دليل المساهمة في منصة الجمعية

نرحب بمساهماتكم في تطوير منصة الجمعية! هذا الدليل يوضح كيفية المساهمة في المشروع.

## جدول المحتويات

- [قواعد السلوك](#قواعد-السلوك)
- [كيفية المساهمة](#كيفية-المساهمة)
- [إعداد بيئة التطوير](#إعداد-بيئة-التطوير)
- [معايير الكود](#معايير-الكود)
- [عملية المراجعة](#عملية-المراجعة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [طلب ميزات جديدة](#طلب-ميزات-جديدة)

## قواعد السلوك

### تعهدنا

نحن كمساهمين ومشرفين نتعهد بجعل المشاركة في مشروعنا ومجتمعنا تجربة خالية من التحرش للجميع، بغض النظر عن العمر أو حجم الجسم أو الإعاقة أو العرق أو الهوية والتعبير الجنسي أو مستوى الخبرة أو التعليم أو الوضع الاجتماعي والاقتصادي أو الجنسية أو المظهر الشخصي أو العرق أو الدين أو الهوية والتوجه الجنسي.

### معاييرنا

أمثلة على السلوك الذي يساهم في خلق بيئة إيجابية:

- استخدام لغة ترحيبية وشاملة
- احترام وجهات النظر والتجارب المختلفة
- قبول النقد البناء بأدب
- التركيز على ما هو أفضل للمجتمع
- إظهار التعاطف تجاه أعضاء المجتمع الآخرين

أمثلة على السلوك غير المقبول:

- استخدام اللغة أو الصور الجنسية والاهتمام الجنسي غير المرغوب فيه
- التصيد والتعليقات المهينة/المحطة والهجمات الشخصية أو السياسية
- التحرش العلني أو الخاص
- نشر معلومات خاصة للآخرين دون إذن صريح
- السلوك الآخر الذي يمكن اعتباره غير مناسب في بيئة مهنية

## كيفية المساهمة

### أنواع المساهمات

نرحب بأنواع مختلفة من المساهمات:

1. **الإبلاغ عن الأخطاء**
2. **طلب ميزات جديدة**
3. **تحسين الوثائق**
4. **إصلاح الأخطاء**
5. **إضافة ميزات جديدة**
6. **تحسين الأداء**
7. **تحسين الأمان**

### خطوات المساهمة

1. **Fork المشروع**
   ```bash
   # انقر على زر Fork في GitHub
   git clone https://github.com/your-username/aljameia-platform.git
   cd aljameia-platform
   ```

2. **إنشاء فرع للميزة**
   ```bash
   git checkout -b feature/amazing-feature
   # أو للإصلاحات
   git checkout -b fix/bug-description
   ```

3. **إجراء التغييرات**
   - اتبع معايير الكود
   - أضف اختبارات للميزات الجديدة
   - تأكد من نجاح جميع الاختبارات

4. **Commit التغييرات**
   ```bash
   git add .
   git commit -m "feat: add amazing feature"
   ```

5. **Push للفرع**
   ```bash
   git push origin feature/amazing-feature
   ```

6. **فتح Pull Request**
   - اذهب إلى GitHub
   - انقر على "New Pull Request"
   - املأ النموذج بالتفاصيل

## إعداد بيئة التطوير

### المتطلبات

- Node.js 20.x+
- Docker 20.10+
- Docker Compose 2.0+
- Git

### الإعداد السريع

```bash
# استنساخ المشروع
git clone https://github.com/your-username/aljameia-platform.git
cd aljameia-platform

# إعداد البيئة
make dev-setup

# بدء التطوير
make dev-up

# تشغيل الاختبارات
make test
```

### هيكل المشروع

```
aljameia/
├── backend/                 # Backend API (Node.js/Express)
├── admin-dashboard/         # Admin Dashboard (Next.js)
├── database/               # Database configurations
├── nginx/                  # Reverse proxy configuration
├── scripts/                # Utility scripts
├── docs/                   # Documentation
├── tests/                  # Integration tests
└── docker-compose.yml      # Docker configuration
```

## معايير الكود

### JavaScript/TypeScript

نستخدم ESLint و Prettier لضمان جودة الكود:

```bash
# فحص الكود
npm run lint

# إصلاح المشاكل تلقائياً
npm run lint:fix

# تنسيق الكود
npm run format
```

#### قواعد التسمية

- **المتغيرات والدوال**: camelCase
  ```typescript
  const userName = 'أحمد';
  function getUserData() { }
  ```

- **الثوابت**: UPPER_SNAKE_CASE
  ```typescript
  const MAX_RETRY_ATTEMPTS = 3;
  ```

- **الكلاسات**: PascalCase
  ```typescript
  class UserService { }
  ```

- **الملفات**: kebab-case
  ```
  user-service.ts
  payment-controller.ts
  ```

#### التعليقات

```typescript
/**
 * حساب المبلغ الإجمالي للجمعية
 * @param monthlyAmount المبلغ الشهري
 * @param duration مدة الجمعية بالأشهر
 * @returns المبلغ الإجمالي
 */
function calculateTotalAmount(monthlyAmount: number, duration: number): number {
  return monthlyAmount * duration;
}
```

### Git Commit Messages

نستخدم Conventional Commits:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### أنواع الـ Commits

- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تحديث الوثائق
- `style`: تغييرات التنسيق
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تحديث الاختبارات
- `chore`: مهام الصيانة

#### أمثلة

```bash
feat(auth): add two-factor authentication
fix(payment): resolve mada payment gateway issue
docs(api): update authentication endpoints
style(admin): improve dashboard layout
refactor(database): optimize user queries
test(jamiya): add unit tests for jamiya service
chore(deps): update dependencies
```

### اختبارات الكود

#### Backend Tests

```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل اختبارات محددة
npm test -- --grep "User Service"

# تشغيل مع التغطية
npm run test:coverage
```

#### Frontend Tests

```bash
# تشغيل اختبارات المكونات
npm test

# تشغيل اختبارات E2E
npm run test:e2e
```

#### كتابة الاختبارات

```typescript
// backend/src/tests/services/user.test.ts
import { UserService } from '@/services/UserService';

describe('UserService', () => {
  describe('createUser', () => {
    it('should create a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        name: 'اختبار',
        password: 'password123'
      };

      const user = await UserService.createUser(userData);
      
      expect(user).toBeDefined();
      expect(user.email).toBe(userData.email);
      expect(user.name).toBe(userData.name);
    });

    it('should throw error for duplicate email', async () => {
      const userData = {
        email: '<EMAIL>',
        name: 'اختبار',
        password: 'password123'
      };

      await expect(UserService.createUser(userData))
        .rejects
        .toThrow('User already exists');
    });
  });
});
```

## عملية المراجعة

### قبل إرسال Pull Request

1. **تأكد من نجاح الاختبارات**
   ```bash
   make test
   ```

2. **فحص جودة الكود**
   ```bash
   make lint
   ```

3. **تحديث الوثائق** إذا لزم الأمر

4. **اختبار التغييرات محلياً**
   ```bash
   make dev-up
   # اختبر الميزة الجديدة
   ```

### معايير المراجعة

#### الكود
- [ ] الكود واضح ومفهوم
- [ ] يتبع معايير المشروع
- [ ] لا يحتوي على أكواد مكررة
- [ ] يتعامل مع الأخطاء بشكل صحيح
- [ ] محسن للأداء

#### الاختبارات
- [ ] تغطية اختبارات كافية
- [ ] اختبارات الحالات الحدية
- [ ] اختبارات سيناريوهات الفشل

#### الأمان
- [ ] لا يحتوي على ثغرات أمنية
- [ ] يتبع أفضل ممارسات الأمان
- [ ] يتعامل مع البيانات الحساسة بحذر

#### الوثائق
- [ ] الوثائق محدثة
- [ ] التعليقات واضحة
- [ ] أمثلة الاستخدام متوفرة

## الإبلاغ عن الأخطاء

### قبل الإبلاغ

1. تأكد من أن الخطأ لم يتم الإبلاغ عنه مسبقاً
2. تحقق من أنك تستخدم أحدث إصدار
3. جرب إعادة إنتاج الخطأ

### نموذج الإبلاغ

```markdown
## وصف الخطأ
وصف واضح ومختصر للخطأ.

## خطوات إعادة الإنتاج
1. اذهب إلى '...'
2. انقر على '....'
3. مرر إلى '....'
4. شاهد الخطأ

## السلوك المتوقع
وصف واضح لما كنت تتوقع حدوثه.

## لقطات الشاشة
إذا كان ذلك مناسباً، أضف لقطات شاشة لتوضيح المشكلة.

## معلومات البيئة
- OS: [e.g. Windows 11, macOS 12, Ubuntu 20.04]
- Browser: [e.g. Chrome 91, Firefox 89, Safari 14]
- Version: [e.g. 1.0.0]

## معلومات إضافية
أي معلومات أخرى حول المشكلة.
```

## طلب ميزات جديدة

### نموذج طلب الميزة

```markdown
## هل طلبك مرتبط بمشكلة؟
وصف واضح ومختصر للمشكلة. مثال: أشعر بالإحباط عندما [...]

## وصف الحل المطلوب
وصف واضح ومختصر لما تريده أن يحدث.

## وصف البدائل المدروسة
وصف واضح ومختصر لأي حلول أو ميزات بديلة فكرت فيها.

## معلومات إضافية
أي معلومات أخرى أو لقطات شاشة حول طلب الميزة.
```

## الحصول على المساعدة

إذا كنت بحاجة للمساعدة:

1. **الوثائق**: اقرأ [الوثائق](docs/)
2. **المناقشات**: شارك في [GitHub Discussions](https://github.com/aljameia/platform/discussions)
3. **Discord**: انضم لخادم [Discord](https://discord.gg/aljameia)
4. **البريد الإلكتروني**: راسلنا على [<EMAIL>](mailto:<EMAIL>)

## الترخيص

بمساهمتك في هذا المشروع، فإنك توافق على أن مساهماتك ستكون مرخصة تحت [رخصة MIT](LICENSE).

## شكر وتقدير

شكراً لجميع المساهمين الذين ساعدوا في تطوير منصة الجمعية! 🙏

---

**ملاحظة**: هذا الدليل قابل للتطوير. إذا كان لديك اقتراحات لتحسينه، فلا تتردد في فتح issue أو pull request.
