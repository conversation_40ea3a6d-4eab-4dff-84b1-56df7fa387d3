// ==============================================
// MongoDB Initialization Script for Aljameia
// ==============================================

// Switch to the aljameia database
db = db.getSiblingDB('aljameia');

// Create application user
db.createUser({
  user: 'aljameia_app',
  pwd: 'app_password_123',
  roles: [
    {
      role: 'readWrite',
      db: 'aljameia'
    }
  ]
});

print('✅ Created application user: aljameia_app');

// ==============================================
// Create Collections with Validation
// ==============================================

// Users Collection
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'name', 'password', 'role'],
      properties: {
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
          description: 'Must be a valid email address'
        },
        name: {
          bsonType: 'string',
          minLength: 2,
          maxLength: 100,
          description: 'Must be a string between 2-100 characters'
        },
        password: {
          bsonType: 'string',
          minLength: 6,
          description: 'Must be a string with minimum 6 characters'
        },
        role: {
          enum: ['user', 'admin', 'moderator'],
          description: 'Must be one of: user, admin, moderator'
        },
        phone: {
          bsonType: ['string', 'null'],
          pattern: '^[+]?[0-9]{10,15}$',
          description: 'Must be a valid phone number'
        },
        isVerified: {
          bsonType: 'bool',
          description: 'Must be a boolean'
        },
        isActive: {
          bsonType: 'bool',
          description: 'Must be a boolean'
        }
      }
    }
  }
});

print('✅ Created users collection with validation');

// Jamiyas Collection
db.createCollection('jamiyas', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['name', 'description', 'totalAmount', 'monthlyAmount', 'duration', 'maxMembers', 'createdBy'],
      properties: {
        name: {
          bsonType: 'string',
          minLength: 3,
          maxLength: 100,
          description: 'Must be a string between 3-100 characters'
        },
        description: {
          bsonType: 'string',
          maxLength: 500,
          description: 'Must be a string with maximum 500 characters'
        },
        totalAmount: {
          bsonType: 'number',
          minimum: 1000,
          description: 'Must be a number with minimum value 1000'
        },
        monthlyAmount: {
          bsonType: 'number',
          minimum: 100,
          description: 'Must be a number with minimum value 100'
        },
        duration: {
          bsonType: 'int',
          minimum: 6,
          maximum: 60,
          description: 'Must be an integer between 6-60 months'
        },
        maxMembers: {
          bsonType: 'int',
          minimum: 5,
          maximum: 100,
          description: 'Must be an integer between 5-100'
        },
        status: {
          enum: ['draft', 'active', 'full', 'completed', 'cancelled'],
          description: 'Must be one of: draft, active, full, completed, cancelled'
        }
      }
    }
  }
});

print('✅ Created jamiyas collection with validation');

// Subscriptions Collection
db.createCollection('subscriptions', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'jamiyaId', 'shares', 'monthlyAmount', 'status'],
      properties: {
        shares: {
          bsonType: 'int',
          minimum: 1,
          maximum: 10,
          description: 'Must be an integer between 1-10'
        },
        monthlyAmount: {
          bsonType: 'number',
          minimum: 100,
          description: 'Must be a number with minimum value 100'
        },
        status: {
          enum: ['active', 'paused', 'cancelled', 'completed'],
          description: 'Must be one of: active, paused, cancelled, completed'
        }
      }
    }
  }
});

print('✅ Created subscriptions collection with validation');

// Payments Collection
db.createCollection('payments', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'subscriptionId', 'amount', 'currency', 'method', 'status'],
      properties: {
        amount: {
          bsonType: 'number',
          minimum: 1,
          description: 'Must be a positive number'
        },
        currency: {
          enum: ['SAR', 'USD', 'EUR'],
          description: 'Must be one of: SAR, USD, EUR'
        },
        method: {
          enum: ['mada', 'stc_pay', 'apple_pay', 'bank_transfer', 'cash'],
          description: 'Must be a valid payment method'
        },
        status: {
          enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
          description: 'Must be a valid payment status'
        }
      }
    }
  }
});

print('✅ Created payments collection with validation');

// ==============================================
// Create Indexes for Performance
// ==============================================

// Users indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ phone: 1 }, { unique: true, sparse: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ isActive: 1 });
db.users.createIndex({ createdAt: 1 });

print('✅ Created users indexes');

// Jamiyas indexes
db.jamiyas.createIndex({ name: 1 });
db.jamiyas.createIndex({ status: 1 });
db.jamiyas.createIndex({ createdBy: 1 });
db.jamiyas.createIndex({ startDate: 1 });
db.jamiyas.createIndex({ endDate: 1 });
db.jamiyas.createIndex({ totalAmount: 1 });
db.jamiyas.createIndex({ monthlyAmount: 1 });

print('✅ Created jamiyas indexes');

// Subscriptions indexes
db.subscriptions.createIndex({ userId: 1 });
db.subscriptions.createIndex({ jamiyaId: 1 });
db.subscriptions.createIndex({ userId: 1, jamiyaId: 1 }, { unique: true });
db.subscriptions.createIndex({ status: 1 });
db.subscriptions.createIndex({ nextPaymentDate: 1 });

print('✅ Created subscriptions indexes');

// Payments indexes
db.payments.createIndex({ userId: 1 });
db.payments.createIndex({ subscriptionId: 1 });
db.payments.createIndex({ status: 1 });
db.payments.createIndex({ method: 1 });
db.payments.createIndex({ createdAt: 1 });
db.payments.createIndex({ transactionId: 1 }, { unique: true, sparse: true });

print('✅ Created payments indexes');

// ==============================================
// Insert Sample Data
// ==============================================

// Sample admin user
db.users.insertOne({
  email: '<EMAIL>',
  name: 'مدير النظام',
  password: '$2b$10$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5u', // hashed: admin123
  role: 'admin',
  phone: '+966501234567',
  isVerified: true,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

print('✅ Created admin user');

// Sample jamiya
const jamiyaId = new ObjectId();
db.jamiyas.insertOne({
  _id: jamiyaId,
  name: 'جمعية الأصدقاء',
  description: 'جمعية مالية للأصدقاء والزملاء',
  totalAmount: 60000,
  monthlyAmount: 2500,
  duration: 24,
  maxMembers: 24,
  currentMembers: 0,
  status: 'active',
  startDate: new Date(),
  endDate: new Date(Date.now() + 24 * 30 * 24 * 60 * 60 * 1000), // 24 months
  createdBy: db.users.findOne({ email: '<EMAIL>' })._id,
  createdAt: new Date(),
  updatedAt: new Date()
});

print('✅ Created sample jamiya');

// ==============================================
// Create Views for Analytics
// ==============================================

// Active subscriptions view
db.createView('activeSubscriptions', 'subscriptions', [
  {
    $match: { status: 'active' }
  },
  {
    $lookup: {
      from: 'users',
      localField: 'userId',
      foreignField: '_id',
      as: 'user'
    }
  },
  {
    $lookup: {
      from: 'jamiyas',
      localField: 'jamiyaId',
      foreignField: '_id',
      as: 'jamiya'
    }
  },
  {
    $unwind: '$user'
  },
  {
    $unwind: '$jamiya'
  }
]);

print('✅ Created activeSubscriptions view');

// Payment statistics view
db.createView('paymentStats', 'payments', [
  {
    $match: { status: 'completed' }
  },
  {
    $group: {
      _id: {
        year: { $year: '$createdAt' },
        month: { $month: '$createdAt' },
        method: '$method'
      },
      totalAmount: { $sum: '$amount' },
      count: { $sum: 1 }
    }
  },
  {
    $sort: { '_id.year': -1, '_id.month': -1 }
  }
]);

print('✅ Created paymentStats view');

print('🎉 MongoDB initialization completed successfully!');
print('📊 Database: aljameia');
print('👤 App User: aljameia_app');
print('🔐 Admin Email: <EMAIL>');
print('🔑 Admin Password: admin123');
