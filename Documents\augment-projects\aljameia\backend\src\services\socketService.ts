import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { User, IUser } from '@/models/User';
import { logger } from '@/utils/logger';
import { CacheManager } from '@/config/redis';

// Socket user interface
interface SocketUser extends Socket {
  user?: IUser;
}

// Active connections map
const activeConnections = new Map<string, Set<string>>();

// Socket authentication middleware
const authenticateSocket = async (socket: SocketUser, next: Function) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return next(new Error('Authentication token required'));
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      return next(new Error('JWT secret not configured'));
    }

    const decoded = jwt.verify(token, jwtSecret) as any;
    const user = await User.findById(decoded.id);

    if (!user || !user.isActive) {
      return next(new Error('User not found or inactive'));
    }

    socket.user = user;
    next();
  } catch (error) {
    next(new Error('Invalid authentication token'));
  }
};

// Setup socket handlers
export const setupSocketHandlers = (io: SocketIOServer) => {
  // Authentication middleware
  io.use(authenticateSocket);

  io.on('connection', (socket: SocketUser) => {
    const user = socket.user!;
    
    logger.info(`User connected via socket: ${user.email}`, {
      userId: user._id.toString(),
      socketId: socket.id,
    });

    // Add user to active connections
    const userId = user._id.toString();
    if (!activeConnections.has(userId)) {
      activeConnections.set(userId, new Set());
    }
    activeConnections.get(userId)!.add(socket.id);

    // Join user-specific room
    socket.join(`user:${userId}`);

    // Join role-specific room
    socket.join(`role:${user.role}`);

    // Handle joining jamiya rooms
    socket.on('join_jamiya', async (jamiyaId: string) => {
      try {
        // Verify user is member of this jamiya
        const Subscription = require('@/models/Subscription').default;
        const subscription = await Subscription.findOne({
          userId: user._id,
          jamiyaId,
          status: 'active'
        });

        if (subscription) {
          socket.join(`jamiya:${jamiyaId}`);
          socket.emit('joined_jamiya', { jamiyaId });
          logger.debug(`User ${user.email} joined jamiya room: ${jamiyaId}`);
        } else {
          socket.emit('error', { message: 'Not authorized to join this jamiya' });
        }
      } catch (error) {
        socket.emit('error', { message: 'Failed to join jamiya' });
        logger.error('Error joining jamiya room:', error);
      }
    });

    // Handle leaving jamiya rooms
    socket.on('leave_jamiya', (jamiyaId: string) => {
      socket.leave(`jamiya:${jamiyaId}`);
      socket.emit('left_jamiya', { jamiyaId });
      logger.debug(`User ${user.email} left jamiya room: ${jamiyaId}`);
    });

    // Handle real-time chat messages
    socket.on('send_message', async (data: {
      jamiyaId: string;
      message: string;
      type?: 'text' | 'image' | 'file';
    }) => {
      try {
        // Verify user is member of this jamiya
        const Subscription = require('@/models/Subscription').default;
        const subscription = await Subscription.findOne({
          userId: user._id,
          jamiyaId: data.jamiyaId,
          status: 'active'
        });

        if (!subscription) {
          socket.emit('error', { message: 'Not authorized to send messages to this jamiya' });
          return;
        }

        // Create message object
        const message = {
          id: Date.now().toString(),
          jamiyaId: data.jamiyaId,
          userId: user._id,
          userName: user.name,
          userAvatar: user.avatar,
          message: data.message,
          type: data.type || 'text',
          timestamp: new Date(),
        };

        // Store message in cache for recent messages
        await CacheManager.lpush(`jamiya_messages:${data.jamiyaId}`, JSON.stringify(message));
        await CacheManager.ltrim(`jamiya_messages:${data.jamiyaId}`, 0, 99); // Keep last 100 messages

        // Broadcast to jamiya room
        io.to(`jamiya:${data.jamiyaId}`).emit('new_message', message);

        logger.debug(`Message sent in jamiya ${data.jamiyaId} by ${user.email}`);
      } catch (error) {
        socket.emit('error', { message: 'Failed to send message' });
        logger.error('Error sending message:', error);
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data: { jamiyaId: string }) => {
      socket.to(`jamiya:${data.jamiyaId}`).emit('user_typing', {
        userId: user._id,
        userName: user.name,
        jamiyaId: data.jamiyaId,
      });
    });

    socket.on('typing_stop', (data: { jamiyaId: string }) => {
      socket.to(`jamiya:${data.jamiyaId}`).emit('user_stopped_typing', {
        userId: user._id,
        jamiyaId: data.jamiyaId,
      });
    });

    // Handle payment status updates
    socket.on('subscribe_payment_updates', (paymentId: string) => {
      socket.join(`payment:${paymentId}`);
      logger.debug(`User ${user.email} subscribed to payment updates: ${paymentId}`);
    });

    socket.on('unsubscribe_payment_updates', (paymentId: string) => {
      socket.leave(`payment:${paymentId}`);
      logger.debug(`User ${user.email} unsubscribed from payment updates: ${paymentId}`);
    });

    // Handle notification acknowledgments
    socket.on('mark_notification_read', async (notificationId: string) => {
      try {
        const Notification = require('@/models/Notification').default;
        await Notification.findByIdAndUpdate(notificationId, { isRead: true });
        
        socket.emit('notification_marked_read', { notificationId });
        logger.debug(`Notification ${notificationId} marked as read by ${user.email}`);
      } catch (error) {
        socket.emit('error', { message: 'Failed to mark notification as read' });
        logger.error('Error marking notification as read:', error);
      }
    });

    // Handle user status updates
    socket.on('update_status', async (status: 'online' | 'away' | 'busy') => {
      try {
        // Update user status in cache
        await CacheManager.set(`user_status:${userId}`, status, 300); // 5 minutes TTL
        
        // Broadcast status to user's jamiyas
        const Subscription = require('@/models/Subscription').default;
        const subscriptions = await Subscription.find({ userId: user._id, status: 'active' });
        
        for (const subscription of subscriptions) {
          socket.to(`jamiya:${subscription.jamiyaId}`).emit('user_status_changed', {
            userId: user._id,
            userName: user.name,
            status,
          });
        }

        logger.debug(`User ${user.email} status updated to: ${status}`);
      } catch (error) {
        logger.error('Error updating user status:', error);
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`User disconnected: ${user.email}`, {
        userId: user._id.toString(),
        socketId: socket.id,
        reason,
      });

      // Remove from active connections
      const userConnections = activeConnections.get(userId);
      if (userConnections) {
        userConnections.delete(socket.id);
        if (userConnections.size === 0) {
          activeConnections.delete(userId);
          
          // Update user status to offline
          CacheManager.set(`user_status:${userId}`, 'offline', 300);
        }
      }
    });

    // Send initial data
    socket.emit('connected', {
      userId: user._id,
      userName: user.name,
      userRole: user.role,
      timestamp: new Date(),
    });
  });

  // Handle connection errors
  io.on('connect_error', (error) => {
    logger.error('Socket connection error:', error);
  });
};

// Socket notification service
export class SocketNotificationService {
  private io: SocketIOServer;

  constructor(io: SocketIOServer) {
    this.io = io;
  }

  // Send notification to specific user
  async sendToUser(userId: string, event: string, data: any) {
    this.io.to(`user:${userId}`).emit(event, data);
    logger.debug(`Socket notification sent to user ${userId}: ${event}`);
  }

  // Send notification to all users in a jamiya
  async sendToJamiya(jamiyaId: string, event: string, data: any) {
    this.io.to(`jamiya:${jamiyaId}`).emit(event, data);
    logger.debug(`Socket notification sent to jamiya ${jamiyaId}: ${event}`);
  }

  // Send notification to users with specific role
  async sendToRole(role: string, event: string, data: any) {
    this.io.to(`role:${role}`).emit(event, data);
    logger.debug(`Socket notification sent to role ${role}: ${event}`);
  }

  // Send payment update
  async sendPaymentUpdate(paymentId: string, status: string, data: any) {
    this.io.to(`payment:${paymentId}`).emit('payment_status_update', {
      paymentId,
      status,
      ...data,
    });
    logger.debug(`Payment update sent for payment ${paymentId}: ${status}`);
  }

  // Send jamiya update
  async sendJamiyaUpdate(jamiyaId: string, updateType: string, data: any) {
    this.io.to(`jamiya:${jamiyaId}`).emit('jamiya_update', {
      jamiyaId,
      updateType,
      ...data,
      timestamp: new Date(),
    });
    logger.debug(`Jamiya update sent for jamiya ${jamiyaId}: ${updateType}`);
  }

  // Send system announcement
  async sendSystemAnnouncement(message: string, priority: 'low' | 'normal' | 'high' = 'normal') {
    this.io.emit('system_announcement', {
      message,
      priority,
      timestamp: new Date(),
    });
    logger.info(`System announcement sent: ${message}`);
  }

  // Get active users count
  getActiveUsersCount(): number {
    return activeConnections.size;
  }

  // Get active connections for user
  getUserConnections(userId: string): string[] {
    const connections = activeConnections.get(userId);
    return connections ? Array.from(connections) : [];
  }

  // Check if user is online
  isUserOnline(userId: string): boolean {
    return activeConnections.has(userId);
  }

  // Get online users in jamiya
  async getOnlineUsersInJamiya(jamiyaId: string): Promise<string[]> {
    try {
      const sockets = await this.io.in(`jamiya:${jamiyaId}`).fetchSockets();
      return sockets.map(socket => (socket as any).user._id.toString());
    } catch (error) {
      logger.error('Error getting online users in jamiya:', error);
      return [];
    }
  }
}

// Export socket notification helpers
export const createSocketNotificationService = (io: SocketIOServer) => {
  return new SocketNotificationService(io);
};

export default {
  setupSocketHandlers,
  SocketNotificationService,
  createSocketNotificationService,
};
