{"logs": [{"outputFile": "com.aljameia.app-mergeDebugResources-104:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f1a3dac0af0a85f165dc53a6762b4a81\\transformed\\play-services-basement-18.2.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6325", "endColumns": "163", "endOffsets": "6484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b3e74a7425b14daa5ccd7c3c1ea8df2a\\transformed\\browser-1.4.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,274,388", "endColumns": "106,111,113,107", "endOffsets": "157,269,383,491"}, "to": {"startLines": "74,114,115,116", "startColumns": "4,4,4,4", "startOffsets": "7728,12194,12306,12420", "endColumns": "106,111,113,107", "endOffsets": "7830,12301,12415,12523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\21cc0a6be925d3c2a0c747bb469e8733\\transformed\\appcompat-1.6.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2454,2567,2677,2794,2961,19493", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2449,2562,2672,2789,2956,3067,19568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5081b8eacd451e5b41f2b566f41c579e\\transformed\\core-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "42,43,44,45,46,47,48,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3991,4094,4197,4299,4405,4503,4603,19736", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "4089,4192,4294,4400,4498,4598,4706,19832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\cdc55fe2502a063bcee343c082439e6f\\transformed\\navigation-ui-2.7.6\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,119", "endOffsets": "167,287"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "18597,18714", "endColumns": "116,119", "endOffsets": "18709,18829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\85fae80ff7869a8da6251422548fb462\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,988,1054,1144,1237,1314,1395,1463", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,983,1049,1139,1232,1309,1390,1458,1578"}, "to": {"startLines": "52,53,106,108,110,128,129,188,189,190,191,193,194,197,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5010,5109,11510,11694,11862,13907,13989,18834,18921,19007,19073,19223,19313,19573,19940,20021,20089", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "5104,5194,11599,11786,11943,13984,14080,18916,19002,19068,19134,19308,19401,19645,20016,20084,20204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1c0b3a48f4d4c74d31971bddddc3d282\\transformed\\play-services-base-18.1.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5283,5394,5552,5686,5803,5974,6110,6220,6489,6669,6783,6947,7080,7228,7380,7446,7518", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "5389,5547,5681,5798,5969,6105,6215,6320,6664,6778,6942,7075,7223,7375,7441,7513,7605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\523b9fad2b83d89c2b74860689d8fb91\\transformed\\biometric-1.1.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,263,385,513,645,790,922,1070,1166,1306,1445", "endColumns": "117,89,121,127,131,144,131,147,95,139,138,130", "endOffsets": "168,258,380,508,640,785,917,1065,1161,1301,1440,1571"}, "to": {"startLines": "73,107,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7610,11604,12528,12650,12778,12910,13055,13187,13335,13431,13571,13710", "endColumns": "117,89,121,127,131,144,131,147,95,139,138,130", "endOffsets": "7723,11689,12645,12773,12905,13050,13182,13330,13426,13566,13705,13836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d2a56f6da0e2e4ab4da1b326f38c566a\\transformed\\material-1.11.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1070,1166,1232,1293,1398,1462,1534,1592,1666,1728,1782,1895,1955,2016,2075,2153,2277,2358,2443,2579,2660,2743,2874,2957,3043,3105,3159,3225,3302,3381,3469,3552,3621,3697,3778,3846,3950,4041,4119,4212,4309,4383,4462,4560,4620,4708,4774,4862,4950,5012,5080,5143,5209,5314,5420,5515,5620,5686,5744", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,130,82,85,61,53,65,76,78,87,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83", "endOffsets": "257,346,434,516,611,700,802,912,999,1065,1161,1227,1288,1393,1457,1529,1587,1661,1723,1777,1890,1950,2011,2070,2148,2272,2353,2438,2574,2655,2738,2869,2952,3038,3100,3154,3220,3297,3376,3464,3547,3616,3692,3773,3841,3945,4036,4114,4207,4304,4378,4457,4555,4615,4703,4769,4857,4945,5007,5075,5138,5204,5309,5415,5510,5615,5681,5739,5823"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3548,3637,3725,3807,3902,4711,4813,4923,11948,12098,13841,14085,14146,14251,14315,14387,14445,14519,14581,14635,14748,14808,14869,14928,15006,15130,15211,15296,15432,15513,15596,15727,15810,15896,15958,16012,16078,16155,16234,16322,16405,16474,16550,16631,16699,16803,16894,16972,17065,17162,17236,17315,17413,17473,17561,17627,17715,17803,17865,17933,17996,18062,18167,18273,18368,18473,18539,19139", "endLines": "5,37,38,39,40,41,49,50,51,111,113,127,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,192", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,130,82,85,61,53,65,76,78,87,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83", "endOffsets": "307,3632,3720,3802,3897,3986,4808,4918,5005,12009,12189,13902,14141,14246,14310,14382,14440,14514,14576,14630,14743,14803,14864,14923,15001,15125,15206,15291,15427,15508,15591,15722,15805,15891,15953,16007,16073,16150,16229,16317,16400,16469,16545,16626,16694,16798,16889,16967,17060,17157,17231,17310,17408,17468,17556,17622,17710,17798,17860,17928,17991,18057,18162,18268,18363,18468,18534,18592,19218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c514f303830aa824ccabc1313adde6e2\\transformed\\material3-1.1.2\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,295,407,531,615,706,815,955,1072,1221,1301,1405,1499,1594,1702,1822,1931,2073,2212,2342,2503,2629,2777,2931,3057,3152,3243,3355,3475,3575,3691,3796,3937,4081,4187,4290,4361,4445,4532,4618,4721,4797,4878,4975,5080,5171,5270,5353,5460,5555,5655,5786,5862,5962", "endColumns": "124,114,111,123,83,90,108,139,116,148,79,103,93,94,107,119,108,141,138,129,160,125,147,153,125,94,90,111,119,99,115,104,140,143,105,102,70,83,86,85,102,75,80,96,104,90,98,82,106,94,99,130,75,99,89", "endOffsets": "175,290,402,526,610,701,810,950,1067,1216,1296,1400,1494,1589,1697,1817,1926,2068,2207,2337,2498,2624,2772,2926,3052,3147,3238,3350,3470,3570,3686,3791,3932,4076,4182,4285,4356,4440,4527,4613,4716,4792,4873,4970,5075,5166,5265,5348,5455,5550,5650,5781,5857,5957,6047"}, "to": {"startLines": "33,34,35,36,54,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,109,112,195,198,200,204,205,206,207,208,209,210,211,212,213,214,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3072,3197,3312,3424,5199,7835,7926,8035,8175,8292,8441,8521,8625,8719,8814,8922,9042,9151,9293,9432,9562,9723,9849,9997,10151,10277,10372,10463,10575,10695,10795,10911,11016,11157,11301,11407,11791,12014,19406,19650,19837,20209,20285,20366,20463,20568,20659,20758,20841,20948,21043,21143,21274,21350,21450", "endColumns": "124,114,111,123,83,90,108,139,116,148,79,103,93,94,107,119,108,141,138,129,160,125,147,153,125,94,90,111,119,99,115,104,140,143,105,102,70,83,86,85,102,75,80,96,104,90,98,82,106,94,99,130,75,99,89", "endOffsets": "3192,3307,3419,3543,5278,7921,8030,8170,8287,8436,8516,8620,8714,8809,8917,9037,9146,9288,9427,9557,9718,9844,9992,10146,10272,10367,10458,10570,10690,10790,10906,11011,11152,11296,11402,11505,11857,12093,19488,19731,19935,20280,20361,20458,20563,20654,20753,20836,20943,21038,21138,21269,21345,21445,21535"}}]}]}