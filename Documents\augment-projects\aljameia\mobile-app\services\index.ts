// Export all services
export { httpClient, tokenManager, userManager, networkUtils, STORAGE_KEYS } from './api';
export type { ApiResponse, PaginatedResponse } from './api';
export { ApiError } from './api';

export { authService } from './authService';
export type { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  User, 
  ChangePasswordRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest 
} from './authService';

export { jamiyaService } from './jamiyaService';
export type { 
  Jamiya, 
  JamiyaMember, 
  Subscription, 
  CreateJamiyaRequest, 
  JoinJamiyaRequest,
  JamiyaStats 
} from './jamiyaService';

export { paymentService } from './paymentService';
export type { 
  PaymentMethod, 
  Payment, 
  CreatePaymentRequest, 
  PaymentStats,
  PaymentSummary 
} from './paymentService';

export { reportService } from './reportService';
export type { 
  DashboardStats, 
  PerformanceReport, 
  ShareDistribution, 
  FutureProjection,
  InvestmentCalculation,
  ReportFilters 
} from './reportService';

// Service instances for easy access
export const services = {
  auth: authService,
  jamiya: jamiyaService,
  payment: paymentService,
  report: reportService,
};

// Utility functions
export const formatCurrency = (amount: number, currency: string = 'SAR'): string => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-SA');
};

export const formatDateTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString('ar-SA');
};

export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'active':
    case 'completed':
    case 'success':
      return '#10B981';
    case 'pending':
    case 'processing':
      return '#F59E0B';
    case 'failed':
    case 'error':
    case 'overdue':
      return '#EF4444';
    case 'cancelled':
    case 'suspended':
      return '#6B7280';
    default:
      return '#6B7280';
  }
};

export const getStatusText = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'active':
      return 'نشط';
    case 'pending':
      return 'معلق';
    case 'processing':
      return 'قيد المعالجة';
    case 'completed':
      return 'مكتمل';
    case 'failed':
      return 'فشل';
    case 'cancelled':
      return 'ملغي';
    case 'suspended':
      return 'معلق';
    case 'overdue':
      return 'متأخر';
    case 'success':
      return 'نجح';
    case 'error':
      return 'خطأ';
    default:
      return status;
  }
};

// Error handling utilities
export const handleApiError = (error: any): string => {
  if (error instanceof ApiError) {
    return error.message;
  }
  
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  return 'حدث خطأ غير متوقع';
};

// Validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^(\+966|0)?[5-9]\d{8}$/;
  return phoneRegex.test(phone);
};

export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
  }
  
  if (!/\d/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Storage utilities
export const clearAllData = async (): Promise<void> => {
  try {
    await tokenManager.clearTokens();
    await userManager.clearUserData();
  } catch (error) {
    console.error('Error clearing data:', error);
  }
};

// Network utilities
export const isOnline = async (): Promise<boolean> => {
  try {
    return await networkUtils.checkConnection();
  } catch {
    return false;
  }
};

// Default export
export default services;
