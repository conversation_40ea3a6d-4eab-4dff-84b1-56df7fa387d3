import React, { forwardRef, ButtonHTMLAttributes } from 'react';
import { cn } from '@/utils/cn';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'ghost' | 'link';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  children: React.ReactNode;
}

const buttonVariants = {
  primary: 'bg-primary-700 hover:bg-primary-800 focus:ring-primary-500 text-white shadow-sm',
  secondary: 'bg-white hover:bg-gray-50 focus:ring-primary-500 text-gray-900 border border-gray-300 shadow-sm',
  success: 'bg-green-700 hover:bg-green-800 focus:ring-green-500 text-white shadow-sm',
  danger: 'bg-red-700 hover:bg-red-800 focus:ring-red-500 text-white shadow-sm',
  warning: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 text-white shadow-sm',
  info: 'bg-blue-700 hover:bg-blue-800 focus:ring-blue-500 text-white shadow-sm',
  ghost: 'hover:bg-gray-100 focus:ring-primary-500 text-gray-700',
  link: 'text-primary-700 hover:text-primary-800 focus:ring-primary-500 underline-offset-4 hover:underline',
};

const buttonSizes = {
  xs: 'px-2.5 py-1.5 text-xs',
  sm: 'px-3 py-2 text-sm',
  md: 'px-4 py-2 text-sm',
  lg: 'px-4 py-2 text-base',
  xl: 'px-6 py-3 text-base',
};

const iconSizes = {
  xs: 'h-3 w-3',
  sm: 'h-4 w-4',
  md: 'h-4 w-4',
  lg: 'h-5 w-5',
  xl: 'h-5 w-5',
};

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = 'primary',
      size = 'md',
      loading = false,
      fullWidth = false,
      leftIcon,
      rightIcon,
      disabled,
      children,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || loading;

    return (
      <button
        className={cn(
          // Base styles
          'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed',
          
          // Variant styles
          buttonVariants[variant],
          
          // Size styles
          buttonSizes[size],
          
          // Full width
          fullWidth && 'w-full',
          
          // Loading state
          loading && 'cursor-wait',
          
          // Disabled state
          isDisabled && 'pointer-events-none',
          
          // Dark mode adjustments
          variant === 'secondary' && 'dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700',
          variant === 'ghost' && 'dark:text-gray-300 dark:hover:bg-gray-800',
          
          className
        )}
        disabled={isDisabled}
        ref={ref}
        {...props}
      >
        {/* Left Icon */}
        {leftIcon && !loading && (
          <span className={cn('flex-shrink-0', iconSizes[size], children && 'me-2')}>
            {leftIcon}
          </span>
        )}

        {/* Loading Spinner */}
        {loading && (
          <svg
            className={cn(
              'animate-spin',
              size === 'xs' || size === 'sm' ? 'h-4 w-4' : 'h-5 w-5',
              children && 'me-2'
            )}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        )}

        {/* Button Text */}
        {children && (
          <span className={loading ? 'opacity-70' : ''}>
            {children}
          </span>
        )}

        {/* Right Icon */}
        {rightIcon && !loading && (
          <span className={cn('flex-shrink-0', iconSizes[size], children && 'ms-2')}>
            {rightIcon}
          </span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

// Button Group Component
export interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: ButtonProps['size'];
  variant?: ButtonProps['variant'];
}

export function ButtonGroup({
  children,
  className,
  orientation = 'horizontal',
  size,
  variant,
}: ButtonGroupProps) {
  return (
    <div
      className={cn(
        'inline-flex',
        orientation === 'horizontal' ? 'flex-row' : 'flex-col',
        className
      )}
      role="group"
    >
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child) && child.type === Button) {
          const isFirst = index === 0;
          const isLast = index === React.Children.count(children) - 1;
          
          return React.cloneElement(child as React.ReactElement<ButtonProps>, {
            size: child.props.size || size,
            variant: child.props.variant || variant,
            className: cn(
              child.props.className,
              orientation === 'horizontal' && [
                !isFirst && !isLast && 'rounded-none border-x-0',
                isFirst && 'rounded-e-none border-e-0',
                isLast && 'rounded-s-none border-s-0',
              ],
              orientation === 'vertical' && [
                !isFirst && !isLast && 'rounded-none border-y-0',
                isFirst && 'rounded-b-none border-b-0',
                isLast && 'rounded-t-none border-t-0',
              ]
            ),
          });
        }
        return child;
      })}
    </div>
  );
}

// Icon Button Component
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon' | 'children'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, className, size = 'md', ...props }, ref) => {
    return (
      <Button
        ref={ref}
        size={size}
        className={cn(
          'p-2',
          size === 'xs' && 'p-1',
          size === 'sm' && 'p-1.5',
          size === 'lg' && 'p-2.5',
          size === 'xl' && 'p-3',
          className
        )}
        {...props}
      >
        <span className={iconSizes[size]}>
          {icon}
        </span>
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

// Floating Action Button Component
export interface FABProps extends Omit<ButtonProps, 'variant' | 'size'> {
  size?: 'md' | 'lg';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

export const FloatingActionButton = forwardRef<HTMLButtonElement, FABProps>(
  ({ className, size = 'lg', position = 'bottom-right', ...props }, ref) => {
    const positionClasses = {
      'bottom-right': 'fixed bottom-6 right-6 rtl:right-auto rtl:left-6',
      'bottom-left': 'fixed bottom-6 left-6 rtl:left-auto rtl:right-6',
      'top-right': 'fixed top-6 right-6 rtl:right-auto rtl:left-6',
      'top-left': 'fixed top-6 left-6 rtl:left-auto rtl:right-6',
    };

    return (
      <Button
        ref={ref}
        variant="primary"
        size={size}
        className={cn(
          'rounded-full shadow-lg hover:shadow-xl z-50',
          size === 'md' && 'h-12 w-12',
          size === 'lg' && 'h-14 w-14',
          positionClasses[position],
          className
        )}
        {...props}
      />
    );
  }
);

FloatingActionButton.displayName = 'FloatingActionButton';
